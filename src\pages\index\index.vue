<template>
  <div>
    <view class="index-container" v-if="tokenInfo.accessToken && tokenInfo.isDriver">
      <div class="index-content">
        <div v-show="tabBarValue === 'refuel-home'">
          <page-home :showMap="tabBarValue === 'refuel-home'" v-if="tabBarCache.includes('refuel-home')"></page-home>
        </div>
        <div v-show="tabBarValue === 'navigation'">
          <page-navigation :showMap="tabBarValue === 'navigation'" v-if="tabBarCache.includes('navigation')"></page-navigation>
        </div>
        <div v-show="tabBarValue === 'refuel-oil'">
          <page-refuel-oil :showMap="tabBarValue === 'refuel-oil'" v-if="tabBarCache.includes('refuel-oil')"></page-refuel-oil>
        </div>
        <div v-show="tabBarValue === 'order'">
          <page-order v-if="tabBarCache.includes('order')"></page-order>
        </div>
        <div v-show="tabBarValue === 'mine'">
          <mine v-if="tabBarCache.includes('mine')"></mine>
        </div>
      </div>
      <div class="index-tabbar">
        <u-tabbar
          :value="tabBarValue"
          @change="tabbarChange($event)"
          :fixed="true"
          :placeholder="true"
          :safeAreaInsetBottom="true"
          inactiveColor="#767678"
          activeColor="#FA1919"
        >
          <u-tabbar-item :name="item.page" :text="item.name" v-for="(item, i) in tabBarList" :key="i">
            <image class="tab-item-slot-icon" slot="inactive-icon" :src="item.icon"></image>
            <image class="tab-item-slot-icon" slot="active-icon" :src="item.iconActive"></image>
          </u-tabbar-item>
        </u-tabbar>
      </div>
    </view>
    <page-login v-if="!tokenInfo.accessToken && query.token" :query-params="query"></page-login>

    <!-- TODO 测试 -->
    <!-- <div class="ceshi" @click="onDebug">测试</div> -->
  </div>
</template>

<script>
import { mapState } from 'vuex';
import pageHome from '@/components/page-home/page-home.vue';
import pageRefuelOil from '@/components/page-refuel-oil/page-refuel-oil.vue';
import pageNavigation from '@/components/page-navigation/page-navigation.vue';
import pageOrder from '@/components/page-order/page-order.vue';
import mine from '@/pages/mine/mine.vue';

import icon_oil from '@/static/images/tab/oil.png';
import icon_oil_active from '@/static/images/tab/oil-checked.png';
import icon_navigation from '@/static/images/tab/navigation.png';
import icon_navigation_active from '@/static/images/tab/navigation-checked.png';
import icon_order from '@/static/images/tab/order.png';
import icon_order_active from '@/static/images/tab/order-checked.png';
import icon_mine from '@/static/images/tab/mine.png';
import icon_mine_active from '@/static/images/tab/mine-checked.png';
import PageLogin from '@/pages/login/login.vue';

export default {
  components: {
    PageLogin,
    pageHome,
    pageRefuelOil,
    pageNavigation,
    pageOrder,
    mine,
  },
  data() {
    return {
      query: {},
      tabbarIndex1: 'refuel-oil',
      loadPageLis1: ['refuel-oil'],
      tabbarList1: [
        {
          page: 'refuel-home',
          name: '首页',
          icon: icon_oil,
          iconActive: icon_oil_active,
        },
        {
          page: 'navigation',
          name: '导航',
          icon: icon_navigation,
          iconActive: icon_navigation_active,
        },
        {
          page: 'refuel-oil',
          name: '加油',
          icon: icon_oil,
          iconActive: icon_oil_active,
        },
        {
          page: 'order',
          name: '订单',
          icon: icon_order,
          iconActive: icon_order_active,
        },
        {
          page: 'mine',
          name: '我的',
          icon: icon_mine,
          iconActive: icon_mine_active,
        },
      ],
      debugList: [
        {
          label: 'demo页',
          value: 'demo',
        },
      ],
    };
  },
  computed: {
    ...mapState({
      tabBarListStore: state => state?.tabBar?.tabBarList,
      tabBarValue: state => state?.tabBar?.tabBarValue,
      tabBarCache: state => state?.tabBar?.tabBarCache,
      companyInfo: state => state?.company?.companyInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
    tabBarList() {
      // BD卡/对外输出司机端
      if ([1, 2, 3].includes(this.tokenInfo.fleetType)) {
        return this.tabBarListStore.filter(item => item.page !== 'refuel-oil');
      }
      return this.tabBarListStore;
    },
  },
  watch: {
    // 监听企业改变
    'companyInfo.enterpriseNo': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.initPage();
        }
      },
      immediate: false,
    },
  },
  onShow() {
    console.log('index------show');
    this.$store.commit('setIndexPageStatus', true);
  },
  onHide() {
    console.log('index------hide');
    this.$store.commit('setIndexPageStatus', false);
  },
  async onLoad(queryParams) {
    const { query } = uni.getLaunchOptionsSync() || {};
    // const { query } = uni.getEnterOptionsSync();
    this.query = { ...query, ...queryParams };
  },
  methods: {
    // 调试
    onDebug({ value }) {
      uni.showModal({
        title: 'tokenInfo',
        content: JSON.stringify(this.tokenInfo),
        confirmText: '确认',
        showCancel: false,
      });
      // if (value === 'demo') {
      //   uni.$petro.route('/pages/demo/demo', { test: 1 });
      // }
    },
    // 初始化
    async initPage() {
      // this.$store.dispatch('getStationList');
      // this.$store.dispatch('getCarList');
      // 获取钱包信息
      this.$store.dispatch('getWalletInfo', { staffRole: 4, refresh: true });
    },

    // 切换底部导航栏的回调函数
    tabbarChange(val) {
      this.$store.commit('switchTab', val);
      // this.tabbarIndex = val;
      // // 记录已经加载过的页面
      // if (!this.loadPageList.includes(val)) {
      //   this.loadPageList.push(val);
      // }
    },

    // 关闭小程序
    async closeMriver() {
      // const tokenInfo = await uni.$petro.getTokenInfo();
      // 测试代码-test
      // uni.setClipboardData({
      //   data: JSON.stringify(tokenInfo),
      //   success: function () {
      //     console.log('success');
      //   },
      // });
      uni.$petro.Bridge.sys.closeMriver();
    },
  },
};
</script>

<style lang="scss" scoped>
.debug-div {
  position: absolute;
  left: 20rpx;
  top: 30vh;
  z-index: 999999;
}
.index-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .index-content {
    flex: 1;
    overflow: hidden;
    > div {
      height: 100%;
      overflow: auto;
    }
  }
}
.tab-item-slot-icon {
  width: 50rpx;
  height: 50rpx;
}

.ceshi {
  position: fixed;
  top: 35vh;
  left: 0vw;
  z-index: 99999;
  opacity: 0.6;
  color: #ff7575;
}
</style>
