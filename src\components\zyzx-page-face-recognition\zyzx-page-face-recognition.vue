<template>
  <div class="zyzx-page-face-recognition">
    <div class="container">
      <div class="content">
        <div class="content-header">
          <div class="content-header-title">
            <span>{{ userInfo.splitterName }}</span>
            <span>{{ userInfo.identityNo }}</span>
          </div>
          <div class="content-header-des">请识别本人人脸，保持正对收集，确保光线充足</div>
        </div>

        <img class="content-face" src="./images/bg-face.png" alt="" />

        <div class="content-example">
          <div class="content-example-cell" v-for="(v, i) in exampleList" :key="i">
            <img class="content-example-cell-img" :src="v.url" alt="" />
            <span class="content-example-cell-title">{{ v.title }}</span>
          </div>
        </div>
      </div>

      <div class="instruction">
        <p class="instruction-des">通过人脸识别对您的身份进行确认，以确保企业和个人财产安全。 </p>
        <p class="instruction-title">温馨提示:</p>
        <p class="instruction-des">账号下所有的昆仑e享卡共用一个密码，设置后将同步修改。</p>
      </div>
    </div>

    <div class="btns">
      <div class="btns-item">
        <button @click="handleSubmit">开始验证</button>
        <!-- <button :class="{ btnDisable: isLoading }" @click="handleSubmit" :loading="isLoading" :disabled="isLoading">开始验证</button> -->
      </div>
    </div>
    <petro-layout ref="layout" :petroKeyboard="true"></petro-layout>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'zyzx-page-face-recognition',
  components: {},
  data() {
    return {
      // 修改支付密码相关
      isReal: false, // 是否实名
      isLoading: false,
      authenticationInfo: null, // 人脸初始化认证结果
      identityNo: '', // 身份证号码

      exampleList: [
        {
          title: '正对手机',
          url: require('./images/icon-phone.png'),
        },
        {
          title: '光线充足',
          url: require('./images/icon-shine.png'),
        },
        {
          title: '放慢动作',
          url: require('./images/icon-time.png'),
        },
      ],
      onAppShow: null,
      onAppHide: null,
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      userInfo: state => state?.roles?.userInfo,
      companyInfo: state => state?.company?.companyInfo,
      accountInfo: state => state?.company?.accountInfo,
    }),
  },
  created() {},
  async mounted() {
    try {
      this.getUserInfo();
    } catch (err) {
      console.log(err);
    }
  },

  methods: {
    /**
     * @description: 开始验证
     * @return {*}
     */
    handleSubmit() {
      uni.$u.throttle(this.handleSdkGetMetaInfo, 500);
    },

    /**
     * @description: 获取用户信息
     * @return {*}
     */
    async getUserInfo() {
      try {
        const res = await uni.$petro.http('user.getBasicInfo.h5', {
          type: 1, // 1-不脱敏信息 2-脱敏信息
        });
        console.log('🚀 ~ getUserInfo ~ res:', res);
        this.identityNo = res?.data?.identityNo || '';
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 获取人脸识别元信息
     *
     * @returns 返回Promise对象，表示异步操作的结果
     */
    async handleSdkGetMetaInfo() {
      try {
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'getMetaInfo',
          data: {},
        });
        console.log('🚀 ~ handleSdkGetMetaInfo ~ res:', res);

        this.initAuthentication(res.data?.metaInfo);
      } catch (err) {
        console.error(err);
      }
    },

    /**
     * 初始化人脸认证
     *
     * @param metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
     * @param metaInfo.areaCode 认证地市编码，例如：320100
     * @param metaInfo.roleBusinessList 业务角色列表
     * @param metaInfo.roleBusinessList.orgCode 单位编号
     * @param metaInfo.roleBusinessList.staffNo 员工编号
     * @param metaInfo.roleBusinessList.businessNo 业务编号
     * @param metaInfo.roleBusinessList.userRole 角色编码:
     * - 1: 企业管理员
     * - 2: 业务联系人
     * - 3: 员工
     * - 4: 司机
     * - 5: 游客
     */
    async initAuthentication(metaInfo) {
      /**
       * metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
       * areaCode 认证地市编码 例如：320100
       * roleBusinessList 业务角色列表
       *  - orgCode 单位编号
       *  - staffNo 员工编号
       *  - businessNo 业务编号
       *  - userRole 角色编码:1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
       */
      let params = {
        metaInfo: metaInfo,
        areaCode: '510100',
        roleBusinessList: [
          {
            orgCode: this.companyInfo?.enterpriseNo,
            staffNo: this.userInfo?.staffNo,
            businessNo: this.companyInfo?.businessNo,
            userRole: uni.$petro.Enum?.MEMBER_ROLES_CODE[this.role?.code], // 车队业务管理员-2 司机-4
            enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo || this.accountInfo?.parentMainAccountNo,
          },
        ],
      };
      console.log('🚀 ~ initAuthentication ~ params:', params);
      try {
        const res = await uni.$petro.http('user.memberInfo.initRealPersonIdentify.h5', params);
        console.log('🚀 ~ initAuthentication ~ res:', JSON.stringify(res));

        if (!res?.data) return uni.showToast({ content: res.message || '初始化失败' });

        this.authenticationInfo = res.data;
        this.handleSdkVerify();
      } catch (err) {
        console.error(err);
      }
    },

    /**
     * 人脸识别验证
     *
     * @returns Promise<void>
     */
    async handleSdkVerify() {
      try {
        // 获取人脸识别凭证
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'verify',
          data: {
            certifyId: this.authenticationInfo?.certifyId,
          },
        });
        console.log('🚀 ~ handleSdkVerify ~ res:', JSON.stringify(res));

        this.realPersonAuthentication();
      } catch (err) {
        console.error(err);
      }
    },

    /**
     * 查询实人认证结果
     *
     * @returns 返回Promise对象，表示异步操作的结果
     */
    async realPersonAuthentication() {
      try {
        /**
         * type 实人认证场景： 1—业务资料审核实人; 2—管理员激活企业; 3—司机首次注册; 4—扫一扫实人;
         * verifyNo 身份认证唯一标识(初始化实人认证接口返回)；
         * certifyId 实人认证三方系统的标识(初始化实人认证接口返回)。
         * roleBusinessList 业务角色列表
         *  - orgCode 单位编号
         *  - staffNo 员工编号
         *  - businessNo 业务编号
         *  - userRole 角色编码: 1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
         */
        const res = await uni.$petro.http('user.memberInfo.realPersonIdentify.h5', {
          type: '1',
          verifyNo: this.authenticationInfo?.verifyNo,
          certifyId: this.authenticationInfo?.certifyId,
          roleBusinessList: this.authenticationInfo?.roleBusinessList,
        });
        console.log('🚀 ~ realPersonAuthentication ~ res:', JSON.stringify(res));

        if (!res?.success || !res?.data?.authInfo) return uni.showToast({ content: res.message || '实人认证失败' });
        // 调用账户 SDK 重置支付密码
        this.handleSdkResetPassword();
      } catch (err) {
        console.error(err);
      } finally {
        // this.isLoading = false;
      }
    },

    /**
     * @description: 调用账户 SDK 重置支付密码
     * @return {*}
     */
    async handleSdkResetPassword() {
      uni.$petro.showLoading();
      try {
        await uni.$petro.AccountPlugin?.securityPluginInstance
          ?.resetPassword(this.identityNo, this.authenticationInfo?.verifyNo, uni.$petro.AccountPlugin?.ref, pwd => {
            uni.$petro.hideLoading();
            console.log('>>>>>>>>on keyboard:', pwd);
          })
          .then(res => {
            uni.$petro.hideLoading();
            console.log('reset password:', res);
            if (res?.code === '0') return this.resetPasswordSuccess();
            // 不抛错-键盘取消:P_SDK07_200001
            if (res?.code.indexOf('P_SDK07_200001') === -1) {
              uni.showModal({
                title: res?.msg || '异常',
                confirmText: '确认',
                showCancel: false,
              });
            }
          })
          .catch(error => {
            uni.$petro.hideLoading();
            console.error('reset password:', error);
            // 不抛错-键盘取消:P_SDK07_200001
            if (error?.code.indexOf('P_SDK07_200001') === -1) {
              uni.showModal({
                title: error?.msg || '异常',
                confirmText: '确认',
                showCancel: false,
              });
            }
          });
      } catch (err) {
        console.error(err);
        uni.$petro.hideLoading();
      } finally {
        uni.$petro.hideLoading();
      }
    },

    resetPasswordSuccess() {
      uni.$petro.hideLoading();
      uni.showToast({ title: '修改密码成功' });
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    },
  },
};
</script>

<style scoped lang="scss">
.zyzx-page-face-recognition {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .container {
    .content {
      width: 90%;
      height: 800rpx;
      margin: 0 auto;
      // border: 1px solid red;
      padding: 48rpx 60rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #d9d9d9;

      &-header {
        &-title {
          font-weight: bold;
          font-size: 36rpx;
          color: #333333;
          text-align: center;
          display: flex;
          flex-direction: row;
          justify-content: space-around;
        }

        &-des {
          margin-top: 16rpx;
          font-weight: 500;
          font-size: 24rpx;
          color: #999999;
        }
      }

      &-face {
        width: 280rpx;
        height: 280rpx;
      }

      &-example {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        &-cell {
          display: flex;
          flex-direction: column;

          &-img {
            width: 96rpx;
            height: 96rpx;
          }
          &-title {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
          }
        }
      }
    }

    .instruction {
      padding: 16rpx 32rpx;
      box-sizing: border-box;
      &-title {
        font-weight: bold;
        font-size: 24rpx;
        color: #666666;
        line-height: 44rpx;
        margin-top: 30rpx;
      }
      &-des {
        font-weight: 500;
        font-size: 24rpx;
        color: #999999;
        line-height: 44rpx;
      }
    }
  }

  .btns {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    &-item {
      height: 148rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      box-sizing: content-box;
      padding-bottom: env(safe-area-inset-bottom);

      button {
        width: 654rpx;
        height: 92rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #fff;
        background-color: #fa1919;
        line-height: 92rpx;
        border-radius: 200rpx 200rpx 200rpx 200rpx;
        border: 2rpx solid #eeeeee;
      }
    }
  }

  .btnDisable {
    opacity: 0.5;
  }
}
</style>
