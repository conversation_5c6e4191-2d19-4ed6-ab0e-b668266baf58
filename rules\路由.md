---
description: UniApp 司机小程序路由配置规范
globs: *.vue,*.js,pages.json
alwaysApply: true
---

# UniApp 司机小程序路由配置规范

## 总体描述

本文档围绕UniApp司机小程序的路由管理展开，介绍了pages.json配置结构、页面跳转方式、路由参数传递、分包配置以及路由命名规则，为UniApp路由的设计和开发提供了详细的规范和示例。

### 应用范围

本规范适用于所有基于UniApp框架的司机小程序项目，确保路由的配置、跳转和命名符合UniApp规范。

### 使用要求

开发人员在进行UniApp路由开发时，需要按照本规范中的pages.json配置、页面跳转API和路由命名规则进行开发。在配置路由时，要合理设置页面路径、样式和元信息。在实现页面跳转时，要使用UniApp提供的导航API。在命名路由时，要遵循kebab-case路径命名规则。

## 规则1 pages.json 配置规范

### 完整配置结构

UniApp使用pages.json文件进行路由配置，定义页面路径、样式和全局配置。基于项目实际配置，提供完整的配置示例：

#### 项目实际配置示例

```json
{
  "globalStyle": {
    "backgroundTextStyle": "dark",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#f7f7fb",
    "navigationStyle": "custom",
    "pullRefresh": false,
    "defaultTitle": "",
    "allowsBounceVertical": "NO",
    "transparentTitle": "always",
    "titlePenetrate": "YES"
  },
  "easycom": {
    "^uni-(.*)": "@/components/uni-$1.vue",
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
    "^petro-(.*)": "@petro-soti/foudation-zyzx/components/petro-$1/petro-$1.vue",
    "^zyzx-(.*)": "@petro-soti/foudation-zyzx/components/zyzx-$1/zyzx-$1.vue"
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "",
        "allowsBounceVertical": "NO"
      }
    },
    {
      "path": "pages/verification/verification",
      "style": {
        "navigationBarTitleText": "实人认证"
      }
    },
    {
      "path": "pages/station-list/station-list",
      "style": {
        "navigationBarTitleText": "油站列表"
      }
    },
    {
      "path": "pages/car/car",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/car/pages/car-details-page",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/business-info/business-info",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/order-detail/order-detail",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/wallet/pages/wallet-unitcar-page",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/help/help",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/message/message",
      "style": {
        "navigationBarTitleText": "站内信"
      }
    },
    {
      "path": "pages/todo/todo",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/mine/pages/about-page",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/demo/demo",
      "style": {
        "mp-weixin": {},
        "mp-alipay": {
          "pullRefresh": false,
          "defaultTitle": "",
          "allowsBounceVertical": "NO",
          "transparentTitle": "always",
          "titleBarColor": "#FFFFFF",
          "titlePenetrate": "YES"
        }
      }
    }
  ]
}
```

### 配置项详细说明

#### globalStyle 全局样式配置

**核心配置项**

- **navigationStyle**: "custom" - 自定义导航栏，允许页面自定义标题栏
- **navigationBarBackgroundColor**: "#ffffff" - 导航栏背景色
- **navigationBarTextStyle**: "black" - 导航栏文字颜色
- **backgroundColor**: "#f7f7fb" - 页面背景色
- **backgroundTextStyle**: "dark" - 下拉loading的样式
- **allowsBounceVertical**: "NO" - 禁止垂直滚动超出边界回弹
- **transparentTitle**: "always" - 导航栏透明设置
- **titlePenetrate**: "YES" - 导航栏点击穿透

#### easycom 组件自动引入配置

**组件库自动引入规则**

- **uni-(.*)**: uni-ui组件库，如`<uni-list>`自动引入`@/components/uni-list.vue`
- **u-(.*)**: uView UI组件库，如`<u-button>`自动引入`uview-ui/components/u-button/u-button.vue`
- **petro-(.*)**: 中石油自研组件库，如`<petro-layout>`自动引入对应组件
- **zyzx-(.*)**: 中石油自研组件库，如`<zyzx-company-bar>`自动引入对应组件

#### pages 页面配置规范

**页面配置结构**

- **path**: 页面路径，相对于src目录，如"pages/index/index"
- **style**: 页面样式配置对象
  - **navigationBarTitleText**: 页面标题，空字符串表示使用自定义导航栏
  - **enablePullDownRefresh**: 是否启用下拉刷新
  - **allowsBounceVertical**: 页面级别的回弹设置
  - **mp-weixin/mp-alipay**: 平台特定配置

#### 平台特定配置

**多平台适配配置**

```json
{
  "path": "pages/demo/demo",
  "style": {
    "mp-weixin": {
      // 微信小程序特定配置
    },
    "mp-alipay": {
      // 支付宝小程序特定配置
      "pullRefresh": false,
      "defaultTitle": "",
      "allowsBounceVertical": "NO",
      "transparentTitle": "always",
      "titleBarColor": "#FFFFFF",
      "titlePenetrate": "YES"
    }
  }
}
```

## 规则2 页面跳转规范

### 导航API使用规范

UniApp提供了多种页面跳转API，需要根据不同场景选择合适的API，确保页面栈管理和用户体验的最优化。

#### 跳转API详细说明

**1. uni.navigateTo - 保留当前页面跳转**

```javascript
// 适用场景：普通页面跳转，需要返回上一页
methods: {
  // 跳转到订单详情
  goToOrderDetail(orderId) {
    uni.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`,
      success: () => {
        console.log('跳转成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        uni.showToast({ title: '页面跳转失败', icon: 'none' });
      }
    });
  },

  // 跳转到车辆详情
  goToCarDetail(carId) {
    uni.navigateTo({
      url: `/pages/car/pages/car-details-page?carId=${carId}`
    });
  },

  // 跳转到实人认证
  goToVerification() {
    uni.navigateTo({
      url: '/pages/verification/verification'
    });
  }
}
```

**2. uni.redirectTo - 替换当前页面**

```javascript
// 适用场景：登录成功后跳转，不需要返回当前页面
methods: {
  // 登录成功后跳转首页
  loginSuccess() {
    uni.redirectTo({
      url: '/pages/index/index'
    });
  },

  // 支付完成后跳转结果页
  paymentComplete(orderId) {
    uni.redirectTo({
      url: `/pages/pay-result/pay-result?orderId=${orderId}&status=success`
    });
  }
}
```

**3. uni.reLaunch - 重启应用**

```javascript
// 适用场景：重新启动应用，清空页面栈
methods: {
  // 退出登录，重启应用
  logout() {
    // 清理用户数据
    this.$store.dispatch('user/logout');

    // 重启应用到首页
    uni.reLaunch({
      url: '/pages/index/index'
    });
  },

  // 切换企业后重启
  switchCompany(companyId) {
    // 切换企业逻辑
    this.$store.dispatch('company/switchCompany', companyId);

    // 重启应用
    uni.reLaunch({
      url: '/pages/index/index'
    });
  }
}
```

**4. uni.switchTab - Tab页面切换**

```javascript
// 适用场景：切换到Tab页面
methods: {
  // 切换到首页Tab
  goToHome() {
    uni.switchTab({
      url: '/pages/index/index'
    });
  },

  // 切换到车辆Tab
  goToCar() {
    uni.switchTab({
      url: '/pages/car/car'
    });
  }
}
```

**5. uni.navigateBack - 返回上一页**

```javascript
// 适用场景：返回上一页或指定页面
methods: {
  // 返回上一页
  goBack() {
    uni.navigateBack({
      delta: 1
    });
  },

  // 返回多级页面
  goBackToList() {
    uni.navigateBack({
      delta: 2 // 返回到列表页面
    });
  },

  // 带确认的返回
  confirmGoBack() {
    uni.showModal({
      title: '确认返回',
      content: '当前页面有未保存的内容，确认返回吗？',
      success: (res) => {
        if (res.confirm) {
          uni.navigateBack();
        }
      }
    });
  }
}
```

### 参数传递规范

#### URL参数传递
```javascript
// 发送页面
methods: {
  goToDetail() {
    const params = {
      id: '123',
      type: 'order',
      status: 'pending'
    };

    // 构建URL参数
    const query = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    uni.navigateTo({
      url: `/pages/order-detail/order-detail?${query}`
    });
  }
}

// 接收页面
export default {
  data() {
    return {
      orderId: '',
      orderType: '',
      orderStatus: ''
    };
  },
  onLoad(options) {
    // 接收URL参数
    this.orderId = options.id || '';
    this.orderType = options.type || '';
    this.orderStatus = options.status || '';

    // 参数解码
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data));
        this.handleData(data);
      } catch (error) {
        console.error('参数解析失败:', error);
      }
    }
  }
};
```

#### 事件总线传递复杂数据
```javascript
// 发送页面
methods: {
  goToDetail() {
    const complexData = {
      userInfo: { name: '张三', age: 30 },
      orderList: [{ id: 1, name: '商品1' }]
    };

    // 使用事件总线传递复杂数据
    uni.$emit('transferData', complexData);

    uni.navigateTo({
      url: '/pages/order-detail/order-detail?dataKey=complexData'
    });
  }
}

// 接收页面
export default {
  data() {
    return {
      receivedData: null
    };
  },
  onLoad(options) {
    if (options.dataKey) {
      // 监听事件总线
      uni.$on('transferData', (data) => {
        this.receivedData = data;
        this.handleReceivedData(data);
      });
    }
  },
  beforeDestroy() {
    // 清理事件监听
    uni.$off('transferData');
  }
};
```

## 规则3 路由命名规范

### 页面路径命名
定义页面路径的命名规则，确保路径清晰、语义化。

#### 应用范围
适用于所有页面文件和目录的命名。

#### 使用要求
严格按照kebab-case命名规则，路径要体现页面功能和层级关系。

#### 命名规则

```text
# 页面路径命名规范
pages/
├── index/                    # 首页
│   └── index.vue
├── station-list/             # 加油站列表
│   └── station-list.vue
├── car/                      # 车辆管理
│   ├── car.vue              # 车辆列表
│   └── pages/
│       └── car-details-page.vue  # 车辆详情
├── order-detail/             # 订单详情
│   ├── order-detail.vue     # 订单详情
│   └── order-return-detail.vue  # 退单详情
└── wallet/                   # 钱包模块
    ├── wallet.vue           # 钱包首页
    └── pages/
        ├── bill-list-page.vue      # 账单列表
        ├── bill-details-page.vue   # 账单详情
        └── face-recognition-page.vue # 人脸识别
```

#### 命名约定
- **目录名**: kebab-case，体现功能模块
- **文件名**: kebab-case，与目录名保持一致或添加功能后缀
- **子页面**: 放在pages子目录中，使用-page后缀
- **功能页面**: 使用功能描述词，如detail、list、edit等

### 路由参数命名
```javascript
// 参数命名规范
const routeParams = {
  // 基础参数 - camelCase
  id: '123',
  userId: 'user123',
  orderId: 'order456',

  // 状态参数 - camelCase
  status: 'pending',
  type: 'refuel',
  mode: 'edit',

  // 布尔参数 - is/has前缀
  isEdit: 'true',
  hasPermission: 'false',

  // 复杂数据 - JSON字符串
  data: encodeURIComponent(JSON.stringify(complexObject))
};
```

### 页面生命周期规范
```javascript
export default {
  // 页面加载时触发，只触发一次
  onLoad(options) {
    console.log('页面加载:', options);
    this.initPage(options);
  },

  // 页面显示时触发，每次显示都会触发
  onShow() {
    console.log('页面显示');
    this.refreshData();
  },

  // 页面隐藏时触发
  onHide() {
    console.log('页面隐藏');
    this.pauseTimer();
  },

  // 页面卸载时触发
  onUnload() {
    console.log('页面卸载');
    this.cleanup();
  },

  // 下拉刷新时触发
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },

  // 上拉加载更多时触发
  onReachBottom() {
    console.log('上拉加载');
    this.loadMoreData();
  }
};
```

## 规则4 分包配置规范

### 分包策略

基于小程序包大小限制和加载性能考虑，合理配置分包结构。

#### 分包配置示例

```json
{
  "subPackages": [
    {
      "root": "pages/wallet",
      "name": "wallet",
      "pages": [
        {
          "path": "pages/bill-list-page",
          "style": {
            "navigationBarTitleText": "账单列表"
          }
        },
        {
          "path": "pages/wallet-unitcar-page",
          "style": {
            "navigationBarTitleText": "单位车辆钱包"
          }
        }
      ]
    },
    {
      "root": "pages/mine",
      "name": "mine",
      "pages": [
        {
          "path": "pages/about-page",
          "style": {
            "navigationBarTitleText": "关于我们"
          }
        },
        {
          "path": "pages/edit-password-page",
          "style": {
            "navigationBarTitleText": "修改密码"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["wallet"]
    }
  }
}
```

#### 分包原则

1. **按功能模块分包**：相关功能页面放在同一分包
2. **控制分包大小**：每个分包不超过2MB
3. **合理预加载**：在主包页面预加载常用分包
4. **独立性原则**：分包间避免相互依赖

### 分包跳转规范

```javascript
// 跳转到分包页面
methods: {
  // 跳转到钱包分包页面
  goToBillList() {
    uni.navigateTo({
      url: '/pages/wallet/pages/bill-list-page'
    });
  },

  // 跳转到个人中心分包页面
  goToAbout() {
    uni.navigateTo({
      url: '/pages/mine/pages/about-page'
    });
  }
}
```