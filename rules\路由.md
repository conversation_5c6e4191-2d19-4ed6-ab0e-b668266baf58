---
description: UniApp 司机小程序路由配置规范
globs: *.vue,*.js,pages.json
alwaysApply: true
---

### UniApp 路由规范

#### 总体描述

本文档围绕UniApp司机小程序的路由管理展开，介绍了pages.json配置结构、页面跳转方式、路由参数传递以及路由命名规则，为UniApp路由的设计和开发提供了详细的规范和示例。

#### 应用范围
本规范适用于所有基于UniApp框架的司机小程序项目，确保路由的配置、跳转和命名符合UniApp规范。

#### 使用要求
开发人员在进行UniApp路由开发时，需要按照本规范中的pages.json配置、页面跳转API和路由命名规则进行开发。在配置路由时，要合理设置页面路径、样式和元信息。在实现页面跳转时，要使用UniApp提供的导航API。在命名路由时，要遵循kebab-case路径命名规则。

## 规则1 pages.json 配置规范

### 基础配置结构
UniApp使用pages.json文件进行路由配置，定义页面路径、样式和全局配置。

#### 应用范围
适用于所有页面的路由配置，包括主包页面和分包页面。

#### 使用要求
严格按照UniApp官方规范配置pages.json，确保跨平台兼容性。

#### 配置示例

```json
{
  "globalStyle": {
    "backgroundTextStyle": "dark",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#f7f7fb",
    "navigationStyle": "custom",
    "pullRefresh": false,
    "defaultTitle": "",
    "allowsBounceVertical": "NO",
    "transparentTitle": "always",
    "titlePenetrate": "YES"
  },
  "easycom": {
    "^uni-(.*)": "@/components/uni-$1.vue",
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue",
    "^petro-(.*)": "@petro-soti/foudation-zyzx/components/petro-$1/petro-$1.vue",
    "^zyzx-(.*)": "@petro-soti/foudation-zyzx/components/zyzx-$1/zyzx-$1.vue"
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "allowsBounceVertical": "NO"
      }
    },
    {
      "path": "pages/station-list/station-list",
      "style": {
        "navigationBarTitleText": "油站列表",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/car/car",
      "style": {
        "navigationBarTitleText": "我的车辆"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/wallet",
      "pages": [
        {
          "path": "pages/bill-list-page",
          "style": {
            "navigationBarTitleText": "账单列表"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tab_home.png",
        "selectedIconPath": "static/tab_home_active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/car/car",
        "iconPath": "static/tab_car.png",
        "selectedIconPath": "static/tab_car_active.png",
        "text": "我的车辆"
      }
    ]
  }
}
```

### 配置项说明

#### globalStyle 全局样式
- **navigationStyle**: 导航栏样式，"custom"表示自定义导航栏
- **navigationBarBackgroundColor**: 导航栏背景色
- **backgroundColor**: 页面背景色
- **allowsBounceVertical**: 是否允许垂直滚动超出边界回弹

#### easycom 组件自动引入
- **uni-前缀**: uni-ui组件自动引入
- **u-前缀**: uView UI组件自动引入
- **petro-前缀**: 中石油自研组件自动引入
- **zyzx-前缀**: 中石油自研组件自动引入

#### pages 页面配置
- **path**: 页面路径，相对于src目录
- **style**: 页面样式配置
- **navigationBarTitleText**: 页面标题
- **enablePullDownRefresh**: 是否启用下拉刷新

## 规则2 页面跳转规范

### 导航API使用
UniApp提供了多种页面跳转API，需要根据不同场景选择合适的API。

#### 应用范围
适用于所有页面间的跳转操作，包括普通跳转、Tab切换、重定向等。

#### 使用要求
根据跳转场景选择合适的API，正确处理参数传递和页面栈管理。

#### 跳转API规范

```javascript
// 1. uni.navigateTo - 保留当前页面，跳转到应用内的某个页面
// 适用场景：普通页面跳转，需要返回上一页
methods: {
  goToDetail(id) {
    uni.navigateTo({
      url: `/pages/order-detail/order-detail?id=${id}`,
      success: () => {
        console.log('跳转成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
      }
    });
  }
}

// 2. uni.redirectTo - 关闭当前页面，跳转到应用内的某个页面
// 适用场景：登录成功后跳转，不需要返回当前页面
methods: {
  loginSuccess() {
    uni.redirectTo({
      url: '/pages/index/index'
    });
  }
}

// 3. uni.reLaunch - 关闭所有页面，打开到应用内的某个页面
// 适用场景：重新启动应用，清空页面栈
methods: {
  restart() {
    uni.reLaunch({
      url: '/pages/index/index'
    });
  }
}

// 4. uni.switchTab - 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
// 适用场景：切换到Tab页面
methods: {
  goToHome() {
    uni.switchTab({
      url: '/pages/index/index'
    });
  }
}

// 5. uni.navigateBack - 关闭当前页面，返回上一页面或多级页面
// 适用场景：返回上一页或指定页面
methods: {
  goBack() {
    uni.navigateBack({
      delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
    });
  }
}
```

### 参数传递规范

#### URL参数传递
```javascript
// 发送页面
methods: {
  goToDetail() {
    const params = {
      id: '123',
      type: 'order',
      status: 'pending'
    };

    // 构建URL参数
    const query = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');

    uni.navigateTo({
      url: `/pages/order-detail/order-detail?${query}`
    });
  }
}

// 接收页面
export default {
  data() {
    return {
      orderId: '',
      orderType: '',
      orderStatus: ''
    };
  },
  onLoad(options) {
    // 接收URL参数
    this.orderId = options.id || '';
    this.orderType = options.type || '';
    this.orderStatus = options.status || '';

    // 参数解码
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data));
        this.handleData(data);
      } catch (error) {
        console.error('参数解析失败:', error);
      }
    }
  }
};
```

#### 事件总线传递复杂数据
```javascript
// 发送页面
methods: {
  goToDetail() {
    const complexData = {
      userInfo: { name: '张三', age: 30 },
      orderList: [{ id: 1, name: '商品1' }]
    };

    // 使用事件总线传递复杂数据
    uni.$emit('transferData', complexData);

    uni.navigateTo({
      url: '/pages/order-detail/order-detail?dataKey=complexData'
    });
  }
}

// 接收页面
export default {
  data() {
    return {
      receivedData: null
    };
  },
  onLoad(options) {
    if (options.dataKey) {
      // 监听事件总线
      uni.$on('transferData', (data) => {
        this.receivedData = data;
        this.handleReceivedData(data);
      });
    }
  },
  beforeDestroy() {
    // 清理事件监听
    uni.$off('transferData');
  }
};
```

## 规则3 路由命名规范

### 页面路径命名
定义页面路径的命名规则，确保路径清晰、语义化。

#### 应用范围
适用于所有页面文件和目录的命名。

#### 使用要求
严格按照kebab-case命名规则，路径要体现页面功能和层级关系。

#### 命名规则

```text
# 页面路径命名规范
pages/
├── index/                    # 首页
│   └── index.vue
├── station-list/             # 加油站列表
│   └── station-list.vue
├── car/                      # 车辆管理
│   ├── car.vue              # 车辆列表
│   └── pages/
│       └── car-details-page.vue  # 车辆详情
├── order-detail/             # 订单详情
│   ├── order-detail.vue     # 订单详情
│   └── order-return-detail.vue  # 退单详情
└── wallet/                   # 钱包模块
    ├── wallet.vue           # 钱包首页
    └── pages/
        ├── bill-list-page.vue      # 账单列表
        ├── bill-details-page.vue   # 账单详情
        └── face-recognition-page.vue # 人脸识别
```

#### 命名约定
- **目录名**: kebab-case，体现功能模块
- **文件名**: kebab-case，与目录名保持一致或添加功能后缀
- **子页面**: 放在pages子目录中，使用-page后缀
- **功能页面**: 使用功能描述词，如detail、list、edit等

### 路由参数命名
```javascript
// 参数命名规范
const routeParams = {
  // 基础参数 - camelCase
  id: '123',
  userId: 'user123',
  orderId: 'order456',

  // 状态参数 - camelCase
  status: 'pending',
  type: 'refuel',
  mode: 'edit',

  // 布尔参数 - is/has前缀
  isEdit: 'true',
  hasPermission: 'false',

  // 复杂数据 - JSON字符串
  data: encodeURIComponent(JSON.stringify(complexObject))
};
```

### 页面生命周期规范
```javascript
export default {
  // 页面加载时触发，只触发一次
  onLoad(options) {
    console.log('页面加载:', options);
    this.initPage(options);
  },

  // 页面显示时触发，每次显示都会触发
  onShow() {
    console.log('页面显示');
    this.refreshData();
  },

  // 页面隐藏时触发
  onHide() {
    console.log('页面隐藏');
    this.pauseTimer();
  },

  // 页面卸载时触发
  onUnload() {
    console.log('页面卸载');
    this.cleanup();
  },

  // 下拉刷新时触发
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh();
    });
  },

  // 上拉加载更多时触发
  onReachBottom() {
    console.log('上拉加载');
    this.loadMoreData();
  }
};
```

#### 规则2 路由懒加载

所有路由组件都应使用动态导入实现懒加载：

```typescript
component: () => import('@/views/UserProfile.vue')
```

反例

```typescript
// /router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior() {
    // 滚动行为
    return { top: 0 }
  }
})

export default router
```



#### 规则3 路由命名规则

- 用大驼峰命名路由名称
- 保持路由路径使用 kebab-case

- 使用嵌套路由组织复杂页面

#### 