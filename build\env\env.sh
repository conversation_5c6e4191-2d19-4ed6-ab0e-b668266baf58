#!/bin/bash
## 此行定义不做改动
configLocalPath="../../src/config.index.local.js"

# 当前目录创建环境配置文件，命名规范 项目简称.平台名称.环境名称.local.js 例如：test.zfb.sit.local.js
# 配置内容为src目录下模板config.index.local.js内容配置
# 开放以下相应命令后当前目录执行 ./env.sh 命令使环境配置覆盖项目配置文件
# 编译平台命令 npm run build:mp-mpaas

## dev
# cp ./mpaas.dev.local.js $configLocalPath

## sit
cp ./mpaas.sit.local.js $configLocalPath

## 测试环境
#cp ./test.zfb.sit.local.js $configLocalPath
#cp ./test.wx.sit.local.js $configLocalPath
#cp ./test.mpaas.sit.local.js $configLocalPath

## 生产环境
#cp ./test.zfb.prd.local.js $configLocalPath
#cp ./test.wx.prd.local.js $configLocalPath
#cp ./test.mpaas.prd.local.js $configLocalPath
