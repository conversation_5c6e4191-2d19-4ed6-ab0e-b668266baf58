<template>
  <!-- 钱包看板 -->
  <div class="zyzx-page-wallet" :style="{ backgroundColor }">
    <div class="header-layout" :class="{ 'is-ide': isIDE }">
      <u-navbar title="车牌卡" :fixed="!isIDE" :autoBack="true" :placeholder="true" :bgColor="'#fff'" @rightClick="handleSelect">
        <div slot="right" style="color: #666666; font-size: 28rpx">设置</div>
      </u-navbar>
    </div>

    <zyzx-data-list ref="dataList" :showEmpty="false" @refreshPullDown="refreshPullDown" :is-refresh="cardPullLoad" :is-load="false">
      <!-- 资产信息展示 -->
      <div class="header" v-if="cardInfo">
        <div class="header-content">
          <!-- <img class="header-content-bg" src="./images/icon-wallet-bg.png" alt="" /> -->
          <div class="header-content-info">
            <div class="top">
              <div class="top-unitName ellipsis">{{ walletInfoData.enterpriseName || '' }}</div>
              <div class="top-name ellipsis">{{ desensitizedName(walletInfoData.userName) || '' }}</div>
            </div>
            <div class="middle">
              <div class="middle-left cell">
                <span class="title">总资产（元）</span>
                <span class="assets ellipsis">{{ accountAmount(walletInfoData.walletAccountList, 'availableAmount') || '' }}</span>
              </div>
              <!-- 司机角色展示冻结金额 -->
              <!-- <div class="middle-right cell">
                <span class="title"></span>
                <span class="assets ellipsis">{{ walletInfoData.licencePlate }}</span>
              </div> -->
              <!-- 管理端角色展示开票类型 -->
              <div class="middle-right manager">
                <p class="manager-invoiceType ellipsis"> {{ walletInfoData.accountPlaceName || '' }}</p>
              </div>
            </div>
            <div class="line"></div>
            <div class="end">
              <p class="end-leftk">
                {{ walletInfoData.licencePlate || '' }}
              </p>
              <p class="end-right ellipsis">
                <span style="margin-right: 10rpx">{{ isBdCard ? 'BD装备卡号' : '昆仑e享卡' }}</span>
                <span class="cardNo">
                  {{
                    isBdCard
                      ? walletInfoData.bdCardNo || ''
                      : accountAmount(walletInfoData.walletAccountList, 'cardNo') || walletInfoData.mainAccountNo || ''
                  }}</span
                >
                <img :src="require('./images/tabCard.png')" v-if="walletInfoData.bdCardNo" @click="tabCardNameTap" />
              </p>
            </div>
          </div>
        </div>

        <!-- 账户信息展示 -->
        <div class="header-assets" v-if="accountInfo">
          <div class="header-assets-list">
            <div class="header-assets-list-item">
              <span>冻结金额（元）：</span>
              <span>{{ accountAmount(walletInfoData.walletAccountList, 'frozenAmount') }}</span>
            </div>
            <div class="header-assets-list-item" @click="handleSeeMore(8)">
              <span>积分：</span>
              <span>{{ accountAmount(walletInfoData.loyaltyAccountList, 'availablePointAmount', 8) }}</span>
            </div>
            <div class="header-assets-list-item">
              <span>优惠金：</span>
              <span>{{ accountAmount(walletInfoData.loyaltyAccountList, 'availablePointAmount', 13) }}</span>
            </div>
            <!-- <div class="header-assets-list-item">
              <span></span>
              <span>{{ formatInvoice(walletInfoData.invoiceType || '1') }}</span>
            </div> -->
          </div>
          <div class="header-assets-nav"></div>
        </div>
      </div>
    </zyzx-data-list>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'zyzx-page-car-wallet',
  props: {
    cardInfo: {
      type: Boolean,
      default: true,
    },
    cardPullLoad: {
      type: Boolean,
      default: false,
    },
    accountInfo: {
      type: Boolean,
      default: true,
    },
    businessInfo: {
      type: Boolean,
      default: true,
    },
    backgroundColor: {
      type: String,
      default: '',
    },
    carWalletData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      isShowEye: false, // 是否显示眼睛
      moduleList: [
        { title: '冻结金额（元）', value: '0', id: 1 },
        { title: '优惠金', value: '0', id: 2 },
        { title: '积分', value: '0', id: 3 },
      ],
      walletInfoData: { walletAccount: { availableAmount: 0 } }, // 获取钱包信息
      isBdCard: false, // 是否显示bd卡
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      companyInfo: state => state?.company?.companyInfo,
      walletInfo: state => state.account.walletInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
    isIDE() {
      return my.isIDE;
    },
  },
  watch: {},
  created() {
    this.getWalletInfo();
  },
  mounted() {},
  methods: {
    /**
     * @description: 查看全部，跳转账单列表
     * @return {*}
     */
    handleSeeMore(type) {
      let pointAccountNo = '';
      if (type == 8) {
        pointAccountNo = this.walletInfoData.integralAccountNo;
      } else {
        pointAccountNo = this.walletInfoData.discountAccountNo;
      }
      let params = {
        mainAccountNo: this.walletInfoData.mainAccountNo,
        enterpriseAccountNo: this.walletInfoData.enterpriseAccountNo,
        account: pointAccountNo,
        accountType: type,
        type: this.walletInfoData.accountType,
      };
      uni.$petro.route(
        {
          url: '/pages/wallet/pages/bill-list-page',
          type: 'navigateTo',
          params,
        },
        true,
      );
    },
    tabCardNameTap() {
      this.isBdCard = !this.isBdCard;
    },
    /**
     * 对姓名进行脱敏处理
     *
     * @param originalName 原始姓名字符串
     * @returns 脱敏后的姓名字符串
     */
    desensitizedName(originalName) {
      if (!originalName) {
        return '';
      }
      const nameLength = originalName.length;
      if (nameLength === 1) {
        // 如果姓名只有一个字，则不脱敏
        return originalName;
      } else if (nameLength === 2) {
        // 如果姓名只有两个字，则脱敏最后一个字
        return originalName.charAt(0) + '*';
      } else {
        // 如果姓名超过两个字，则保留第一个和最后一个字，中间用*代替
        const firstChar = originalName.charAt(0);
        const lastChar = originalName.charAt(nameLength - 1);
        const middleStars = '*'.repeat(nameLength - 2);
        return firstChar + middleStars + lastChar;
      }
    },
    /**
     * @description: navBar 右侧点击事件
     * @return {*}
     */
    handleRightClick() {
      if (this.role.roleDescribe === 'manager') return;
      uni.$petro.route('/pages/wallet/pages/face-recognition-page');
    },

    /**
     * @description: 跳转路由
     * @return {*}
     * @param url
     */
    handleSelect() {
      this.$emit('viewRestrictions', {});
    },

    /**
     * @description: 格式化金额
     * @param {*} moneyStr
     * @return {*}
     */
    formatMoney(moneyStr) {
      // 如果money不是数字，返回原始字符串
      if (isNaN(moneyStr)) {
        return moneyStr;
      }

      // 使用Intl.NumberFormat进行格式化
      let formatter = new Intl.NumberFormat('zh-CN', {
        style: 'decimal',
        minimumFractionDigits: 2, // 至少显示两位小数
        maximumFractionDigits: 2, // 最多显示两位小数
      });

      // 格式化并返回结果
      return formatter.format(moneyStr);
    },

    /**
     * @description: 开票类型
     * @param {*} v
     * @return {*}
     */
    formatInvoice(v = '') {
      return uni.$petro.Enum?.INVOICE_TYPE[v] || '';
    },

    // 下拉刷新触发
    async refreshPullDown() {
      console.log('下拉刷新触发....');
      if (!this.role?.roleType) {
        this.$refs.dataList.stopRefresh();
        return;
      }
      this.$refs.dataList.loadStatus = 'loading';
      await this.$store.dispatch('getWalletInfo', {
        staffRole: this.role?.roleType,
        refresh: true,
      });
      this.$refs.dataList.stopRefresh();
    },
    async getWalletInfo() {
      console.log(this.carWalletData);
      console.log(this);

      try {
        const params = {
          businessNo: this.carWalletData?.businessNo || '',
          enterpriseNo: this.carWalletData?.enterpriseNo || '',
          enterpriseAccountNo: this.carWalletData?.enterpriseAccountNo || '',
          mainAccountNo: this.carWalletData?.mainAccountNo || '',
        };
        let showLoading = true;
        const { success, data } = await uni.$petro.http('account.user.queryAccountDetailInfo.h5', params, {
          showLoading: showLoading,
          mockResponse: {
            success: true,
            data: {
              enterpriseAccountNo: '************',
              mainAccountNo: '************',
              enterpriseNo: '****************',
              enterpriseStaffNo: '*****************',
              userId: '469',
              userName: '雷天伟',
              licencePlate: '川A99999',
              unitAlias: '长沙市岳麓区林雀子饭店',
              accountType: 3,
              businessNo: '****************',
              businessType: 10,
              invoiceType: null,
              accountStatus: 1,
              accountPlace: '1-A4301-C001',
              accountPlaceName: '湖南长沙销售分公司',
              remark: null,
              serialNo: null,
              memberName: '雷天伟',
              memberDocNo: '**************',
              enterpriseName: '长沙市岳麓区林雀子饭店',
              staffNo: '*****************',
              bdCardNo: '***********',
              walletAccountList: [
                {
                  cardNo: null,
                  accountNo: '************',
                  frozenAmount: *********.0,
                  availableAmount: ********.0,
                  accountStatus: null,
                  capitalType: 1,
                  accountType: 5,
                },
              ],
              loyaltyAccountList: [
                {
                  pointAccountNo: '************',
                  frozenPointAmount: 0.0,
                  availablePointAmount: 5.0,
                  accountStatus: 1,
                  accountType: 8,
                },
                {
                  pointAccountNo: '************',
                  frozenPointAmount: 0.0,
                  availablePointAmount: 8.0,
                  accountStatus: 1,
                  accountType: 13,
                },
              ],
            },
            message: '请求成功',
            errorCode: null,
          },
        });
        if (success) {
          // 获取积分编号
          data.integralAccountNo = data.loyaltyAccountList.find(item => item.accountType == 8).pointAccountNo || '';
          // 获取优惠金编号
          data.discountAccountNo = data.loyaltyAccountList.find(item => item.accountType == 13).pointAccountNo || '';
          this.walletInfoData = data;
          if (this.walletInfoData.bdCardNo) {
            this.isBdCard = true;
          } else {
            this.isBdCard = false;
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 根据账户类型获取账户金额
     *
     * @param item 账户数据数组
     * @param value 需要获取的金额类型，如'availableAmount'
     * @param key 账户类型，默认为5
     * @returns 返回指定账户类型的金额，如果未找到则返回0
     */
    accountAmount(item = [], value, key = 5) {
      const data = (item || []).find(it => key == it.accountType);
      if (data) {
        if (value == 'availableAmount') {
          return this.formatMoney(data[value]);
        } else {
          return data[value];
        }
      } else {
        return 0;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.zyzx-page-wallet {
  // padding: 20rpx 20rpx 40rpx;
  color: #333;
  font-size: 26rpx;
  background-color: #f0f1f5;
  box-sizing: border-box;

  .header-layout {
    &.is-ide {
      padding-right: calc(240rpx + 24rpx);
    }
  }

  .interval {
    margin-top: 20rpx;
  }

  .header {
    width: 100%;
    padding-top: 22rpx;
    box-sizing: border-box;
    background-color: #f0f1f5;

    &-content {
      width: 686rpx;
      height: 336rpx;
      padding: 10rpx 0;
      margin: 0 auto;
      overflow: hidden;
      box-sizing: border-box;
      background: url('./images/icon-wallet-bg2.png') no-repeat;
      background-size: cover;

      &-info {
        width: 100%;
        height: 326rpx;

        .top {
          display: flex;
          justify-content: space-between;
          font-weight: 400;
          padding-left: 32rpx;
          height: 80rpx;
          align-items: center;
          &-unitName {
            width: 80%;
            font-size: 32rpx;
            color: #ffffff;
            line-height: 80rpx;
          }
          &-setup {
            // width: 11%;
            height: 35rpx;
            line-height: 35rpx;
            text-align: center;
            background: #adc5ff;
          }
          &-name {
            width: 20%;
            font-size: 28rpx;
            color: #333333;
            // letter-spacing: 2px;
            line-height: 70rpx;
            text-align: center;
          }
        }
        .middle {
          margin-top: 10rpx;
          display: flex;
          flex-direction: row;
          padding: 0 32rpx;
          height: 122rpx;
          .cell {
            display: flex;
            flex-direction: column;
            text-align: left;

            .title {
              height: 44rpx;
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.6);
              line-height: 44rpx;
            }
            .assets {
              font-family: HarmonyOS Sans Condensed, HarmonyOS Sans Condensed;
              font-weight: 500;
              font-size: 48rpx;
            }
          }

          &-left {
            width: 60%;

            .assets {
              color: #ffffff;
            }
          }
          &-right {
            width: 40%;
            .assets {
              color: rgba(255, 255, 255, 0.75);
            }
          }
          .manager {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            &-invoiceType {
              line-height: 70rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.75);
              text-align: right;
            }
          }
        }
        .end {
          width: 100%;
          padding: 0 32rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 400;
          line-height: 60rpx;
          color: rgba(255, 255, 255, 0.75);
          box-sizing: border-box;

          &-leftz {
            font-size: 28rpx;
            width: 63%;
            .cardNo {
              margin-left: 10rpx;
            }
          }

          &-leftk {
            font-size: 28rpx;
            flex: 1;
          }
          &-right {
            width: 70%;
            font-size: 28rpx;
            text-align: right;
            img {
              width: 32rpx;
              height: 32rpx;
              margin-left: 10rpx;
              transform: translateY(5rpx);
            }
          }
        }
        .line {
          width: 622rpx;
          margin: 4rpx auto;
          border: 2rpx solid;
          border-image: linear-gradient(90deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
        }
      }
    }

    &-assets {
      width: 100%;
      // height: 130rpx;
      box-sizing: border-box;
      background: linear-gradient(90deg, #fceadb 0%, #eee2d6 100%);
      border-radius: 32rpx 32rpx 0rpx 0rpx;
      margin-top: -20rpx;
      &-list {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        height: 90rpx;
        line-height: 90rpx;
        padding: 0 32rpx;
        &-item {
          // flex: 1;
          text-align: center;
          font-weight: 500;
          font-size: 28rpx;
          color: #795853;
        }
      }

      &-nav {
        height: 30rpx;
        width: 100%;
        background: #fff;
        border-radius: 32rpx 32rpx 0rpx 0rpx;
      }
    }
  }

  // 资产信息样式
  .control {
    width: 100%;
    height: 150rpx;
    background-color: #fff;
    display: flex;
    justify-content: space-between;

    &-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 64rpx;
        height: 64rpx;
      }

      .title {
        font-size: 24rpx;
        color: #333333;
        margin-top: 8rpx;
      }
    }
  }
}
.ellipsis {
  /* 防止内容换行 */
  white-space: nowrap;
  /* 溢出内容隐藏 */
  overflow: hidden;
  /* 文本溢出时使用省略号 */
  text-overflow: ellipsis;
}
</style>
