<template>
  <div class="page-gift-result">
    <u-navbar :title="'支付完成'" :autoBack="true" :placeholder="true"></u-navbar>

    <view class="result" v-if="false">
      <!-- <img src="@/static/images/icon-success.png" class="result-icon" />
      <view class="result-amount"> <span>￥</span>{{ resultInfo.payAmount || 0 }} </view>
      <view class="result-item">
        <view class="result-item-title">支付渠道</view>
        <view class="result-item-value">{{ resultInfo.channel }}</view>
      </view>
      <view class="result-item">
        <view class="result-item-title">支付时间</view>
        <view class="result-item-value">{{ resultInfo.transTime }}</view>
      </view> -->
    </view>
    <div class="page-container">
      <div class="content">
        <img class="result-icon" src="@/static/images/icon-success.png" />
        <div class="value">
          <span class="unit" v-if="resultInfo.payType != 15">￥</span>
          <span class="money">{{ (resultInfo.payType != 15 ? resultInfo.payAmount : resultInfo.accumulatedPoint) || 0 }}</span>
          <span class="unit" v-if="resultInfo.payType == 15">积分</span>
        </div>
      </div>
      <button class="btn-pay" @click="onBack()">完成</button>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      resultInfo: {},
    };
  },
  computed: {},
  async onLoad(query) {
    this.resultInfo = query?.data ? JSON.parse(query?.data) || {} : {};
    this.resultInfo.channel = '中油智行APP';
    // uni.$emit('updateGiftCardCode', { msg: '更新礼品卡券支付二维码' });
  },
  methods: {
    onBack() {
      uni.$petro.route({
        type: 'back',
        delta: 1, // 默认是1，表示返回上一页，可不传delta
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-gift-result {
  min-height: 100vh;
  background: #f0f1f5;
  .page-container {
    padding: 20rpx 32rpx;
    text-align: center;

    .content {
      padding: 120rpx 0;
      background: #ffffff;
      border-radius: 16rpx;
      .result-icon {
        width: 128rpx;
        height: 128rpx;
      }
      .title {
        font-size: 30rpx;
        color: #333;
        height: 42rpx;
        line-height: 35rpx;
      }

      .unit {
        font-size: 72rpx;
      }

      .money {
        margin-top: 24rpx;
        height: 120rpx;
        font-weight: bold;
        font-size: 100rpx;
        color: #333333;
        line-height: 84rpx;
      }
    }

    .btn-pay {
      box-sizing: border-box;
      width: 100%;
      height: 92rpx;
      text-align: center;
      font-weight: 500;
      font-size: 32rpx;
      line-height: 92rpx;
      border-radius: 100rpx;
      background: #fa1919;
      color: #fff;
      padding: 0 120rpx;
      display: inline-block;
      margin-top: 60rpx;

      &:active {
        opacity: 0.8;
      }

      &.is-loading {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}
</style>
