<template>
  <div class="">
    <zyzx-page-restrictions v-if="isTure" :mainAccountNo="mainAccountNo" :enterpriseAccountNo="enterpriseAccountNo" />
  </div>
</template>

<script>
// import zyzxPageRestrictions from '@/components/zyzx-page-restrictions/zyzx-page-restrictions.vue';
export default {
  data() {
    return {
      list: [
        {
          title: '每天加油限量',
          name: 'dayOilVolume',
          content: '',
        },
        {
          title: '每次加油限量',
          name: 'onceOilVolume',
          content: '',
        },
        {
          title: '每天加油次数',
          name: 'dayOilTimes',
          content: '',
        },
        {
          title: '加油频次限制',
          name: 'frequency',
          content: '',
        },
        {
          title: '每天消费金额',
          name: 'dayAmount',
          content: '',
        },
        {
          title: '每月加油限量',
          name: '',
          content: '暂无数据',
        },
        {
          title: '每月消费金额',
          name: '',
          content: '暂无数据',
        },
        {
          title: '每月加油次数',
          name: '',
          content: '暂无数据',
        },
        {
          title: '限购油品',
          name: 'oils',
          content: '',
        },
        {
          title: '可购商品',
          name: 'goods',
          content: '',
        },
        {
          title: '定点油站',
          name: 'stationLimit',
          content: '',
        },
      ],
      enterpriseAccountNo: '',
      mainAccountNo: '',
      isTure: false,
    };
  },
  // components: { zyzxPageRestrictions },
  onLoad(query) {
    this.mainAccountNo = JSON.parse(query.data)?.mainAccountNo || '';
    this.enterpriseAccountNo = JSON.parse(query.data)?.enterpriseAccountNo || '';
    this.isTure = true;
  },
  onShow() {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss"></style>
