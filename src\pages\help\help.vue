<template>
  <div class="page-help">
    <u-navbar title="开通业务常见问题" :autoBack="true" :placeholder="true"> </u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-help />
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import ZyzxPageHelp from '@/components/zyzx-page-help/zyzx-page-help.vue';

export default {
  // components: { ZyzxPageHelp },
  data() {
    return {};
  },
  computed: {},
  onLoad(query) {},
  methods: {},
};
</script>

<style scoped lang="scss">
.page-help {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
