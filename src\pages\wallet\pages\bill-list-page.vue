<template>
  <view class="bill-list-page">
    <u-navbar :title="accountType == 8 ? '积分明细' : '账单明细'" :autoBack="true" :placeholder="true"></u-navbar>

    <!-- 账单列表 -->
    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-bill
          ref="zyzxPageBillRef"
          :isSortCategory="true"
          v-if="mainAccountNo"
          :mainAccountNo="mainAccountNo"
          :enterpriseAccountNo="enterpriseAccountNo"
          :account="account"
          :accountType="accountType"
          :type="type"
        />
      </petro-layout>
    </div>
  </view>
</template>

<script>
export default {
  name: 'bill-list-page',
  data() {
    return { mainAccountNo: '', enterpriseAccountNo: '', accountType: 5, account: '', type: '' };
  },
  watch: {
    mainAccountNo: {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.$nextTick(() => {
            this.$refs.zyzxPageBillRef?.getBillList(true);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  onLoad(query) {
    this.mainAccountNo = JSON.parse(query.data)?.mainAccountNo;
    this.enterpriseAccountNo = JSON.parse(query.data)?.enterpriseAccountNo;
    this.account = JSON.parse(query.data)?.account;
    this.accountType = JSON.parse(query.data)?.accountType;
    this.type = JSON.parse(query.data)?.type;

    console.log(query.data);
  },
  onShow() {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.bill-list-page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
