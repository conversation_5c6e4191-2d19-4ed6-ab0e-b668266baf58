<template>
  <div class="page-demo">
    <!--    <u-navbar :title="pageTitle" :autoBack="true" :placeholder="true" :bgColor="'transparent'"> </u-navbar>-->
    <u-navbar :title="pageTitle" :autoBack="false" :leftIconColor="'transparent'" :placeholder="true" :bgColor="'transparent'"></u-navbar>

    <!-- petro-layout 组件 | @supported petro-soti/foudation-zyzx@^1.1.0-1000 | 加载全局键盘 :petroKeyboard="true" -->
    <petro-layout ref="layout" :petro-keyboard="true">
      <img class="cover" src="http://picsum.photos/375/200?r=1" alt="" />

      <petro-market v-if="loadMarket" marketType="lbt" ref="topChargeBanner" spaceType="oilcharge_top" @click="onWebView()"></petro-market>

      <u-button>u-button</u-button>

      <petro-test></petro-test>

      <view class="container">
        <image class="logo" mode="widthFix" :src="logo"></image>

        <view class="cell-box" v-for="(v, k) in cellList" :key="k">
          <div class="cell-box-title">{{ v.group }}</div>
          <view class="cell-group-item" v-for="(vv, kk) in v.list" :key="kk">
            <view class="cell-group-item__title" @click="onItems(v.click || vv.click, vv)">{{ vv.title }}</view>
          </view>
        </view>
      </view>
    </petro-layout>
  </div>
</template>

<script>
import logo from 'assets/lg.png';

import appMixin from './platform/demo-app';
import wxMixin from './platform/demo-wx';
import zfbMixin from './platform/demo-zfb';

export default {
  mixins: [appMixin, wxMixin, zfbMixin],
  components: {},
  data() {
    return {
      logo, // 本地静态资源logo
      pageTitle: 'Demo', // 标题
      loadMarket: false, // 是否加载营销组件
      pollingTaskId: 0,
      cellList: [
        {
          group: 'mpaas安全键盘',
          list: [
            {
              title: 'securityPlugin',
              click: 'onOpenKeyboardForSecurityPlugin',
            },
          ],
        },
        {
          group: 'mpaas支付插件',
          list: [
            {
              title: 'payPlugin',
              click: 'onPayPlugin',
            },
          ],
        },
        {
          group: 'vuex',
          list: [
            {
              title: 'store count',
              click: 'onIncrementSync',
            },
            {
              title: 'modal',
              click: 'onModal',
            },
          ],
        },
        {
          group: 'uni.$petro API',
          list: [
            { title: '配置', click: 'onGetConfig' },
            { title: '设备指纹', click: 'onGetDFP' },
            { title: '获取GPS', click: 'onGetLocation' },
            { title: '强刷获取GPS', click: 'onGetLocationForce' },
            { title: '设置调试缓存信息', click: 'onSetTokenInfo' },
            { title: '清除调试缓存信息', click: 'onClearTokenInfo' },
            { title: '聚合获取用戶信息', click: 'onGetTokenInfo' },
            { title: '选择图片上传至OSS/OBS', click: 'chooseUploadImage' },
            { title: '选择文件上传至OSS/OBS', click: 'chooseUploadFile' },
            { title: '扫一扫 - 链接二维码', click: 'onScanLink' },
            { title: '扫一扫 - 业务二维码', click: 'onScanOther' },
            { title: 'TEST', click: 'onTest' },
          ],
        },
        {
          group: 'uni.$petro 组件',
          list: [{ title: '加载/销毁营销组件', click: 'onLoadMarket' }],
        },
        {
          group: 'uni.$petro.Utils 工具',
          list: [
            { title: '防抖', click: 'debounce' },
            { title: '开启/停止轮询微任务', click: 'onTogglePollTask' },
            { title: '并发唯一Promise任务', click: 'promiseTask' },
          ],
        },
        {
          group: 'uni.$petro.route 路由',
          list: [
            { title: '重新加载首页', click: 'onReLaunchHome' },
            { title: '新栈打开页面', click: 'onNavigateTo' },
            { title: '当前栈打开页面', click: 'onRedirectTo' },
            { title: '跳转其他小程序', click: 'onNavigateToMiniProgram' },
            { title: '打开webview', click: 'onWebView' },
            { title: 'webview监听路由', click: 'onWebviewListenRouter' },
            { title: '关闭webview', click: 'onCloseWebview' },
            { title: '跳转分包首页', click: 'onSubPackages' },
          ],
        },
        {
          group: 'uni.$petro.AccountPlugin 账户插件',
          list: [
            { title: '账户-微信/支付宝-安全键盘', click: 'onOpenKeyboard' },
            { title: '账户-APP-修改密码', click: 'changePW' },
          ],
        },
        {
          group: 'uni.$petro.PayPlugin 支付插件',
          list: [{ title: '获取充值支付方式', click: 'getRechargeTypeList' }],
        },
        {
          group: 'uni.$petro.Bridge.hkyz 真机协议',
          click: 'onHkyzCall',
          list: [
            { title: '关闭小程序', click: 'closeMriver' },
            { title: '唤起导航', click: 'nav' },
            { title: '获取设备支持的生物识别类型', click: 'supportType' },
            { title: '获取app的环境', click: 'workspaceName' },
            { title: '用手机浏览器打开url', click: 'mobileBrowsers' },
            { title: '获取状态栏高度', click: 'barHeight' },
            { title: '检查某个权限是否开启', click: 'checkPermission' },
            { title: 'app检查更新', click: 'checkUpdate' },
            { title: '获取小程序版本', click: 'getMiniVersion', data: { appId: 7928435601927482 } }, // 加油机器人小程序mPaaSId
            { title: '刷新token', click: 'refreshToken' },
            { title: '微信分享', click: 'wechatShare' },
            { title: '获取token', click: 'getToken' },
            { title: '获取getGsmsToken', click: 'getGsmsToken' },
            { title: '获取会员编码', click: 'getUserId' },
            { title: '获取风控设备指纹', click: 'getFinger' },
            { title: '支付中心SDK', click: 'paymentCenter' },
            { title: '账户中心SDK', click: 'accountCenter' },
            { title: '设备信息', click: 'commonArgs' },
            { title: '定位', click: 'locationInfo' },
            { title: '面部识别(IOS)', click: 'faceID' },
            { title: '指纹识别', click: 'fingerPrintRe' },
            { title: '阿里人脸获取aliMetaInfo', click: 'aliMetaInfo' },
            { title: '阿里人脸图像采集', click: 'faceCollecAli' },
            { title: '微信支付', click: 'wechatPay' },
            { title: '支付宝支付', click: 'aliPay' },
            { title: '清除缓存', click: 'cleanCache' },
            { title: '统计缓存', click: 'getCacheSize' },
            { title: '获取智能开关的值', click: 'getSwitch' },
            { title: '设置mpaas埋点', click: 'setEvent' },
            { title: '打开权限设置', click: 'openPermissions' },
            { title: '获取图片', click: 'getPhoto' },
            { title: '打开第三方app', click: 'openApp' },
            { title: '拨打电话', click: 'callPhone' },
            { title: '扫一扫', click: 'scan' },
            { title: '根据userid查询手机号', click: 'getPhoneByUserId' },
            { title: '获取原生相机权限', click: '' },
            { title: '获取原生相册权限', click: '' },
            { title: '支付宝预授权支付', click: '' },
          ],
        },
        {
          group: 'uni.$petro.Bridge.sys 旧系统协议',
          list: [{ title: '生物识别验证', click: 'localAuthVerifica' }],
        },
        {
          group: 'webview',
          list: [{ title: 'webview调用真机协议', click: 'onWebViewBridge' }],
        },
        {
          group: 'uni.$petro.http 请求',
          list: [
            { title: 'H5 API 同步 请求', click: 'onH5ApiSync' },
            { title: 'H5 API 异步 请求', click: 'onH5Api' },
            { title: 'Native API 请求', click: 'onNativeApi' },
          ],
        },
        {
          group: 'uni 官方',
          list: [
            { title: '相机', click: 'chooseImage' },
            { title: '扫码', click: 'scanCode' },
          ],
        },
        {
          group: 'uni.$petro.Bridge.zyzx 真机协议',
          click: 'onZyzxCall',
          list: [
            // { title: '获取APP的环境', click: 'env' },
            {
              title: '切换小程序为宿主小程序 - 管理员小程序',
              click: 'switchTinyApp',
              data: {
                appId: '6183959902273077',
                params: {
                  page: '/pages/index/index',
                  query: 'test=3',
                },
              },
            },
            { title: '选择文件', click: 'chooseFile', data: { count: 2 } },
            {
              title: '禁用用户截屏',
              click: 'setUserCaptureScreen',
              data: {
                enable: false,
                listen: () => {
                  console.log('用户截屏了');
                },
              },
            },
            {
              title: '允许用户截屏',
              click: 'setUserCaptureScreen',
              data: {
                enable: true,
              },
            },
            {
              title: '保存核心数据',
              click: 'save',
              data: {
                isAgreePrivacy: true, // 是否同意隐私弹框
                tinyAppType: '4-10', // 宿主小程序类型
                accessToken:
                    'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzE1IiwiaXNzIjoiZ3NtcyIsIm1vYmlsZSI6IjE4NjI4ODE2MDY4IiwidGltZSI6MTcyMTcxNDc4NDMyMSwidXNlcklkIjoiMzcifQ.SisqwyBw6-Ymi5EfmdmcUntzIeXs-uSZigu82zmGHuY', // accessToken凭证
                refreshToken: '', // 刷新token
                gsmsToken: 'JL:C15:2000042351200001:37:954fad27-6957-4030-a544-1ffe1943ea14', //
                userId: '37', // 用户id
                phone: '18628816068', // 手机号
                orgCode: '2000042351200001', // 组织编码
                expiresIn: '1721897157983', // token过期时间
              },
            },
            { title: '获取isAgreePrivacy', click: 'getIsAgreePrivacy' },
            { title: '获取tinyAppType', click: 'getTinyAppType' },
            { title: '获取accessToken', click: 'getAccessToken' },
            { title: '获取gsmsToken', click: 'getGsmsToken' },
            { title: '获取userId', click: 'getUserId' },
            { title: '获取phone', click: 'getPhone' },
            { title: '获取tokenInfo', click: 'getTokenInfo' },
            { title: '获取refreshToken', click: 'getRefreshToken' },
            // { title: '刷新tokenInfo', click: 'refreshTokenInfo' },
            { title: '获取deviceId', click: 'getDeviceId' },
            { title: '打开定位设置', click: 'openLocationSetting' },
            {
              title: '打开系统应用(浏览器、短信、邮件等) - 百度',
              click: 'launchUrl',
              data: {
                url: 'https://www.baidu.com',
              },
            },
            {
              title: '打开系统分享',
              click: 'systemShare',
              data: {
                shareTitle: '分享title',
                shareUrl: 'https://www.baidu.com',
                shareImage: 'https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/login/logo.png',
              },
            },
            {
              title: '应用内打开导航',
              click: 'openLocation',
              data: {
                startLat: 30.6605, // 起点经度
                startLon: 104.0715, // 起点纬度
                endLat: 40.17416, // 终点经度
                endLon: 116.24462, // 终点纬度
              },
            },
            {
              title: '梆梆加密',
              click: 'bangbangEncrypt',
              data: {
                // param: JSON.stringify({ test: 123 }),
                // param: encodeURIComponent(
                //   JSON.stringify({
                //     enterpriseNo: 2000030966600001,
                //     orgCode: '1-A5001-C001-S017',
                //     registerAndAdmittance: true,
                //     businessTypes: [10],
                //     enterpriseName: '',
                //     certificateType: 1,
                //     certificateNo: '马91442000MA4WMAP26X',
                //     legalPerson: '张鸾',
                //     businessScope:
                //       '研发、设计、生产、销售：照明灯具、灯用电器附件及其他照明器具、工艺美术品、展示制品、家具：承接园林绿化工程：销售：装修材料、建筑材料：设计、生产、销售：纺织品：货物及技术进出口。（依法须经批准的项目，经相关部门批准后方可开展经营活动。）',
                //     businessContactDTO: {
                //       name: '张鸾',
                //       identityNo: '513002199411206131',
                //       phone: '***********',
                //       businessContactNames: [10],
                //       checkEmail: false,
                //     },
                //     agrmtAttachmentsDTOS: [{ type: 1, url: 'www.belinda-bednar.com', name: 'marisela.upton', contentType: 'image/png' }],
                //     contactIdnumDTOS: [
                //       {
                //         type: 6,
                //         url: 'bc-partner/20240726181335650471721988813568.png',
                //         name: 'bc-partner/20240726181335650471721988813568.png',
                //         contentType: 'image/png',
                //       },
                //     ],
                //     authorizationLetterDTOS: [{ type: 2, url: 'www.belinda-bednar.com', name: 'marisela.upton', contentType: 'image/png' }],
                //     authInfo:
                //       'Fe4SyRuS5j2KWQJ6uiE0BX2r5TK5tb4ImphFcRzYfX8uNZxlb7ADjrXUkWdRpJCAB4nAx3jsrNrz03e1PkfoecM_swAjOKLz7fz9dMxOY-TGxgCa1iWI4H9Hn27dKqK9n0gXyt4rE3IdxbO69LjcTOsgPwkvKLf0X3jWyMRyoiqSF0cOeJ6gOwfAbdQ0dSxvZjTMC0X_0-KK-gUK7igwkTASdKHLytsbGstYyh7pZqVc-LtNaK9wlWO4vQlKJ1YaSptprEe5lzpNvY-_72XLlboSLQ00Z9Wuleodw7j8U22Csl4wBYyhDuHhp3vYLDXazDaXPuPa69JONc3s_DDuyA',
                //     deviceId: 'Zpdg3ka8UIEDAM0iL11cnUuE',
                //   }),
                // ),
                param:
                    '{"enterpriseNo":2000086341200001,"orgCode":"1-A5001-C001-S017","registerAndAdmittance":true,"businessTypes":[10],"enterpriseName":"","certificateType":1,"certificateNo":"9131011632424217XF","legalPerson":"张鸾","businessScope":"文化艺术交流策划咨询、高务信息咨询，企业管理咨询（除经纪），市场营销策划。企业形象策划，体育赛事策划，设计制作代理发布各类广告。电脑图文设计制作动漫设计，服装设计，模型设计，美术设计制作、包装装潢设计，摄摄像服务。会务服务，礼仪服务，婚庆服务，展览展示服务，翻译服务，舞台艺术创作服务。自有广告器材租赁，从事计算机领域内的技术开发、技术咨询、技术服务，电子商务（不得从事增值电信、金融业务）【依法须经批准的项目，经相关部门批准后方可开展经营活动】","businessContactDTO":{"name":"张鸾","identityNo":"513002199411206131","phone":"***********","businessContactNames":[10],"checkEmail":false},"agrmtAttachmentsDTOS":[{"type":1,"url":"www.belinda-bednar.com","name":"marisela.upton","contentType":"image/png"}],"contactIdnumDTOS":[{"type":6,"url":"bc-partner/20240726191308835471721992386667.png","name":"bc-partner/20240726191308835471721992386667.png","contentType":"image/png"}],"authorizationLetterDTOS":[{"type":2,"url":"www.belinda-bednar.com","name":"marisela.upton","contentType":"image/png"}],"authInfo":"jo0khEa52VYoaoP0l8ebH3KO59FtgNGEL6e4zMMyfFeVdq56AuvXo6JruQoxAs3GSYycq_TS3dZEe3fOtHX45ZEGMOSc75Z79dfp8eLUBIItheopSGJVV6Nye9yq-8AsOqENvPFWMQ9H1a8kqcn-kFh2XVGReEaGLQhAW56HrDzPHWyZ1zLUIZrQ8l1dWtH1hCyHBFLpZrNZvd2ZugmFylA_xIx2fRGXr108G8yBRKJfX6Z2wO10vHQK13xlXTUDIwLoUqjR5GhbP6uRZ85Ax4_j3zybZ1EU7YYR5vMEjRsi-zUtdebAmXaP2KxdKcKTgdTeGiZMBdoLQA9PFnn06Q","deviceId":"Zpdg3ka8UIEDAM0iL11cnUuE"}',
              },
            },

            { title: 'openSysSetting', click: 'openSysSetting' },
            { title: 'systemShare', click: 'systemShare' },

            { title: '检查APP更新', click: 'checkAppUpdate' },
            { title: '预授权权限', click: 'preAuthPermissions' },
            { title: '生物识别支持类型', click: 'biometricAuthSupported' },
            { title: '用手机浏览器打开url', click: 'openBrowserUrl' },
            { title: '分享', click: 'showSharePanel' },
            { title: '退出小程序', click: 'exitMiniProgram' },
            { title: '定位', click: 'getLocation' },
            { title: '面部识别(IOS)', click: 'faceRecognition' },
            { title: '指纹识别', click: 'fingerprintRecognition' },
            { title: '微信支付', click: 'weChatPay' },
            { title: '支付宝支付', click: 'aliPay' },
            { title: '清除缓存', click: 'clearCache' },
            { title: '统计缓存', click: 'getCacheSize' },
            { title: '获取配置开关值', click: 'getConfigSwitch' },
            { title: '打开权限设置页', click: 'openPermissions' },
            { title: '打开第三方APP', click: 'launchApp' },
            { title: '真机联调扫一扫', click: 'debugScan' },
            {
              title: '人脸认证（阿里云）- getMetaInfo',
              click: 'faceAuth',
              data: {
                bizType: 'getMetaInfo',
                data: {},
              },
            },
            {
              title: '人脸认证（阿里云）- verify',
              click: 'faceAuth',
              data: {
                bizType: 'verify',
                data: {
                  certifyId: '',
                },
              },
            },

            {
              title: '支付中心SDK - initPay',
              click: 'payCenter',
              data: {
                bizType: 'initPay', // 初始化 - 尽早调用
                data: {},
              },
            },
            {
              title: '支付中心SDK - exitPay',
              click: 'payCenter',
              data: {
                bizType: 'exitPay', // 退出SDK
                data: {},
              },
            },
            {
              title: '支付中心SDK - getBusinessNoList',
              click: 'payCenter',
              data: {
                bizType: 'getBusinessNoList', // 获取企业待支付订单 查询待支付订单 无需传参
                data: {},
              },
            },
            {
              title: '支付中心SDK - rposBusinessPay',
              click: 'payCenter',
              data: {
                bizType: 'rposBusinessPay', // 企业确认支付扫码订单
                data: {
                  businessInfo: {
                    businessNo: '87777', //业务编码
                    staffNo: '34234', //员工编号
                    accountType: 2, //账户类型
                    capitalType: 3, //金额类型
                  },
                  orderNo: '',
                  amount: '',
                  extInfo: {},
                  headers: {},
                },
              },
            },
            {
              title: '支付中心SDK - businessPreOrder',
              click: 'payCenter',
              data: {
                bizType: 'businessPreOrder', // 企业账户预授权下单
                data: {
                  businessInfo: {
                    businessNo: '87777', //业务编码
                    staffNo: '34234', //员工编号
                    accountType: 2, //账户类型
                    capitalType: 3, //金额类型
                  },
                  businessCodeInfo: {
                    businessIdx: 0, //业务序号
                    charType: 1, //角色类型
                  },
                  businessPayInfo: '', //  支付信息
                  extInfo: {},
                  headers: {},
                },
              },
            },

            {
              title: '账户中心SDK - init',
              click: 'accountCenter',
              data: {
                bizType: 'init', // 初始化 - 尽早调用
                data: {},
              },
            },
            {
              title: '账户中心SDK - register',
              click: 'accountCenter',
              data: {
                bizType: 'register', // 会员注册登录
                data: {
                  memberDocNo: '', // 会员编号
                  token: '', // 网络认证
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - reset',
              click: 'accountCenter',
              data: {
                bizType: 'reset', // 会员登出
                data: {},
              },
            },
            {
              title: '账户中心SDK - update',
              click: 'accountCenter',
              data: {
                bizType: 'update', // 更新会员网络认证
                data: {
                  memberNo: '', // 会员编号
                  token: '', // 网络认证
                },
              },
            },
            {
              title: '账户中心SDK - clearMember',
              click: 'accountCenter',
              data: {
                bizType: 'clearMember', // 清除会员信息
                data: {
                  memberNo: '', // 会员编号
                },
              },
            },
            {
              title: '账户中心SDK - clearAllMembers',
              click: 'accountCenter',
              data: {
                bizType: 'clearAllMembers', // 清除所有会员信息
                data: {},
              },
            },
            {
              title: '账户中心SDK - active',
              click: 'accountCenter',
              data: {
                bizType: 'active', // 激活账户业务
                data: {
                  activation: [
                    // 带激活的列表
                    {
                      unitMemberNo: 'xxx',
                      businessNo: 'xxx',
                      staffNo: '',
                      invoiceType: 4, // 5-开户地开普票 ；7-消费地开普票
                    },
                  ],
                  charType: 2, // 车队业务管理员-2 司机-4
                  token: '',
                  IDNo: '',
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - changePassword',
              click: 'accountCenter',
              data: {
                bizType: 'changePassword', // 修改密码
                data: {
                  charType: 2,
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - resetPassword',
              click: 'accountCenter',
              data: {
                bizType: 'resetPassword', // 重置密码
                data: {
                  charType: 2,
                  IDNo: '',
                  token: '',
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - generatePayCode',
              click: 'accountCenter',
              data: {
                bizType: 'generatePayCode', // 生成支付码
                data: {
                  businessIdx: 1111, // ？？
                  charType: 1, // 角色类型
                },
              },
            },
            {
              title: '账户中心SDK - confirmPayCode',
              click: 'accountCenter',
              data: {
                bizType: 'confirmPayCode', // 确认扫码支付
                data: {
                  businessInfo: {
                    businessNo: '', // 业务编码
                    accountType: '', // 账户类型，
                    capitalType: '', // 金额类型
                  },
                  orderNo: 'xxx', // 订单号
                  amount: 'xxx', // 金额
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - confirmPreTrade',
              click: 'accountCenter',
              data: {
                bizType: 'confirmPreTrade', // 确认预授权支付
                data: {
                  businessInfo: {
                    businessNo: '', // 业务编码
                    accountType: '', // 账户类型，
                    capitalType: '', // 金额类型
                  },
                  businessCodeInfo: {
                    businessIdx: 1111, // ？？
                    charType: 1, // 角色类型
                  },
                  orderNo: 'xxx', // 订单号
                  amount: 'xxx', // 金额
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - transferTemplate',
              click: 'accountCenter',
              data: {
                bizType: 'transferTemplate', // 模板分配汇总
                data: {
                  businessInfo: {
                    businessNo: '', // 业务编码
                    accountType: '', // 账户类型，
                    capitalType: '', // 金额类型
                  },
                  businessCodeInfo: {
                    businessIdx: 1111, // ？？
                    charType: 1, // 角色类型
                  },
                  templateId: '',
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
            {
              title: '账户中心SDK - transferDetail',
              click: 'accountCenter',
              data: {
                bizType: 'transferDetail', // 定制分配汇总
                data: {
                  businessInfo: {
                    businessNo: '', // 业务编码
                    accountType: '', // 账户类型，
                    capitalType: '', // 金额类型
                  },
                  businessCodeInfo: {
                    businessIdx: 1111, // ？？
                    charType: 1, // 角色类型
                  },
                  detailInfo: {
                    transferType: 1, // 分配类型
                    businessNo: '', // 业务编码
                    amount: '', // 金额
                    staffDocNo: 'x', // 是司机分配汇总才传。员工档案编码？和staffNo区别？
                  },
                  extInfo: {}, // 扩展数据
                  headers: {}, // HTTP-Header数据
                },
              },
            },
          ],
        },
      ],
    };
  },
  // onUnload() {
  //     console.log('onUnload');
  // },
  beforeCreate() {
    console.log('beforeCreate');
  },
  created() {
    console.log('created');
  },
  beforeMount() {
    console.log('beforeMount');
  },
  onLoad(query) {
    console.log('demo onLoad', query);
    // my.hideShareMenu();
    // uni.clearStorageSync();
    // this.promiseTask();
  },
  onShow() {
    console.log('onShow');
    // this.promiseTask();
  },
  onHide() {
    console.log('onHide');
  },
  mounted() {
    console.log('mounted', process.env);

    console.log('uni.$u', uni.$u);
    console.log('uni.$petro', uni.$petro);
    console.log('$refs', this.$refs);

    try {
      uni.$petro.rootTest();
      uni.$petro.overwrite();
      uni.$petro.test();
    } catch (err) {
      console.log(err);
    }

    // #ifdef MP-MPAAS
    console.log('MP-MPAAS');
    // #endif

    // #ifdef MP-WEIXIN
    console.log('MP-WEIXIN');
    // #endif

    // #ifdef MP-ALIPAY
    console.log('MP-ALIPAY');
    // #endif

    // #ifndef MP-MPAAS
    console.log('NOT MP-MPAAS');
    // #endif

    // #ifndef MP-WEIXIN
    console.log('NOT MP-WEIXIN');
    // #endif

    // #ifndef MP-ALIPAY
    console.log('NOT MP-ALIPAY');
    // #endif
  },
  onReady() {
    console.log('onReady');
  },
  updated() {
    console.log('updated');
  },
  activated() {
    console.log('activated');
  },
  deactivated() {
    console.log('deactivated');
  },
  beforeDestroy() {
    console.log('beforeDestroy');
  },
  destroyed() {
    console.log('destroyed');
  },
  errorCaptured() {
    console.log('errorCaptured');
  },
  computed: {},
  methods: {
    onItems(clickName, data) {
      this[clickName].call(this, data);
    },
    // 加载/销毁营销组件
    onLoadMarket() {
      this.loadMarket = !this.loadMarket;
    },
    // 获取配置
    async onGetConfig() {
      const { config } = await uni.$petro;
      console.log('Config:', config);
    },
    // 获取设备指纹
    async onGetDFP() {
      const result = await uni.$petro.DFP.getDfp();
      console.log('DFP:', result);
    },
    // 获取GPS
    async onGetLocation() {
      const result = await uni.$petro.getLocation();
      console.log('getLocation:', result);
    },
    // 强制获取GPS
    async onGetLocationForce() {
      const env = my.env;
      console.log('env:', aaa);

      const result = await uni.$petro.getLocation({}, true);
      console.log('getLocationForce:', result);
    },
    // 设置调试缓存信息
    async onSetTokenInfo() {
      console.warn('仅mPaaS真机支持输出!!!');
      const tokenInfo = {
        accessToken:
            'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjbGllbnRDb2RlIjoiQzE1IiwiaXNzIjoiZ3NtcyIsIm1vYmlsZSI6IjE4NjQwNjQ1Mjk5IiwidGltZSI6MTczMjI0NTQwOTA0MCwidXNlcklkIjoiNDc2In0.LwQHL0O_xDai6bynG9336LStjhaWWmIsgZwzh-Kb-9c',
        expiresIn: '1732849609041',
        gsmsToken: 'JL:C15:2430100432610001:476:7ce22c6e-70c0-4895-9c3c-a032e56c2b1d',
        memberNo: '476',
        orgCode: '2430100432610001',
        authStatus: 0,
        tempCode: null,
        petroChinaNo: '32107236711508',
      };
      const result = await uni.$petro.setTokenInfo(tokenInfo);
      console.log('onSetTokenInfo', result);
      uni.showModal({
        title: 'onSetTokenInfo1',
        content: JSON.stringify(result) + JSON.stringify(tokenInfo),
      });
    },
    // 清除调试缓存信息
    async onClearTokenInfo() {
      const tokenInfo = {
        accessToken: '',
        expiresIn: '',
        gsmsToken: '',
        memberNo: '',
        orgCode: '',
        phone: '',
      };
      const result = await uni.$petro.setTokenInfo(tokenInfo);
      console.log('onSetTokenInfo', result);
      uni.showModal({
        title: 'onSetTokenInfo',
        content: JSON.stringify(result) + JSON.stringify(tokenInfo),
      });
    },
    // 用戶信息
    async onGetTokenInfo() {
      console.warn('仅mPaaS真机支持输出!!!');
      const result = await uni.$petro.getTokenInfo();
      console.log('onGetTokenInfo:', result);
      uni.showModal({
        title: 'onGetTokenInfo',
        content: JSON.stringify(result),
      });
    },
    // 扫一扫 - 链接二维码
    async onScanLink() {
      console.warn('仅mPaaS真机支持输出!!!');
      const result = await uni.$petro.scan();
      console.log('onScanLink:', result);
      uni.showModal({
        title: 'onScanLink',
        content: JSON.stringify(result),
      });
    },
    // 扫一扫 - 业务二维码
    async onScanOther() {
      console.warn('仅mPaaS真机支持输出!!!');
      const result = await uni.$petro.scan({
        type: 4,
      });
      console.log('onScanOther:', result);
      uni.showModal({
        title: 'onScanOther',
        content: JSON.stringify(result),
      });
    },
    // 测试函数
    async onTest() {
      try {
        console.log(uni.$petro.store);

        try {
          await uni.$petro.Bridge.zyzx.getUserId();
          await uni.$petro.Bridge.zyzx.getUserId();
          await uni.$petro.Bridge.zyzx.getUserId();
        } catch (err) {
          console.error(err);
        }

        uni.$petro.Bridge.zyzx.getTokenInfo();
        uni.$petro.Bridge.zyzx.getTokenInfo();
        uni.$petro.Bridge.zyzx.getTokenInfo();

        uni.$petro.Bridge.zyzx.bangbangEncrypt({
          param: '{}',
        });
        uni.$petro.Bridge.zyzx.bangbangEncrypt({
          param:
              '{"enterpriseNo":2000086341200001,"orgCode":"1-A5001-C001-S017","registerAndAdmittance":true,"businessTypes":[10],"enterpriseName":"","certificateType":1,"certificateNo":"9131011632424217XF","legalPerson":"张鸾","businessScope":"文化艺术交流策划咨询、高务信息咨询，企业管理咨询（除经纪），市场营销策划。企业形象策划，体育赛事策划，设计制作代理发布各类广告。电脑图文设计制作动漫设计，服装设计，模型设计，美术设计制作、包装装潢设计，摄摄像服务。会务服务，礼仪服务，婚庆服务，展览展示服务，翻译服务，舞台艺术创作服务。自有广告器材租赁，从事计算机领域内的技术开发、技术咨询、技术服务，电子商务（不得从事增值电信、金融业务）【依法须经批准的项目，经相关部门批准后方可开展经营活动】","businessContactDTO":{"name":"张鸾","identityNo":"513002199411206131","phone":"***********","businessContactNames":[10],"checkEmail":false},"agrmtAttachmentsDTOS":[{"type":1,"url":"www.belinda-bednar.com","name":"marisela.upton","contentType":"image/png"}],"contactIdnumDTOS":[{"type":6,"url":"bc-partner/20240726191308835471721992386667.png","name":"bc-partner/20240726191308835471721992386667.png","contentType":"image/png"}],"authorizationLetterDTOS":[{"type":2,"url":"www.belinda-bednar.com","name":"marisela.upton","contentType":"image/png"}],"authInfo":"jo0khEa52VYoaoP0l8ebH3KO59FtgNGEL6e4zMMyfFeVdq56AuvXo6JruQoxAs3GSYycq_TS3dZEe3fOtHX45ZEGMOSc75Z79dfp8eLUBIItheopSGJVV6Nye9yq-8AsOqENvPFWMQ9H1a8kqcn-kFh2XVGReEaGLQhAW56HrDzPHWyZ1zLUIZrQ8l1dWtH1hCyHBFLpZrNZvd2ZugmFylA_xIx2fRGXr108G8yBRKJfX6Z2wO10vHQK13xlXTUDIwLoUqjR5GhbP6uRZ85Ax4_j3zybZ1EU7YYR5vMEjRsi-zUtdebAmXaP2KxdKcKTgdTeGiZMBdoLQA9PFnn06Q","deviceId":"Zpdg3ka8UIEDAM0iL11cnUuE"}',
        });

        return;

        const {
          data: { files = [] },
        } = await uni.$petro.Bridge.zyzx.chooseFile({
          count: 1,
        });
        console.log('files', files);

        // 遍历文件数组
        Array.from(files).forEach(async file => {
          // file.arrayBuffer = uni.base64ToArrayBuffer(file.data);
          console.log('file', file, file.data, file.arrayBuffer);
          const result = await uni.$petro.uploadFile(
              file.data,
              'pdf',
              // {
              //   contentType: 'image/png',
              //   fileName: new Date().getTime(),
              // }
          );
          console.log('file result', result);

          // uni.getFileInfo({
          //   filePath: file?.path,
          //   success: file => {
          //     console.log('uni.getFileInfo', file);
          //   },
          //   fail: err => {
          //     console.error(err);
          //   },
          // });
        });
      } catch (err) {
        console.error(err);
      }
    },
    // 微信支付
    async wechatPay2() {
      const { config } = await uni.$petro;
      const payInfo = {
        //   appid: config?.mPaas?.appid,
        appid: 'wx0859438800a3f7fe',
        noncestr: 'jCHRWiZPG6JwMUOG',
        packages: 'Sign=WXPay',
        partnerid: '1299348001',
        prepayid: 'wx171610054908622b5f5fffeb9d81e40000',
        timestamp: 1715933405,
        sign: '6E9C519302272A2C01A090F9482C798E',
      };
      const res = await uni.$petro.Bridge?.appCall({
        type: 'wechatPay',
        data: {
          payinfo: payInfo,
        },
      });
      console.log('payInfo', payInfo, res);
    },
    // 支付宝支付
    async aliPay2() {
      const payInfo =
          'app_id=2015121300969087&biz_content=%7B%22out_trade_no%22%3A%22UPX249874512736838892%22%2C%22subject%22%3A%22%E4%BB%98%E8%B4%B9%E4%BC%9A%E5%91%98%22%2C%22timeout_express%22%3A%225m%22%2C%22total_amount%22%3A0.01%7D&charset=utf-8&format=JSON&method=alipay.trade.app.pay&notify_url=http%3A%2F%2Fyoutu.95504.net%2Fv4%2Fupay%2Fnotify%2Falipay&sign=B%2FsQzArpYiWbBnWFjJKZWGPm4T5kAvBec4pToq2o7TaxrlEQ6naf%2BdNKHUEA9rxyzjagoTh7ZhwSGXhVMip6DnzugxAWh3rngQ%2FUGsBoRUy4Zhj67AfspfkG1t9UPu8HED9iTozHsobMFmyMHJXqeDldVM0yHiXVEaq01jcKOlxo6pRksQjCcgYuF371ZaTTz8yCHQjWumHuDrt6hZFVJGgDHY9l1OVQKiZzdTNBPJ6neODIr6BjPXR5mp6d2S86z9f3sVOikKY1Ml72UyC0G%2F0WBwGJHAFN%2F6fi7YbT%2BXrHikZeOgYMSlk8uakg97m57K0olQufIPr3DPb98C5SAA%3D%3D&sign_type=RSA2&timestamp=2024-05-22+13%3A48%3A10&version=1.0';
      const res = await uni.$petro.Bridge?.appCall({
        type: 'aliPay',
        data: {
          aliPreapy: payInfo,
        },
      });
      console.log('payInfo', payInfo, res);
    },
    async localAuthVerifica() {
      console.warn('仅mPaaS真机支持输出!!!');
      const result = await uni.$petro.Bridge.sys.localAuthVerifica(res => {
        console.log(res);
      });
      console.log('localAuthVerifica:', result);
    },
    // 返回首页
    async onReLaunchHome() {
      uni.$petro.reLaunchHome();
    },
    // 新页面打开
    async onNavigateTo() {
      // uni.$petro.route('/pages/login/login', { test: 1 });
      // uni.$petro.route('/pages/login/login', { test: 1 }, true);
      // uni.$petro.route('/pages/login/login', { test: 1 }, 'key');
      // uni.$petro.route({ url: '/pages/login/login?sub=2', params: { type: 1 }, type: 'to' }, true);
      // uni.$petro.route({ url: '/pages/login/login?sub=2', params: { type: 1 }, type: 'to' }, 'key');
    },
    // 当前页打开
    async onRedirectTo() {
      uni.$petro.route({ url: '/pages/login/login?sub=2', params: { type: 1 }, type: 'redirectTo' });
    },
    // 跳转到小程序
    async onNavigateToMiniProgram() {
      const res = await uni.$petro.route(
          {
            appId: '3815480475716653', // 加油小程序
            url: 'packages/third-scan-code-payment/pages/home-code/main',
          },
          { test: 123 },
      );
      console.log('onNavigateToMiniProgram', res);

      // uni.$petro.route(
      //   {
      //     appId: '3815480475716653', // 好客e站-订单列表
      //     url: `packages/third-order/pages/home/<USER>
      //   },
      //   {
      //     navActive: 1,
      //     secondNavActive: 0,
      //   },
      //   true,
      // );

      // uni.$petro.route(
      //   {
      //     appId: '4908542685197380', // O2O商城小程序
      //     url: 'pages/home/<USER>',
      //   },
      //   { test: 123 },
      // );
    },
    // webView
    async onWebView(url) {
      console.log('onWebView', url);
      uni.$petro.route({ url: `/pages/webview/webview?src=${url}`, type: 'navigateTo' });
      // uni.$petro.route({ url: `/pages/webview/webview?src=http%3A%2F%2Fvue2.kekuming.xin%2F`, type: 'navigateTo' });
      // uni.$petro.route({ url: `/pages/webview/webview?src=https%3A%2F%2Fyoutu.95504.net%2Factivity%2Fmpaas_h5%2F`, type: 'navigateTo' });
      // uni.$petro.route({
      //   url: `/pages/webview/webview?src=https%3A%2F%2Fyyhdnp.kunlunjyk.com%2Fwxzfzf-cj%2Findex%3FactivityClass%3D%25E5%25BC%2580%25E5%258F%2591%25E6%25B5%258B%25E8%25AF%2595-tang%26userId%3DZCXAfq2nrVRg83pThf8SUFm2O4rX5Vv9svGflfG0vOHkW69tiCb0o8QzxWT6zwLMUjXCtqvDucGpG6YvmX6Pwz1ppcVpvifKMNuX8lhaHmNC7BJhnotwlNgAaPqKlJumYi8XKglv%2FlA%2Fb79uxhm7y%2FwmXFDtmtu2oGvVSqed6zw%3D`,
      //   type: 'navigateTo',
      // });
    },
    // webView调用App真机协议
    async onWebViewBridge() {
      console.log('扫码');
      uni.$petro.Bridge.call('hkyz.scan', '', res => {
        console.log('done', res);
      });
    },
    // 打开账户密码键盘
    async onOpenKeyboard() {
      console.warn('支付宝/微信小程序订阅插件后支持!', uni.$petro.AccountPlugin);
      uni.$petro.AccountPlugin.ref?.openKeyboard(
          'password',
          6,
          async pwd => {
            console.log(pwd);
          },
          () => {
            console.log('密码键盘的关闭函数');
          },
      );
    },
    // 获取充值支付方式
    async getRechargeTypeList() {
      console.warn('仅mPaaS真机支持输出!!!');
      console.log('获取充值支付方式');
      const res = await uni.$petro.PayPlugin.GetRechargeTypeList({
        areaCode: '1-A1103-C008',
        // stationCode: '1-A4301-C001-S006',
      });
      console.log('res', res);
    },
    async changePW() {
      console.warn('仅mPaaS真机支持输出!!!');
      console.log('修改密码');
      uni.$petro.Bridge?.acc?.changePW(res => {
        console.log('res', res);
      });
    },
    async permissionCamera() {
      console.warn('仅mPaaS真机支持输出!!!');
      const cp = {
        code: 'camera',
        explain: '访问相机权限使用说明',
        detail: '用于扫一扫、图片上传、拍照、人脸认证的服务。',
      };
      const res = await uni.$petro.Bridge?.sys?.openPermissions(cp);
      console.log(res);
    },
    async permissionGps() {
      console.warn('仅mPaaS真机支持输出!!!');
      const lp = {
        code: 'location',
        explain: '位置权限使用说明',
        detail: '使用您的位置用于获取您所在地区、附近的加油站信息等，为您提供本地服务。',
      };
      const res = await uni.$petro.Bridge?.sys?.openPermissions(lp);
      console.log(res);
    },
    // 分享
    async onAppShare() {
      console.warn('仅mPaaS真机支持输出!!!');
      // 分享到微信好友
      const shareData = {
        webPageUrl: 'http://vue2.kekuming.xin',
        title: '我的分享标题',
        description: '这是一段描述',
        imageUrl: 'https://example.com/image.jpg',
        sceneType: 0,
      };
      uni.$petro.Bridge?.sys?.wechatShare(shareData);
    },
    // $store 同步
    async onIncrementSync() {
      console.log('IncrementSync');
      this.$store.commit('increment');
      console.log('$store.state.count', this.$store.state.test1.count, this.$store.state.test2.count);
    },
    // H5 API 同步网关请求
    async onH5ApiSync() {
      console.log('onH5ApiSync');
      // 如果要阻断流程则不需要try，如果不影响后续流程则需要try
      try {
        const { success, data } = await uni.$petro.http(
            // 'bcaccount.unitAcctTransaction.preAuthStaffAcct.h5',
            // 'bcsecuritykb.pwd.getKeyboardInfo.h5',
            'bcaccount.unitAcctMgmt.updatePwd.h5',
            // 'bcaccount.unitAcctMgmt.resetPwd.h5',
            // 'bcaccount.unitAcctMgmt.activateAcct.h5',
            {
              bookingRefueling: '',
              distance: '6',
              latitude: 40.**************,
              longitude: 116.**************,
              mapType: '1',
              orgCode: '110114',
              pageNum: 1,
              pageSize: 10,
            },
            {
              headers: {
                Authorization: 'Bearer JL:C2853:****************:522:b428a88c-2f5b-4ea6-a4b4-b5fd2683fe9d',
                // token: '',
                'is-plugin': 'sf',
              },
            },
        );
        if (success) {
          uni.showModal({
            content: JSON.stringify(data),
          });
        }
      } catch (e) {}

      return;

      // 如果要阻断流程则不需要try，如果不影响后续流程则需要try
      try {
        const { success, data } = await uni.$petro.http(
            'oilstation.station.list.h5',
            {
              bookingRefueling: '',
              distance: '6',
              latitude: 40.**************,
              longitude: 116.**************,
              mapType: '1',
              orgCode: '110114',
              pageNum: 1,
              pageSize: 10,
            },
            {
              headers: {
                // Authorization: '',
                // token: '',
              },
            },
        );
        if (success) {
          uni.showModal({
            content: JSON.stringify(data),
          });
        }
      } catch (e) {}

      try {
        const res2 = await uni.$petro.http('user.getTodoInfo.h5', {});
        console.log('res2', res2);
      } catch (e) {}

      // 本地mock数据，需配置文件开启mock true，仅dev/sit环境生效
      // const mockRes = await uni.$petro.http(
      //   'oilstation.station.getFuelGunByOrgCode',
      //   {},
      //   {
      //     mockResponse: {
      //       success: true,
      //       data: [
      //         {
      //           fuelNo: '300667',
      //           fuelGunNo: ['1', '2', '4', '11', '28', '46'],
      //           fuelName: '95号 车用汽油(Ⅴ)',
      //           fuelType: 12,
      //         },
      //       ],
      //       message: '请求成功',
      //       errorCode: null,
      //     },
      //   },
      // );
      // console.log('mockRes', mockRes);
    },
    // H5 API 异步网关请求
    async onH5Api() {
      console.log('onH5Api');
      // try {
      const [{ success, data }, res2] = await Promise.all([
        uni.$petro.http(
            'oilstation.station.list.h5',
            {
              bookingRefueling: '',
              distance: '6',
              latitude: 40.**************,
              longitude: 116.**************,
              mapType: '1',
              orgCode: '110114',
              pageNum: 1,
              pageSize: 10,
            },
            {
              // headers: {
              //   Authorization: '',
              //   token: '',
              // },
            },
        ),
        uni.$petro.http(
            'user.getTodoInfo.h5',
            {},
            {
              // method: 'get',
              // headers: {
              // 'App-Login-User': 'token',
              // 'X-Nonce': 'nonce',
              // 'X-Sign': 'sign',
              // 'X-Time': 'time', // 时间戳字符串
              // },
            },
        ),
      ]);
      if (success) {
        uni.showModal({
          content: JSON.stringify(data),
        });
      }
      console.log('res2', res2);
      // } catch (e) {}
    },
    // Native API 网关请求
    async onNativeApi() {
      const { success, data } = await uni.$petro.http(
          'oilstation.station.list',
          {
            bookingRefueling: '',
            distance: '10',
            latitude: 40.**************,
            longitude: 116.**************,
            mapType: '1',
            orgCode: '110114',
            pageNum: 1,
            pageSize: 10,
          },
          {
            showLoading: false, // 是否显示loading
            beforeHooks: args => {
              // 请求前钩子
              console.log(1111, args);
            },
            riskField: true, // 是否风控接口
            apiVersion: 'v1', // api版本
          },
      );

      if (success) {
        uni.showModal({
          content: JSON.stringify(data),
        });
      }
    },
    // 监听webview路由
    onWebviewListenRouter() {
      window.addEventListener('popstate', event => {
        console.log('event, history.length', event, history.length);
        if (event.state.id === '1') {
          this.onCloseWebview();
        }
      });
    },
    // 关闭webview
    onCloseWebview() {
      uni.$petro.closeWebview();
    },
    // 分包首页
    onSubPackages() {
      console.log('需将分包添加至编译产物dist/mp-weixin下，并且添加分包路径至dist/mp-weixin/app.json中');
      console.log('例如分包为example，则跳转以下分包路径');
      uni.$petro.route('/example/pages/index/index', { sub: 1 });
    },
    // 选择图片并上传至OSS/OBS
    async chooseUploadImage() {
      const res = await uni.$petro.chooseUploadImage({
        count: 9,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        extension: ['*.jpg', '*.png'], // 仅H5支持
        // maxSize: 1024 * 1024 * 0.5,
      });

      res?.forEach(v => {
        v.base64 = '太长省略打印...';
      });

      console.log('chooseUploadImage', res);

      uni.showModal({
        title: '上传图片完成',
        content: JSON.stringify(
            res.map(v => {
              return {
                objectKey: v.objectKey,
                statusCode: v.statusCode, // 200 为成功
                base64: v.base64,
                contentType: v.contentType,
                errMsg: v.errMsg,
              };
            }),
        ),
      });
    },
    // 选择文件并上传至OSS/OBS
    async chooseUploadFile() {
      console.warn('仅mPaaS真机支持输出!!!');
      const extension = ['pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
      uni.showActionSheet({
        itemList: extension,
        success: async ({ tapIndex }) => {
          if (tapIndex < 0) return;
          try {
            const res = await uni.$petro.chooseUploadFile({
              count: 2,
              extension: [extension[tapIndex]],
            });
            console.log('chooseUploadFile:', res);
            uni.showModal({
              title: '上传文件完成',
              content: JSON.stringify(res),
              showCancel: false,
            });
          } catch (err) {
            uni.showModal({
              title: '上传文件失败',
              content: err,
              showCancel: false,
            });
          }
        },
        fail: res => {},
      });
    },
    // 选择图片
    async chooseImage() {
      // 相机/相册选择图片
      uni.chooseImage({
        count: 3,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          console.log('uni.chooseImage', res, res.tempFilePaths[0]);

          // 可获取图片arrayBuffer信息
          // const fs = uni.getFileSystemManager();
          // Array.from(res.tempFilePaths).forEach(async file => {
          //   const { success, dataType, data } = fs.readFileSync(file);
          //   console.log('fs.readFileSync', file, success, dataType, data);
          //   if (!success) {
          //     return console.error('fs.readFileSync fail', file);
          //   }
          //   const result = await uni.$petro.uploadFile(data);
          //   console.log('onUploadImage:', result);
          // });

          // 获取不到图片信息，不推荐使用
          // uni.getImageInfo({
          //   src: res.tempFilePaths[0],
          //   success: image => {
          //     console.log('uni.getImageInfo', image);
          //   },
          //   fail: err => {
          //     console.error(err);
          //   },
          // });

          // 可获取图片大小size和摘要digest信息
          uni.getFileInfo({
            filePath: res.tempFilePaths[0],
            success: file => {
              console.log('uni.getFileInfo', file);
            },
            fail: err => {
              console.error(err);
            },
          });
        },
      });
    },
    scanCode() {
      // 允许从相机和相册扫码
      uni.scanCode({
        success: res => {
          console.log('条码类型：' + res.scanType);
          console.log('条码内容：' + res.result);
        },
      });

      // 只允许通过相机扫码
      // uni.scanCode({
      //   onlyFromCamera: true,
      //   success: => (res) {
      //     console.log('条码类型：' + res.scanType);
      //     console.log('条码内容：' + res.result);
      //   },
      // });

      // 调起条码扫描
      // uni.scanCode({
      //   scanType: ['barCode'],
      //   success: => (res) {
      //     console.log('条码类型：' + res.scanType);
      //     console.log('条码内容：' + res.result);
      //   },
      // });
    },
    debounce() {
      console.log('uni.$petro.Utils.debounce');
      uni.$petro.Utils.debounce(
          () => {
            console.log('debounce');
          },
          2000,
          this,
      );
    },
    // 轮询微任务
    onTogglePollTask() {
      if (this.pollingTaskId) {
        clearInterval(this.pollingTaskId);
        console.log('停止轮询', this.pollingTaskId);
        this.pollingTaskId = 0;
        return;
      }
      this.pollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
        const { success, data } = await uni.$petro.http(
            'oilstation.station.list.h5',
            {
              bookingRefueling: '',
              distance: '6',
              latitude: 40.**************,
              longitude: 116.**************,
              mapType: '1',
              orgCode: '110114',
              pageNum: 1,
              pageSize: 10,
            },
            {
              ignoreErrs: true,
              showLoading: false,
            },
        );
        console.log('轮询中', success, data);
        return success === 1; // 为true时停止轮询，否则继续轮询
      }, 5000);
      console.log('pollingTaskId', this.pollingTaskId);
    },
    // 并发唯一Promise任务
    async promiseTask() {
      const task = [
        uni.$petro.http,
        'oilstation.station.list.h5',
        {
          bookingRefueling: '',
          distance: '6',
          latitude: 40.**************,
          longitude: 116.**************,
          mapType: '1',
          orgCode: '110114',
          pageNum: 1,
          pageSize: 10,
        },
        {
          ignoreErrs: true,
          showLoading: false,
        },
      ];
      // 单个多次调用
      const res = await uni.$petro.Utils.createPromiseTask(task);
      console.log(res);

      // 并发多次调用
      // const [{ success, data }, res2, res3] = await Promise.all([
      //   uni.$petro.Utils.createPromiseTask(task), // 不传key，则自动使用第一个参数作为key，默认为网关接口string
      //   uni.$petro.Utils.createPromiseTask(task),
      //   // uni.$petro.Utils.createPromiseTask(task, 'test', 0),
      // ]);
      // console.log(success, data, res2, res3);
    },
    /**
     * 模拟框
     * @supported petro-soti/foudation-zyzx@^1.1.0-1000
     * @returns {Promise<void>}
     */
    async onModal() {
      console.log('onModal');
      uni.$petro.showModal({
        layout: true,
        title: 'title layout',
        content: 'content layout',
        showCancelButton: true,
        cancel() {
          console.log('cancel');
        },
      });
      // this.$store.commit('petroShowModal', {
      //   title: 'title11',
      //   content: 'content11',
      //   showCancelButton: true,
      //   cancel() {
      //     console.log('cancel1111');
      //   },
      // });
      // this.$store.commit('petroShowModal', {
      //   title: 'title22',
      //   content: 'content22',
      //   showCancelButton: true,
      //   cancel() {
      //     console.log('cancel2222');
      //   },
      // });
    },
    // 好客e站真机协议 - 测试用例
    async onHkyzCall(item) {
      console.warn('仅mPaaS真机支持输出!!!', item.click, item.title);
      const result = await uni.$petro.Bridge.hkyz[item.click].call(uni.$petro.Bridge.hkyz, item.data || {});
      console.log(result);
      uni.showModal({
        title: item.click,
        content: JSON.stringify(result),
      });
    },
    // 中油智行真机协议 - 测试用例
    async onZyzxCall(item) {
      console.warn('仅mPaaS真机支持输出!!!', item.click, item.title);
      const result = await uni.$petro.Bridge.zyzx[item.click].call(uni.$petro.Bridge.zyzx, item.data || {});
      console.log(result);
      uni.showModal({
        title: item.click,
        content: JSON.stringify(result),
        success: res => {
          if (res.confirm) {
            if (item?.click == 'getTokenInfo') {
              uni.setClipboardData({
                data: JSON.stringify(result),
              });
            }
          } else if (res.cancel) {
          }
        },
      });
    },
    async onOpenKeyboardForSecurityPlugin() {
      const accountPlugin = requirePlugin('accountPlugin');
      console.log('accountPlugin', accountPlugin);
      return;

      (await uni.$petro.AccountPlugin.getSecurityPluginInstance()).openKeyboard(
          {
            keyboardType: 'number',
            passwordType: 'payment',
            numberPassword: '6',
            letterPassword: [8, 20],
            regular: 'regular',
            setText: '支付',
            passwordInputShow: 0,
          },
          res => {
            console.log(3333, res);
          },
          res => {
            console.log(4444, res);
          },
      );

      return;

      await uni.$petro.AccountPlugin.init();
      console.log(1111, uni.$petro.AccountPlugin);
      const { securityPluginInstance } = uni.$petro.AccountPlugin;
      // const securityPlugin = requirePlugin('securityPlugin');
      //
      // const securityPluginInstance = securityPlugin.passwordInstance.createComponent();
      // console.log(1111, securityPlugin, securityPluginInstance);
      // const { accessToken, gsmsToken } = await uni.$petro.getTokenInfo();
      // const {
      //   clientCode,
      //   env,
      //   mPaas: { appid },
      // } = uni.$petro.config;
      //
      // const params = {
      //   appid: appid,
      //   terminalId: new Date().getTime().toString(),
      //   token: gsmsToken,
      //   baseType: env,
      //   clientCode,
      //   platform: 'SF',
      // };
      // console.log(JSON.stringify(params));
      //
      // securityPluginInstance.init(params);

      securityPluginInstance.openKeyboard(
          {
            keyboardType: 'number',
            passwordType: 'payment',
            numberPassword: '6',
            letterPassword: [8, 20],
            regular: 'regular',
            setText: '支付',
            passwordInputShow: 0,
          },
          res => {
            console.log(1111, res);
          },
          res => {
            console.log(2222, res);
          },
      );
    },
    async onPayPlugin() {
      try {
        const { accessToken, gsmsToken } = await uni.$petro.getTokenInfo();
        const {
          clientCode,
          env,
          http: { v3sign },
          mPaas: { appid },
        } = uni.$petro.config;

        const payPlugin = requirePlugin('payPlugin');
        const params = {
          token: gsmsToken,
          accessToken,
          baseType: env,
          clientCode,
          platform: 'SF',
          sign: v3sign,
        };
        console.log('onPayPlugin', JSON.stringify(params), payPlugin);
        const res = await payPlugin.InitPay(params);
        console.log(res);
        const res2 = await payPlugin.CheckStatus();
        console.log(res2);

        // return;

        // 预下单
        const preParams = {
          bizOrderNo: '2405101950000005175346838',
          stationCode: '1-A4301-C001-S006',
          amount: '10',
          extendFiled:
              '{"dfp":"Uzr8QmrDNs2_6yeeyB4aAUxq8Qmj0Lv7gro4vApnTxwKk1cALKIJILrot6nXP3T2wDtKNsiiIwEswLJx9UBh-5O9HvTUYKM1pRRObzfZr17ziigQ1o82WkDRkKehD_uRETHcGNxSNIGTt_LQ2HWj8ngNhvUEHZEH","gps":"(116.244782,40.172438)","gpsProvince":"北京市","gpsCity":"北京市","gpsArea":"昌平区","sourceChan":"2","operIp":""}',
          accountPreType: '3',
          payType: '5',
          accountNo: '************',
          // unitMainAccountNo: '************',
        };

        // const QryPreOrderRes = await payPlugin.QryPreOrder(preParams, await uni.$petro.AccountPlugin.getSecurityPluginInstance());
        // console.log(QryPreOrderRes);

        const unitQryPreOrderRes = await payPlugin.UnitQryPreOrder(preParams, await uni.$petro.AccountPlugin.getSecurityPluginInstance());
        console.log(unitQryPreOrderRes);
        return;

        // 收银台
        const GetRechargeTypeListParams = {
          areaCode: '1-A1103-C008',
          amount: 30,
        };

        const GetRechargeTypeListRes = await payPlugin.GetRechargeTypeList(GetRechargeTypeListParams);
        console.log(GetRechargeTypeListRes);
      } catch (err) {
        console.error(err);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-demo {
  background: #fff;
}

.cell-box-title {
  padding: 10px;
  background: #eee;
  font-weight: bold;
}

.cell-group-item {
  padding: 10px;
  border-bottom: 1px solid #eee;

  &:active {
    background-color: #f5f5f5;
  }
}

.cover {
  width: 100%;
}

.logo {
  width: 100px;
  margin: 12px auto;
  display: inherit;
}

.container {
  padding: 0 0 50px;
}
</style>
