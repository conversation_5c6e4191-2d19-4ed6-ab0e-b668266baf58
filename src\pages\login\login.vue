<template>
  <view class="page-login">
    <view class="loading" v-show="isLoading">
      <img class="loading-img" src="@/static/icon-loading.gif" />
      <view class="loading-value">正在努力加载中...</view>
    </view>

    <!-- 三方登录 -->
    <p-login-token v-if="isAgree && query.token" :query="query" :locationInfo="locationInfo"></p-login-token>

    <!-- 协议页面 -->
    <p-agreement @agree="onAgree" v-if="!isAgree"></p-agreement>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisible">
      <p-slider-verify
        :show="sliderVisible"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>
  </view>
</template>

<script>
import pAgreement from '@/components/p-agreement/p-agreement.vue';
import PLoginToken from '@/components/p-login-token/p-login-token.vue';

export default {
  name: 'page-login',
  components: { PLoginToken, pAgreement },
  props: {
    queryParams: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isLoading: true, // 是否加载中
      isAgree: true, // 是否同意协议
      saveInfo: {}, // 存储信息
      roleList: [], // 当前登录账号角色列表
      role: {}, // 当前登录账号默认角色
      sliderVisible: false, // 滑块验证是否显示
      mobile: '', // 手机号
      messageType: '3', // 1-用户注册 2-忘记密码 3-登录 4-换设备验证
      tempCode: '', // 临时验证码
      authStatus: 0, // 换设备校验状态:0-通过 1-短信验证 2-人脸验证
      locationInfo: {}, // 位置信息,
      isLocation: true, // 是否获取位置信息
      query: {},
      debugList: [
        {
          label: 'demo页',
          value: 'demo',
        },
      ],
    };
  },
  onLoad(query) {
    this.query = { ...this.queryParams, ...(query || {}) };
  },
  async mounted() {
    this.query = { ...this.queryParams };
    this.init();
  },
  onShow() {
    if (!this.isLocation) {
      console.log('onShow', this.locationInfo);
      this.getLocation(false);
    }
  },
  methods: {
    // 初始化
    async init() {
      try {
        const res = await uni.$petro.getTokenInfo();
        console.log('getTokenInfo', res);
        this.saveInfo = res || {};
        // 是否已登录
        if (!!res?.gsmsToken) {
          return;
        }

        // 是否同意协议
        this.isAgree = !!res?.isAgreePrivacy;
        this.isLoading = false;
        if (this.isAgree) {
          this.getLocation();
        }
      } catch (e) {
        this.isAgree = false;
        this.isLoading = false;
      }
    },
    // 同意协议
    onAgree() {
      this.isAgree = true;
      this.getLocation();
    },
    // 获取位置
    async getLocation(refresh = true) {
      let opt = {};
      if (!refresh) {
        opt.showModal = false;
      } else {
        // 获取用户定位权限
        let result = await uni.$petro.preAuthPermissions({
          scopes: ['gps'],
        });
        console.log('preAuthPermissions', result);
      }
      const res = await uni.$petro.getLocation({}, true, opt);
      console.log('getLocation', res);
      this.locationInfo = res || {};
      if (!res?.address) {
        this.isLocation = !refresh;
      }
    },
    // 跳转验证码页面
    toVerification() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/verification/verification',
          params: { mobile: this.mobile, messageType: this.messageType, tempCode: this.tempCode },
          type: 'navigateTo',
        });
      }, 500);
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisible = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.toVerification();
    },
    // 滑块验证关闭
    onSliderClose() {
      this.sliderVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.loading {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 998;

  &-img {
    width: 560rpx;
    height: 560rpx;
  }

  &-value {
    margin-top: 48rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 44rpx;
  }
}
</style>
