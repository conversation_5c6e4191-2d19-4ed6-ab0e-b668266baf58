<template>
  <view class="page-mine">
    <div class="container">
      <petro-layout ref="layout">
        <zyzx-page-mine :data="list" @onSelected="onSelected" :menu="true" :menu-list="COMPANY_BAR_MENU"></zyzx-page-mine>
      </petro-layout>
    </div>
  </view>
</template>

<script>
import { COMPANY_BAR_MENU } from '@/services/enum';

export default {
  components: {},
  computed: {},
  data() {
    return {
      list: [
        {
          type: 'route',
          icon: '/static/images/icon-mine-menu-01.png',
          text: '企业信息',
          desc: '',
          arrow: true,
          url: '/pages/mine/pages/enterprise-page',
        },
        {
          type: 'route',
          icon: '/static/images/icon-mine-menu-02.png',
          text: '个人信息',
          desc: '',
          arrow: true,
          url: '/pages/mine/pages/personal-page',
        },
        // {
        //   type: 'route',
        //   icon: '/static/images/icon-mine-menu-04.png',
        //   text: '切换身份',
        //   desc: '',
        //   arrow: true,
        //   url: '/pages/roles/roles',
        // },
        // {
        //   type: 'route',
        //   icon: '/static/images/icon-mine-menu-05.png',
        //   text: '账号与安全',
        //   desc: '',
        //   arrow: true,
        //   url: '/pages/mine/pages/account-security-page',
        // },
        // {
        //   type: 'cleanCache',
        //   icon: '/static/images/icon-mine-menu-06.png',
        //   text: '清理缓存',
        //   desc: '',
        //   arrow: false,
        //   url: '',
        // },
        {
          type: 'version',
          icon: '/static/images/icon-mine-menu-07.png',
          text: '版本号',
          desc: '',
          arrow: false,
          url: '',
        },
        {
          type: 'route',
          icon: '/static/images/icon-mine-menu-08.png',
          text: '关于我们',
          desc: '',
          arrow: true,
          url: '/pages/mine/pages/about-page',
        },
      ],
      COMPANY_BAR_MENU,
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
  },
  onShow() {},
  mounted() {},
  methods: {
    async onSelected(v) {},
  },
};
</script>

<style lang="scss" scoped>
.page-mine {
  height: 100%;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    overflow: hidden;
  }
}
</style>
