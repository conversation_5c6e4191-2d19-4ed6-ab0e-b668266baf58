<template>
  <div class="page-none">
    <u-navbar :title="' '" :autoBack="true" :placeholder="false" :bgColor="'transparent'"></u-navbar>

    <img class="bg" src="@/static/bg-header.png" alt="" />
    <img class="noData" src="@/static/icon-naData.png" alt="" />
    <view class="title">暂未收到入驻邀请</view>
    <view class="subTitle">详情请联系你的车队管理人咨询</view>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  computed: {},
  onLoad() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.page-none {
  width: 100%;
  background: #fff;
  text-align: center;

  .bg {
    width: 100%;
    height: 372rpx;
  }

  .noData {
    width: 560rpx;
    height: 436rpx;
    margin: 28rpx auto 48rpx;
    display: block;
  }

  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
  }

  .subTitle {
    margin-top: 16rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 44rpx;
  }
}
</style>
