<template>
  <div class="page-wallet">
    <petro-layout ref="layout" :petroKeyboard="true">
      <div class="wallet">
        <!-- 钱包基础信息  -->
        <zyzx-page-car-wallet @viewRestrictions="viewRestrictions" v-if="carWalletData.mainAccountNo" :carWalletData="carWalletData" />
        <!-- 账单列表 -->
        <div class="bill">
          <zyzx-page-bill
            ref="zyzxPageBillRef"
            :isShowBar="true"
            v-if="carWalletData.mainAccountNo"
            :isLicensePlate="carWalletData.licencePlate"
            :mainAccountNo="carWalletData.mainAccountNo"
            :enterpriseAccountNo="carWalletData.enterpriseAccountNo"
            :type="carWalletData.accountType"
          />
        </div>
      </div>
    </petro-layout>
  </div>
</template>

<script>
import zyzxPageCarWallet from '@/components/zyzx-page-car-wallet/zyzx-page-car-wallet.vue';
// import zyzxPageBill from '@/components/zyzx-page-bill/zyzx-page-bill.vue';
import { mapState } from 'vuex';

export default {
  name: 'car-wallet-page',
  components: {
    zyzxPageCarWallet,
    // zyzxPageBill
  },
  data() {
    return {
      carWalletData: {},
    };
  },
  watch: {
    'carWalletData.mainAccountNo': {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.$nextTick(() => {
            this.$refs.zyzxPageBillRef?.getBillList(true);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  onLoad(query) {
    this.carWalletData = JSON.parse(query.data)?.carWalletData;
  },
  onShow() {},
  computed: {
    ...mapState({
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
  },
  created() {},
  mounted() {},
  methods: {
    viewRestrictions() {
      uni.$petro.route('/pages/wallet/pages/set-up-page');
    },
  },
};
</script>

<style scoped lang="scss">
.page-wallet {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
.wallet {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bill {
  flex: 1;
  overflow: scroll;
}
</style>
