<template>
  <div class="page-container">
    <u-navbar :title="'订单'" :autoBack="false" :leftIconColor="'transparent'" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div class="page-order">
          <div class="header">
            <div class="tab-list">
              <div
                class="tab-item"
                :class="{ active: tab.value == tabActive }"
                v-for="(tab, k) in tabList"
                :key="k"
                @click="tabActiveChange(tab)"
                >{{ tab.name }}
              </div>
            </div>
            <div class="filter-row">
              <div class="filter-row-left" @click="screenPupupOpen()">
                <image src="@/static/images/icon-order-filter.png"></image>
                <span>筛选</span>
              </div>
            </div>
          </div>
          <div class="content">
            <zyzx-data-list
              ref="dataList"
              :safeAreaInsetBottom="true"
              :showEmpty="showEmpty"
              :emptyImage="require('@/static/images/empty-order-list.png')"
              emptyText="未查询到订单"
              :widthImg="180"
              :heightImg="120"
              @refreshPullDown="refreshPullDown"
              @scrolltolower="scrolltolower"
              :isLoad="!!(dataList && dataList.length)"
            >
              <div class="order-list">
                <!-- 非已退款订单 -->
                <template v-if="tabActive != 99">
                  <div class="order-item" v-for="(item, i) in dataList" :key="i" @click="toDetail(item)">
                    <div class="detail-top">
                      <div class="name">
                        <!-- <image src="@/static/images/order-cheer.png" class="name-img" /> -->
                        <div class="title text-overflow">{{ item.stationName }}</div>
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                      <div class="status" :class="{ gray: item.orderStatus == 5 }">{{ ORDER_STATUS[item.orderStatus] }}</div>
                    </div>
                    <div class="detail-content" v-for="(goodItem, idx) in item.orderItemList" :key="idx">
                      <div class="detail-left">
                        <!-- <div class="detail-left-text" v-if="true || goodItem.productType === 1">
                        <span>92</span>
                        <span>#</span>
                      </div> -->
                        <image class="detail-left-img" v-if="goodItem.productType == 1" src="@/static/images/order-oil-image.png" />
                        <image class="detail-left-img" v-else-if="goodItem.productType == 2 && goodItem.imgUrl" :src="goodItem.imgUrl" />
                        <div class="detail-left-img placeholder" v-else></div>

                        <div class="order-name">
                          <div class="name">{{ goodItem.productName }}</div>
                          <div class="type">
                            <!-- <span>{{SUB_ORDER_TYPE[item.orderSubType]}}</span> -->
                          </div>
                        </div>
                      </div>
                      <div class="detail-price">
                        <div class="unitPrice">&yen;{{ goodItem.unitPrice }}</div>
                        <div class="litre">{{ 'x ' + goodItem.productQty + goodItem.productUnit }}</div>
                      </div>
                    </div>
                    <div class="payment-row">
                      <div></div>
                      <div class="amount">
                        <div>实付总额：</div>
                        <div>&yen;</div>
                        <div>{{ item.actualPayTotalAmount || 0 }}</div>
                      </div>
                    </div>
                    <div class="relevant-info">
                      <div class="info-item" v-if="[2, 3].includes(tokenInfo.fleetType)">
                        <div class="info-item-left">BD装备卡</div>
                        <div class="info-item-right">{{ item.equipmentCardNo }}</div>
                      </div>
                      <div class="info-item" v-if="[2, 3].includes(tokenInfo.fleetType)">
                        <div class="info-item-left">当前里程</div>
                        <div class="info-item-right">{{ item.mileage || '' }}km</div>
                      </div>
                      <div class="info-item">
                        <div class="info-item-left">支付方式</div>
                        <div class="info-item-right">{{ ORDER_SUBTYPE[item.orderSubType] }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-item-left">车牌信息</div>
                        <div class="info-item-right">{{ item.licensePlate }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-item-left">订单时间</div>
                        <div class="info-item-right">{{ item.createTime }}</div>
                      </div>
                    </div>

                    <!-- <div class="time-row">
                    <div class="car-num">{{ item.licensePate }}</div>
                    <div class="time">{{ item.createTime }}</div>
                  </div> -->
                    <!-- <div class="detail-bottom">
                    <div class="payment-status">
                      <div class="time">
                        <div>00:01:50</div>
                        <div>后自动取消</div>
                      </div>
                      <button class="btn" size="mini" @click.stop="tabActiveChange()">立即支付</button>
                    </div>
                  </div>-->
                  </div>
                </template>
                <!-- 已退款订单 -->
                <template v-else>
                  <div class="order-item" v-for="(item, i) in dataList" :key="i" @click="toReturnDetail(item)">
                    <div class="detail-top">
                      <div class="name">
                        <div class="title text-overflow">{{ item.stationName }}</div>
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                      <div class="status">已退款</div>
                    </div>
                    <div class="detail-content" v-for="(goodItem, idx) in item.orderItems" :key="idx">
                      <div class="detail-left">
                        <image class="detail-left-img" v-if="goodItem.imgUrl" :src="goodItem.imgUrl" />
                        <div class="detail-left-img placeholder" v-else></div>

                        <div class="order-name">
                          <div class="name">{{ goodItem.productName }}</div>
                          <div class="type">
                            <!-- <span>{{SUB_ORDER_TYPE[item.orderSubType]}}</span> -->
                          </div>
                        </div>
                      </div>
                      <div class="detail-price">
                        <div class="unitPrice">&yen;{{ goodItem.unitPrice }}</div>
                        <div class="litre">{{ 'x ' + goodItem.productQty + goodItem.productUnit }}</div>
                      </div>
                    </div>
                    <div class="payment-row">
                      <div></div>
                      <div class="amount">
                        <div>实退金额：</div>
                        <div>&yen;</div>
                        <div>{{ item.actualTotalRefundAmount || 0 }}</div>
                      </div>
                    </div>
                    <div class="relevant-info">
                      <div class="info-item">
                        <div class="info-item-left">车牌信息</div>
                        <div class="info-item-right">{{ item.licensePlate }}</div>
                      </div>
                      <div class="info-item">
                        <div class="info-item-left">退款时间</div>
                        <div class="info-item-right">{{ item.createTime }}</div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
              <!-- <template v-slot:empty>
                <u-empty
                  :icon="require('@/static/images/empty-order-list.png')"
                  marginTop="0"
                  textSize="12"
                  textColor="#999999"
                  width="174"
                  height="119"
                  text="未查询到订单"
                >
                </u-empty>
              </template> -->
            </zyzx-data-list>

            <!-- 筛选弹窗 -->
            <u-popup :show="showScreenPopup" mode="bottom" bgColor="#fff" @close="screenPupupClose" :round="16">
              <view class="screen-popup">
                <div class="close-wrap">
                  <div></div>
                  <div>筛选</div>
                  <u-icon name="close" color="#333333" size="18" @click="screenPupupClose"></u-icon>
                </div>
                <div class="screen-date">消费时间</div>
                <div class="date-content">
                  <div
                    v-for="(item, index) in dateList"
                    :key="index"
                    class="date-item"
                    :class="{ active: dateActive === item.id }"
                    @click="handelIsdateActive(index, item.id)"
                    >{{ item.name }}
                  </div>
                </div>
                <div class="calendar-date">
                  <div class="picker">
                    <picker
                      mode="date"
                      :value="timeObj.startTime"
                      :start="timeRangeStart"
                      :end="timeRangeEnd"
                      @change="bindStartDateChange"
                      fields="day"
                    >
                      <div class="picker-content">
                        {{ timeObj.startTime }}
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                    </picker>
                  </div>
                  <div class="line">-</div>
                  <div class="picker">
                    <picker
                      mode="date"
                      :value="timeObj.endTime"
                      :start="timeRangeStart"
                      :end="timeRangeEnd"
                      @change="bindEndDateChange"
                      fields="day"
                    >
                      <div class="picker-content">
                        {{ timeObj.endTime }}
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                    </picker>
                  </div>
                </div>
                <div class="tips">可查询近2年内的消费订单</div>
                <div class="order-category">订单品类</div>
                <div class="category-content">
                  <div
                    v-for="(item, index) in orderCategoryList"
                    :key="index"
                    class="date-item"
                    :class="{ active: orderCategoryActive === item.id, 'width-data-item': index == 2 }"
                    @click="handelOrderCategoryActive(index, item.id)"
                    >{{ item.name }}
                  </div>
                </div>
                <div class="btn-row">
                  <button class="custom-btn-block red-plain circle" type="default" @click="dateReset()">重置</button>
                  <button class="custom-btn-block red circle" type="default" @click="dateConfirm()">确认</button>
                </div>
              </view>
            </u-popup>
          </div>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getOrderListApi, getRuturnOrderListApi } from '@/services/http';

import { SUB_ORDER_TYPE, ORDER_SUBTYPE, ORDER_STATUS } from '@/services/enum';

export default {
  name: 'page-order',
  components: {},
  props: {},
  data() {
    return {
      SUB_ORDER_TYPE: SUB_ORDER_TYPE,
      ORDER_SUBTYPE: ORDER_SUBTYPE,
      ORDER_STATUS: ORDER_STATUS,
      showScreenPopup: false,
      tabActive: '',
      tabList: [
        {
          name: '全部',
          value: '',
        },
        // {
        //   name: '待支付',
        //   value: 1,
        // },
        {
          name: '已完成',
          value: 4,
        },
        {
          name: '已取消',
          value: 5,
        },
        {
          name: '已退款',
          value: 99,
        },
      ],
      pageNum: 1,
      pageSize: 10,
      dataList: [],
      showEmpty: false,
      // 时间绑定值
      dateActive: null,
      // 消费订单时间筛选按钮配置数组
      dateList: [
        { name: '近1个月', id: 1 },
        { name: '近3个月', id: 3 },
        { name: '近6个月', id: 6 },
        { name: '近1年', id: 12 },
      ],
      // 时间筛选范围绑定值
      timeObj: {
        startTime: '',
        endTime: '',
      },
      // 消费订单时间筛选范围限制开始时间
      timeRangeStart: '',
      // 消费订单时间筛选范围限制结束时间
      timeRangeEnd: '',
      // 订单品类绑定值
      orderCategoryActive: '',
      // 订单品类配置数组
      orderCategoryList: [
        { name: '油品', id: '3100' },
        { name: '非油品', id: '3150' },
        { name: '油品+非油品混合', id: '33' },
      ],
    };
  },
  watch: {
    tabBarValue: {
      handler(newValue, oldValue) {
        if (newValue === 'order') {
          this.getList(true);
        }
      },
    },
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      companyInfo: state => state?.company?.companyInfo,
      userInfo: state => state?.roles?.userInfo,
      accountInfo: state => state?.company?.accountInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
      tabBarValue: state => state?.tabBar?.tabBarValue,
    }),
  },
  mounted() {
    // 执行初始化操作
    this.init();
  },
  methods: {
    // 初始化操作
    init() {
      this.dateReset();
      this.getList(true);
    },
    // 获取列表
    async getList(reload = false) {
      if (reload) {
        this.pageNum = 1;
        this.dataList = [];
        this.$refs.dataList.loadStatus = 'loading';
      }
      try {
        let params = null;
        let res = null;
        if (this.tabActive != 99) {
          params = {
            staffNo: this.userInfo?.staffNo, // 单位员工编号
            orderStatus: this.tabActive,
            businessNo: this.role?.businessNo,
            enterpriseNo: this.companyInfo?.enterpriseNo,
            // memberType: 2, // 1(个人会员)、2(单位会员) 固定传2
            startTime: this.timeObj.startTime + ' 00:00:00',
            endTime: this.timeObj.endTime + ' 23:59:59',
            orderProductType: this.orderCategoryActive,
            pageSize: this.pageSize,
            pageNum: this.pageNum,
            // v430新增
            enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
            // mainAccountNo: this.accountInfo?.mainAccountNo, // 暂时不传
            // petroChinaNo: this.userInfo?.petroChinaNo, // 暂时不传
            // v440
            showArmyCard: [2].includes(this.tokenInfo.fleetType), // 是否展示军队卡里程数和装备卡号
            // v511 用户类型 1、单位管理员 2、单位司机
            userType: '2',
          };
          res = await getOrderListApi(params);
        } else {
          // 已退款订单
          params = {
            staffNo: this.userInfo?.staffNo, // 单位员工编号
            startTime: this.timeObj.startTime + ' 00:00:00',
            endTime: this.timeObj.endTime + ' 23:59:59',
            pageSize: this.pageSize,
            pageNum: this.pageNum,
          };
          res = await getRuturnOrderListApi(params);
        }
        console.log('111', params);
        console.log('222', data);
        const { success, data } = res;

        if (!success) return;
        const newList = data?.rows || [];

        // 当前为第一页则直接赋值, 否则合并新数据
        if (this.pageNum === 1) {
          this.dataList = newList;
        } else {
          this.dataList = this.dataList.concat(newList);
        }

        // 当前页大于等于总页数,则没有更多了。禁用上拉加载更多
        if (this.pageNum >= data.pageSum) {
          this.$refs.dataList.loadStatus = 'nomore';
        } else {
          this.$refs.dataList.loadStatus = 'contentdown';
        }

        // 是否展示无数据标识
        if (this.dataList.length == 0) {
          this.showEmpty = true;
        } else {
          this.showEmpty = false;
        }
        this.pageNum++;
      } catch (error) {
        console.log(error);
      }
    },
    // 订单品类选择事件
    handelOrderCategoryActive(index, id) {
      this.orderCategoryActive = id;
    },
    // 处理时间筛选按钮和时间范围的交互，处理时间格式
    handelIsdateActive(index, number) {
      this.dateActive = number;
      let date = new Date();
      let year = date.getFullYear(); // 年
      let month = date.getMonth() + 1; // 月
      let day = date.getDate(); // 日
      this.timeObj.endTime = this.getDate();
      month = month - (number % 12);
      year = year - parseInt(number / 12);
      day = day + 1;
      let lastDay = new Date(year, month, 0).getDate();
      if (day > lastDay) {
        day = 1;
        month += 1;
      }
      if (month > 12) {
        month = 12 - month;
        year += 1;
      }
      if (month <= 0) {
        month = month + 12;
        year = year - 1;
      }
      if (month >= 0 && month <= 9) {
        month = '0' + month;
      }
      if (day >= 0 && day <= 9) {
        day = '0' + day;
      }
      this.timeObj.startTime = year + '-' + month + '-' + day;
    },
    // 获取并处理当前时间
    getDate(type) {
      const date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();

      if (type === 'start') {
        year = year - 2;
        day += 1;
      } else if (type === 'end') {
      }
      var lastDay = new Date(year, month, 0).getDate();
      if (day > lastDay) {
        day = lastDay;
        month += 1;
      }
      if (month > 12) {
        year + 1;
      }
      month = month > 9 ? month : '0' + month;
      day = day > 9 ? day : '0' + day;
      return `${year}-${month}-${day}`;
    },
    // 确认筛选
    dateConfirm() {
      this.getList(true);
      this.showScreenPopup = false;
    },
    // 重置筛选
    dateReset() {
      // 默认时间两年
      this.handelIsdateActive(null, 24);
      // 初始化开始结束时间
      this.timeRangeStart = this.getDate('start');
      this.timeRangeEnd = this.getDate('end');
      this.orderCategoryActive = '';
    },
    // 日期选择开始时间改变事件，重置时间按钮重置
    bindStartDateChange(e) {
      this.timeObj.startTime = e.target.value.replace(/\//g, '-');
    },
    // 日期选择结束时间改变事件，重置时间按钮重置
    bindEndDateChange(e) {
      this.timeObj.endTime = e.target.value.replace(/\//g, '-');
    },
    // 打开筛选弹窗
    screenPupupOpen() {
      this.showScreenPopup = true;
    },
    // 关闭筛选弹窗
    screenPupupClose() {
      this.showScreenPopup = false;
    },
    // tab切换
    tabActiveChange(tab) {
      this.tabActive = tab.value;
      this.getList(true);
    },
    // 跳转详情
    toDetail(v) {
      uni.$petro.route('/pages/order-detail/order-detail', {
        orderNo: v?.orderNo,
        stationCode: v?.stationCode,
        orderSubType: v?.orderSubType,
      });
    },
    toReturnDetail(v) {
      uni.$petro.route('/pages/order-detail/order-return-detail', {
        orderNo: v?.returnOrderNo,
      });
    },
    // 下拉刷新触发
    async refreshPullDown() {
      console.log('下拉刷新触发....');
      this.$refs.dataList.loadStatus = 'loading';
      await this.getList(true);
      this.$refs.dataList.stopRefresh();
    },
    // 上拉加载更多
    scrolltolower() {
      console.log('上拉加载更多....');
      // 可加载状态
      if (this.$refs.dataList.loadStatus === 'contentdown') {
        this.$refs.dataList.loadStatus = 'loading';
        this.getList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden;
  }
}

.page-order {
  background: #f7f7fb;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    flex-direction: column;
    background: #ffffff;

    .tab-wrap {
      padding: 0 30rpx;
      display: flex;
      justify-content: center;
      border-bottom: 2rpx solid #eee;
    }

    .tab-list {
      display: flex;
      padding: 10rpx 80rpx 0;

      justify-content: space-between;
      border-bottom: 2rpx solid #eee;

      .tab-item {
        height: 70rpx;
        line-height: 70rpx;
        font-size: 24rpx;
        color: #666666;
        padding-bottom: 4rpx;
        position: relative;

        &.active {
          font-weight: bold;
          transform: scale(1.05);
          font-size: 28rpx;
          color: #333333;
          &::after {
            content: '';
            width: 24rpx;
            height: 4rpx;
            background: #fa1919;
            border-radius: 4rpx;
            position: absolute;
            left: 50%;
            bottom: 0rpx;
            transform: translateX(-50%);
          }
        }
      }
    }

    .filter-row {
      background: #f7f7fb;
      padding: 0rpx 30rpx;
      height: 106rpx;
      display: flex;
      justify-content: space-between;

      &-left {
        display: flex;
        align-items: center;
        &:active {
          opacity: 0.8;
        }

        image {
          width: 27rpx;
          height: 27rpx;
        }

        span {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #333333;
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;

    .order-list {
      padding: 0 32rpx 50rpx;

      .order-item {
        padding: 20rpx 30rpx;
        border-radius: 16rpx;
        background: #ffffff;

        &:not(:first-child) {
          margin-top: 24rpx;
        }

        .detail-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .name {
            display: flex;
            align-items: center;
            overflow: hidden;
            image {
              width: 33rpx;
              height: 32rpx;
              flex-shrink: 0;
            }

            .title {
              margin-left: 8rpx;
              margin-right: 10rpx;
              font-size: 28rpx;
              font-weight: bold;
              color: #333333;
              line-height: 40rpx;
            }
          }

          .status {
            margin-left: 40rpx;
            flex-shrink: 0;
            font-size: 24rpx;
            color: #333;
            &.gray {
              color: #999999;
            }
            &.red {
              color: #fa1919;
            }
          }
        }

        .detail-content {
          margin-top: 16rpx;

          display: flex;
          justify-content: space-between;

          .detail-left {
            margin-right: 20rpx;
            flex: 1;
            display: flex;

            .detail-left-text {
              width: 78rpx;
              height: 78rpx;
              background: #f0f0f0;
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              span:nth-child(1) {
                color: #333333;
                font-size: 32rpx;
                font-weight: bold;
              }
              span:nth-child(2) {
                color: #333333;
                font-size: 20rpx;
                line-height: 20rpx;
                margin-top: -20rpx;
              }
            }

            .detail-left-img {
              width: 78rpx;
              height: 78rpx;
              border-radius: 16rpx;
              &.placeholder {
                background: #f0f0f0;
              }
            }

            .order-name {
              flex: 1;
              padding-top: 4rpx;
              margin-left: 20rpx;
              display: flex;
              flex-direction: column;
              .name {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
              }
              .type {
                margin-top: 6rpx;
                display: flex;
                margin-right: 12rpx;
                span {
                  padding: 0 12rpx;
                  height: 42rpx;
                  line-height: 42rpx;
                  font-size: 22rpx;
                  color: #fa1919;
                  background: #fff3f3;
                  border-radius: 8rpx;
                }
              }
            }
          }

          .detail-price {
            text-align: right;

            .unitPrice {
              margin-top: 4rpx;
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 40rpx;
            }

            .litre {
              font-size: 20rpx;
              font-weight: 400;
              color: #666666;
              line-height: 40rpx;
            }
          }
        }

        .time-row {
          height: 48rpx;
          font-weight: 400;
          line-height: 40rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12rpx;

          .car-num {
            font-size: 26rpx;
            font-weight: bold;
            color: #333333;
            height: 48rpx;
            line-height: 48rpx;
          }
          .time {
            font-size: 26rpx;
            color: #333333;
            height: 48rpx;
            line-height: 48rpx;
          }
        }

        .payment-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12rpx;
          .amount {
            display: flex;
            flex-direction: row;

            div {
              font-size: 28rpx;
              line-height: 44rpx;
            }

            div:nth-child(1) {
              color: #666666;
            }

            div:nth-child(2) {
              color: #ff7033;
              font-size: 24rpx;
              margin-top: 4rpx;
            }

            div:nth-child(3) {
              color: #ff7033;
              font-weight: bold;
              font-size: 32rpx;
            }
          }
        }

        .relevant-info {
          margin-top: 20rpx;
          padding-top: 28rpx;
          border-top: 1rpx dashed #e6e6e6;

          .info-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            &:not(:first-child) {
              margin-top: 18rpx;
            }

            div {
              font-size: 24rpx;
              color: #999999;
            }
          }
        }

        .detail-bottom {
          margin-top: 12rpx;

          .payment-status {
            display: flex;
            justify-content: end;

            .time {
              display: flex;
              align-items: center;

              div {
                font-size: 24rpx;
              }

              div:nth-child(1) {
                color: #e64f22;
              }

              div:nth-child(2) {
                margin-left: 12rpx;
                color: #333;
              }
            }

            .btn {
              padding: 0 12rpx;
              margin-left: 20rpx;
              background: #e64f22;
              color: #ffffff;
              border-color: #e64f22;
              border-radius: 10rpx;
            }
          }
        }
      }

      .more {
        margin-top: 30rpx;
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }

  .screen-popup {
    padding: 50rpx 32rpx 32rpx;

    .close-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
      }
    }

    .screen-date {
      margin-top: 32rpx;
      height: 46rpx;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    .date-content {
      margin-top: 40rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .date-item {
        flex: 1;
        margin-right: 24rpx;
        box-sizing: border-box;
        border: 2rpx solid #f5f6f7;
        height: 60rpx;
        background: #f5f6f7;
        border-radius: 16rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;

        &:nth-of-type(4n) {
          margin-right: 0;
        }
      }
    }

    .calendar-date {
      display: flex;
      align-items: center;
      margin-top: 34rpx;
      justify-content: space-between;

      .line {
        width: 60rpx;
        text-align: center;
      }

      .picker {
        width: calc((100% - 60rpx) / 2);
        height: 80rpx;
        background: #f5f6f7;
        border-radius: 16rpx;
        padding: 0 24rpx 0 34rpx;
        box-sizing: border-box;

        .picker-content {
          height: 80rpx;
          line-height: 80rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          text-align: center;
          font-size: 26rpx;
          color: #333333;

          img {
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }

    .tips {
      margin-top: 24rpx;
      font-size: 26rpx;
      color: #999999;
      line-height: 37rpx;
    }

    .order-category {
      margin-top: 32rpx;
      height: 46rpx;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    .category-content {
      margin-top: 24rpx;
      display: flex;
      flex-wrap: wrap;

      .date-item {
        box-sizing: border-box;
        border: 2rpx solid #f7f7fb;
        width: 155rpx;
        height: 60rpx;
        background: #f7f7fb;
        border-radius: 16rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;
        margin-right: 24rpx;
      }

      .width-data-item {
        width: 300rpx;
      }
    }

    .btn-row {
      // margin-top: 44rpx;
      margin-top: 132rpx;
      display: flex;
      justify-content: space-between;

      button {
        width: calc((100% - 24rpx) / 2);
      }
    }

    .active {
      border: 2rpx solid #fa1919 !important;
      background: #ffffff !important;
      color: #fa1919 !important;
    }
  }
}
</style>
