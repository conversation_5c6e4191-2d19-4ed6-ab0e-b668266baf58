<template>
  <div class="page-container">
    <u-navbar :title="'网点导航'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <page-station-list></page-station-list>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import pageStationList from '@/components/page-station-list/page-station-list.vue';

export default {
  components: {
    pageStationList,
  },
  data() {
    return {};
  },
  onLoad(query) {
    if (!uni.$petro.Utils?.isEmptyObject(query)) {
      // uni.showModal({
      //   title: 'onLoad.query',
      //   content: JSON.stringify(query),
      // });
    }
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
