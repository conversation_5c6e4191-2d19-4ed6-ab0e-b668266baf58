// 订单-子订单类型
export const SUB_ORDER_TYPE = {
  11: '预授权加油',
  12: '加油卡预授权加油',
  13: '线上后支付',
};

// 车辆-加注类型
export const FILL_TYPE = {
  1: '汽油',
  2: '柴油',
  3: '氢气',
  4: 'LNG',
  5: 'CNG',
  6: '混合',
  7: '充电',
};

// 车辆-车辆类型
export const CAR_TYPE = {
  1: '乘用车',
  2: '挂车',
  3: '货车',
  4: '客车',
};

// 车辆-加注品号类型
export const FUEL_TYPE = {
  9: '89#汽油',
  10: '90#汽油',
  11: '93#汽油',
  12: '95#汽油',
  13: '97#汽油',
  14: '98#汽油',
  15: '92#汽油',
  50: '-5#柴油',
  51: '0#柴油',
  52: '+5#柴油',
  53: '+10#柴油',
  54: '-10#柴油',
  55: '-15#柴油',
  56: '-20#柴油',
  57: '-30#柴油',
  58: '-35#柴油',
  59: '-50#柴油',
  70: '煤油',
  80: 'S50#生物柴油',
  81: 'S10#生物柴油',
  60: 'CNG',
  61: 'LNG',
  21: '甲醇',
  22: '乙醇',
};

export const ORDER_SUBTYPE = {
  1: '检测用油-不回罐',
  2: '检测用油-回罐',
  3: '自用油',
  4: '自用气',
  11: 'e享加油',
  12: '加油卡预授权加油',
  13: '线上后支付加油',
  // 14: '销售',
  14: '室内付款',
  15: '原始交易补录',
  20: '洗车服务-并行',
  21: '退货订单',
  22: '积分换购',
  23: 'O2O订单-自提',
  26: '异业订单',
  27: '其他',
  28: '检测用气-不回罐',
  29: '检测用气-回罐',
  30: '直销订单-到站提货',
  31: '直销订单-仓库发货',
  32: '参股站销售',
  33: '检测用尿素-不回罐',
  34: '检测用尿素-回罐',
  35: '自用尿素',
  36: 'O2O订单-配送',
  37: '机器人预约加油',
  47: '洗车服务',
};

export const ORDER_STATUS = {
  1: '创建',
  2: '下单失败',
  5: '已取消',
  4: '已完成',
  6: '已终止',
  7: '已失效',
};

export const COMPANY_BAR_MENU = [
  // {
  //   type: 'route',
  //   icon: '/static/images/icon-company-bar-menu-bag.png',
  //   text: '业务开通',
  //   url: '/pages/business-info/business-info',
  // },
  {
    type: 'route',
    icon: '/static/images/icon-company-bar-menu-todo.png',
    text: '待办事项',
    url: '/pages/todo-list/todo-list',
  },
  {
    type: 'scan',
    icon: '/static/images/icon-company-bar-menu-scan.png',
    text: '扫一扫',
  },
  // {
  //   type: 'route',
  //   icon: '',
  //   text: '邀请下级企业',
  //   url: '',
  // },
  // {
  //   type: 'route',
  //   icon: '',
  //   text: '邀请司机',
  //   url: '',
  // },
];
