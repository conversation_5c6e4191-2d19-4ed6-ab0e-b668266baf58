<template>
  <div class="page-flex page-car-wallet-list">
    <div class="container">
      <div class="header">
        <u-navbar
          :title="'我的钱包'"
          :autoBack="false"
          :border="true"
          :leftIconColor="'transparent'"
          :placeholder="true"
          :bgColor="'#fff'"
        ></u-navbar>
      </div>
      <div class="scroll-view">
        <petro-scroll-view
          ref="scrollView"
          style="height: 100%"
          :scroll-y="true"
          :refresher-enabled="false"
          @refresherrefresh="onRefresh"
          :scrolltolower-enabled="false"
          :scrolltolower-bar-enabled="false"
          @scrolltolower="scrolltolower"
          :scroll-top-bar-enable="false"
          :empty="empty"
          :safe-bottom="true"
        >
          <div class="list">
            <div class="li" v-for="(item, k) in driverWalletList" @click="handleToCardDetail(item)">
              <div class="title">
                <div class="name">{{ item.accountType === 2 ? item.userName : item.licencePlate }}</div>
                <div class="no">NO.{{ accountAmount(item.walletAccountList, 'cardNo') }}</div>
              </div>
              <div class="label">总资产(元)</div>
              <div class="amount">{{ accountAmount(item.walletAccountList, 'availableAmount') }}</div>
              <div class="line"></div>
              <div class="info">
                <div class="jf">
                  <div class="label">积分：</div>
                  <div class="value">{{ accountAmount(item.loyaltyAccountList, 'availablePointAmount', 8) }}</div>
                </div>
                <div class="yhj">
                  <div class="label">优惠金：</div>
                  <div class="value">{{ accountAmount(item.loyaltyAccountList, 'availablePointAmount', 13) }}</div>
                </div>
              </div>
            </div>
          </div>
        </petro-scroll-view>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'page-car-wallet-list',
  components: {},
  data() {
    return {
      driverWalletList: [],
      empty: {
        show: false,
        image: '/static/images/empty-car-account-list.png',
        text: '暂无车牌卡信息',
        width: '280rpx',
        height: '218rpx',
      },
    };
  },
  async mounted() {},
  onShow() {},
  computed: {
    ...mapState({
      accountInfo: state => state.company?.accountInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
  },
  watch: {
    'accountInfo.enterpriseAccountNo': {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.getWalletList();
        }
      },
      immediate: true,
    },
    driverWalletList: {
      handler(newVal, oldVal) {
        console.log('driverWalletList', newVal, oldVal);
        if (newVal && newVal !== oldVal) {
        }
      },
      immediate: true,
    },
  },
  methods: {
    scroll(e) {},
    onPulling() {
      console.log('onPulling');
    },
    onAbout() {
      console.log('onAbout');
    },
    onRefresh() {
      console.log('onRefresh');
      if (this._freshing) return;
      this._freshing = true;
      setTimeout(() => {
        this._freshing = false;
        this.getList(true);
        this.$refs.scrollView.stopRefresh();
      }, 1500);
    },
    scrolltolower() {
      console.log('scrolltolower');
      this.getList();
    },
    getList(refresh) {},
    async getWalletList() {
      try {
        const params = {
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
          refresh: true,
          isBd: [2].includes(this.tokenInfo.fleetType),
        };
        this.driverWalletList = (await this.$store.dispatch('getWalletList', { ...params })) || [];
      } catch (err) {
        console.error(err);
      }
    },
    accountAmount(item = [], value, key = 5) {
      const data = (item || []).find(it => key === it.accountType);
      if (data) {
        return data[value] || '0';
      } else {
        return 0;
      }
    },
    handleToCardDetail(v) {
      uni.$petro.route(
        {
          url: '/pages/wallet/pages/car-wallet-page',
          type: 'to',
          params: { carWalletData: v },
        },
        true,
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.page-car-wallet-list {
  height: 100%;
  background-color: #f8f8f8;
  min-height: 100vh;

  > .container {
    height: inherit;
    display: flex;
    flex-direction: column;
  }

  .scroll-view {
    flex: 1;
    height: inherit;
    overflow: auto;
  }

  .list {
    padding: 32rpx;

    .li {
      background: linear-gradient(136deg, #3c61cb 0%, #6780d6 100%);
      border-radius: 32rpx;
      color: #fff;
      padding: 38rpx 32rpx 36rpx;
      margin-bottom: 32rpx;

      &:active {
        opacity: 0.8;
      }
    }

    .label {
      opacity: 0.6;
      font-size: 24rpx;
    }

    .no {
      opacity: 0.75;
    }

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18rpx;
    }

    .name {
      font-size: 32rpx;
    }

    .amount {
      font-size: 60rpx;
      margin: 16rpx 0 18rpx;
    }

    .value {
      font-size: 24rpx;
    }

    .info {
      display: flex;
      align-items: center;

      > div {
        margin-right: 68rpx;
        display: flex;
        align-items: center;
      }
    }

    .line {
      border: 2rpx solid;
      border-image: linear-gradient(90deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 2 2;
      margin-bottom: 18rpx;
    }
  }
}
</style>
