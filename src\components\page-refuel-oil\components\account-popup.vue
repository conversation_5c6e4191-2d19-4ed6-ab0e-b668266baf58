<template>
  <div class="zyzx-account-popup">
    <u-popup :show="showPopup" mode="bottom" bgColor="#fff" @close="close" :round="16" :safeAreaInsetBottom="true">
      <div class="account-popup">
        <div class="close-wrap">
          <div></div>
          <div>选择昆仑e享卡</div>
          <u-icon name="close" color="#333333" size="18" @click="close"></u-icon>
        </div>
        <div class="account-list">
          <div v-if="!driverWalletList.length" style="margin-top: -200rpx">
            <u-empty
              :icon="require('@/static/images/empty-car-account-list.png')"
              marginTop="100"
              textSize="16"
              textColor="#333333"
              width="280"
              height="218"
              text="暂无车牌卡信息"
            >
            </u-empty>
            <p class="empty-text" style="text-align: center; font-size: 28rpx; color: #999; margin-top: 10rpx">如需开通请联系管理员</p>
          </div>
          <div class="account-item-wrap" v-for="(item, i) in driverWalletList" :key="i" @click="onSelect(item)">
            <div class="account-item" :class="{ active: item.mainAccountNo == driverWalletInfo.mainAccountNo }">
              <div class="name-row">
                <div class="name">
                  <span>{{ item.accountType == 2 ? item.userName : item.licencePlate }}-</span>
                  <span class="text-overflow">{{ item.unitAlias }}</span>
                </div>
                <div class="type driver" v-if="item.accountType == 2">司机卡</div>
                <div class="type license" v-if="item.accountType == 4">车牌卡</div>
              </div>
              <div class="amount-row" :class="{ flex: [1, 3].includes(tokenInfo.fleetType) }">
                <div class="li">
                  <div class="label">余额</div>
                  <div class="value">
                    <span>￥</span>
                    {{ accountAmount(item.walletAccountList, 'availableAmount') }}
                  </div>
                </div>
                <div class="li">
                  <div class="label">优惠金</div>
                  <div class="value black">
                    <span>￥</span>
                    {{ accountAmount(item.loyaltyAccountList, 'availablePointAmount', 13) }}
                  </div>
                </div>
                <div class="li">
                  <div class="label">积分</div>
                  <div class="value black">
                    <span>￥</span>
                    {{ accountAmount(item.loyaltyAccountList, 'availablePointAmount', 8) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </u-popup>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'account-popup',
  components: {},
  props: {
    isAdd: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPopup: false,
    };
  },
  computed: {
    ...mapState({
      driverWalletList: state => state?.accountDriver?.driverWalletList,
      driverWalletInfo: state => state?.accountDriver?.driverWalletInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
      accountInfo: state => state.company?.accountInfo,
    }),
  },
  watch: {},
  mounted() {},
  methods: {
    /**
     * 根据账户类型获取账户金额
     *
     * @param item 账户数据数组
     * @param value 需要获取的金额类型，如'availableAmount'
     * @param key 账户类型，默认为5
     * @returns 返回指定账户类型的金额，如果未找到则返回0
     */
    accountAmount(item = [], value, key = 5) {
      const data = (item || []).find(it => key == it.accountType);
      if (data) {
        return data[value] || '0';
      } else {
        return 0;
      }
    },
    async show() {
      if (!this.driverWalletInfo?.mainAccountNo) {
        await this.getWalletInfo();
      }
      this.showPopup = true;
    },
    async getWalletInfo() {
      try {
        // 获取司机账户钱包列表
        const params = {
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
          refresh: true,
          isBd: [2].includes(this.tokenInfo.fleetType),
        };
        await this.$store.dispatch('getWalletList', { ...params });
      } catch (err) {
        console.error(err);
      }
    },
    close() {
      this.showPopup = false;
    },
    async onSelect(v) {
      if (v.mainAccountNo == this.driverWalletInfo.mainAccountNo) return;
      this.$store.commit('setDriverWalletInfo', v);
      this.$emit('onSelect', v);
      this.close();
    },
  },
};
</script>
<style lang="scss" scoped>
.zyzx-account-popup {
  width: 100%;

  .account-popup {
    padding: 50rpx 32rpx 32rpx;

    .close-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
      }
    }

    .account-list {
      margin-top: 72rpx;
      display: flex;
      flex-direction: column;
      max-height: 60vh;
      overflow-x: hidden;
      overflow-y: auto;

      .account-item-wrap {
        &:not(:first-child) {
          .account-item {
            margin-top: 16rpx;
          }
        }

        .account-item {
          padding: 32rpx;
          box-sizing: border-box;
          border-radius: 16rpx;
          border: 2rpx solid #eeeeee;
          overflow: hidden;

          &.active {
            border: 2rpx solid #fa1919;
          }

          .name-row {
            display: flex;
            align-items: center;

            .name {
              display: flex;
              align-items: center;
              overflow: hidden;

              span:nth-child(1) {
                font-weight: bold;
                font-size: 32rpx;
                color: #333333;
                line-height: 44rpx;
                flex-shrink: 0;
              }

              span:nth-child(1) {
                font-weight: 400;
                font-size: 32rpx;
                color: #333333;
                line-height: 44rpx;
              }
            }

            .type {
              flex-shrink: 0;
              margin-left: 16rpx;
              padding: 0 12rpx;
              height: 38rpx;
              line-height: 38rpx;
              background: #f0f4ff;
              border-radius: 4rpx;
              border: 1rpx solid rgba(92, 110, 184, 0.62);
              font-size: 24rpx;
              color: #42b2b2;

              &.driver {
                color: #5c6eb8;
                background: #f0f4ff;
                border: 1rpx solid #5c6eb8;
              }

              &.license {
                color: #42b2b2;
                background: #f0ffff;
                border: 1rpx solid #42b2b2;
              }
            }
          }

          .amount-row {
            margin-top: 20rpx;
            margin-right: 50rpx;

            &.flex {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-right: 0;
            }

            .li {
              display: flex;
              align-items: center;
              justify-content: space-between;
            }

            .label {
              height: 44rpx;
              font-size: 28rpx;
              color: #666666;
              line-height: 44rpx;
              margin-right: 12rpx;
            }

            .value {
              height: 40rpx;
              font-weight: bold;
              font-size: 34rpx;
              color: #ff5500;
              line-height: 40rpx;

              & > span {
                font-size: 20rpx;
              }

              &.black {
                color: #000;
              }
            }
          }
        }
      }
    }
  }
}
</style>
