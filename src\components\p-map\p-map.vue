<template>
  <div class="p-map" :style="{ height: height }" @touchstart.stop @touchend.stop @touchmove.stop>
    <map
      v-if="loadMap && height"
      :id="mapId"
      :style="{
        height: 'calc(100% + 100px)',
        width: '100%',
        zIndex: '99',
        position: 'absolute',
        left: showMap ? '0' : '100%',
        bottom: '-100px',
      }"
      :scale="scale"
      show-location
      :markers="markers"
      :latitude="mapCenterLat"
      :longitude="mapCenterLon"
      :setting="mapSetting"
      :enable-scroll="true"
      :enable-rotate="false"
      :enable-overlooking="false"
      @markertap="onMarkertap"
    >
    </map>
    <image class="icon-location" :class="{ top: iconTop }" src="@/static/images/map-loaction.png" @click="refreshLocation()"></image>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';

export default {
  name: 'p-map',
  props: {
    mapId: {
      type: String,
      default: 'map',
    },
    showMap: {
      type: Boolean,
      default: true,
    },
    height: {
      type: String,
      default: '480rpx',
    },
    iconTop: {
      type: Boolean,
      default: false,
    },
    pageChannel: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loadMap: false,
      scale: 14,
      mapCtx: {},
      // 地图设置开启手指缩放
      mapSetting: {
        // 比例尺
        showScale: 1,
        // 交通路况展示
        trafficEnabled: 0,
        // 地图POI信息
        showMapText: 1,
      },
      center: {
        longitude: uni.$petro.config.defaultGPS.longitude,
        latitude: uni.$petro.config.defaultGPS.latitude,
      },
      markers: [],
    };
  },
  computed: {
    ...mapState({
      stationList: state => state.station.stationList,
      stationInfo: state => state.station.stationInfo,
      mapCenterLat: state => state.station.mapCenterLat,
      mapCenterLon: state => state.station.mapCenterLon,
      tabBarPermissions: state => state.tabBar?.tabBarPermissions,
    }),
    currentTabBar() {
      const tabBar = this.tabBarPermissions[this.pageChannel];
      if (tabBar && !tabBar.localPre) {
        return null;
      }
      return tabBar;
    },
  },
  watch: {
    // 监听油站列表改变了重绘地图
    stationList: {
      immediate: false,
      handler(newValue, oldValue) {
        console.log('stationList changed from', oldValue, 'to', newValue);
        if (newValue) {
          this.setMarkers();
        }
      },
    },

    // 监听油站改变了重绘地图
    stationInfo: {
      immediate: false,
      handler(newValue, oldValue) {
        console.log('stationInfo changed from', oldValue, 'to', newValue);
        if (newValue?.orgCode !== oldValue?.orgCode) {
          this.setCustomCallout(newValue, oldValue);
          // 设置中心点
          if (newValue?.latitude && newValue?.longitude) {
            this.$store.commit('setLocation', {
              latitude: newValue.latitude,
              longitude: newValue.longitude,
            });
          }
        }
      },
    },
  },
  mounted() {
    setTimeout(() => {
      this.initMap();
    }, 300);
  },
  methods: {
    // 地图初始化
    initMap() {
      // 创建map对象
      this.loadMap = true;
      this.mapCtx = uni.createMapContext(this.mapId, this);
      this.setMarkers();
    },
    // 设置地图油站标志
    setMarkers() {
      const list = this.stationList;
      if (!list || list.length === 0) return;
      let markers = [];
      // 油站标记
      console.log('list--', list);
      for (let [index, item] of list.entries()) {
        let i = index;
        if (item.longitude && item.latitude) {
          markers.push(this.createMarker(item));
        }
      }
      this.markers = markers;
      this.setCustomCallout();
    },

    // 设置自定义气泡
    setCustomCallout(newValue, oldValue) {
      try {
        if (oldValue?.orgCode) {
          const oldMarker = this.markers.find(marker => marker.orgCode === oldValue.orgCode);
          if (oldMarker?.customCallout) {
            oldMarker.customCallout.isShow = 0;
          }
        }
        const { orgCode, distanceInKm, orgName } = this.stationInfo;
        let targetMarker = this.markers.find(marker => marker.orgCode === orgCode);
        if (this.stationInfo && !targetMarker) {
          targetMarker = this.createMarker(this.stationInfo);
          this.markers.push(targetMarker);
        }

        // 判断XML的高级定制渲染在IDE中不生效，（my.isIDE是否在支付宝开发者工具）
        if (my.isIDE) {
          targetMarker.customCallout = {
            isShow: 1,
            type: 2,
            descList: [
              {
                desc: `${distanceInKm}km`,
                descColor: '#f47a43',
              },
              {
                desc: orgName,
                descColor: '#333333',
              },
            ],
          };
        } else {
          let imageUrl = '';
          if (this.stationList[0]?.orgCode == this.stationInfo?.orgCode) {
            imageUrl = '/static/images/map-stationClosest.png';
          } else {
            imageUrl = '/static/images/map-station.png';
          }
          const { osName } = uni.$petro.store.systemInfo;
          const src = osName == 'ios' ? '/static/xml/oil-map-ios-mpaas.xml' : '/static/xml/oil-map-android.xml';
          targetMarker.customCallout = {
            isShow: 1,
            layout: {
              params: {
                distance: `${distanceInKm}km`,
                name: orgName,
                image: imageUrl,
              },
              src: src,
            },
            layoutBubble: {
              style: 'bubble',
              bgColor: '#F4F5F4',
              borderRadius: 4,
            },
          };
        }
      } catch (error) {
        console.log(error);
      }
    },

    // 创建marker数据
    createMarker(item) {
      const newStation = {
        id: Number(item.stationId),
        longitude: Number(item.longitude),
        latitude: Number(item.latitude),
        orgCode: item.orgCode,
        width: 46,
        height: 49,
        iconPath: '/static/images/icon-station-navigation-02.png',
        markerLevel: 0,
      };
      return newStation;
    },

    // marker点击事件
    onMarkertap(e) {
      const stationId = Number(e.markerId);
      // 获取marker对象
      const markerInfo = this.markers.find(marker => marker.id == stationId);
      // 获取油站信息
      const stationInfo = this.stationList.find(item => item.stationId == stationId);
      if (!markerInfo || !stationInfo) return;

      // 重置气泡
      for (let i in this.markers) {
        this.markers[i].customCallout = {};
      }
      // 更新当前油站信息
      this.$store.commit('setStationInfo', stationInfo);
    },

    // 刷新位置信息
    refreshLocation() {
      if (!this.currentTabBar) return;
      if (this.currentTabBar?.isDefaultGPS) {
        this.$store.dispatch('getStationList', uni.$petro.config?.defaultGPS || {});
      } else {
        this.$store.dispatch('getStationList');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.p-map {
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
  .icon-location {
    position: absolute;
    right: 32rpx;
    bottom: 72rpx;
    width: 75rpx;
    height: 75rpx;
    z-index: 99;
    &.top {
      top: 72rpx !important;
    }
    &:active {
      opacity: 0.8;
    }
  }
}
</style>
