<template>
  <div class="p-login-token">
    <view class="header">
      <image class="header-bg" src="@/static/login-sf-bg.png"></image>
    </view>

    <view class="container">
      <view class="agree" @click="isAgree = !isAgree">
        <image class="agree-checkbox" :src="`/static/icon-${isAgree ? 'agree' : 'disagree'}.png`"></image>
        <view class="agree-content">
          我已阅读并同意
          <view @click.stop="onViewAgreement(2)">《用户协议》</view>
          和
          <view @click.stop="onViewAgreement(1)">《隐私政策》</view>
        </view>
      </view>
      <button class="btn-login" @click="onLogin()">一键登录</button>
    </view>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'p-login-token',
  props: {
    query: {
      type: Object,
      default() {
        return {};
      },
    },
    locationInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isAgree: false, // 是否同意协议
      loaded: false,
      location: {}, // 定位信息
    };
  },
  watch: {
    locationInfo(newVal, oldVal) {
      console.log('locationInfo', newVal, oldVal);
      this.location = newVal;
    },
  },
  async mounted() {},
  computed: {
    ...mapState({
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
  },
  methods: {
    // 查看协议
    async onViewAgreement(value) {
      try {
        const res = await uni.$petro.http('user.agreement.h5', {
          agrmtType: value,
          regionCode: '510100',
        });
        console.log('res', res);
        if (!res?.data?.fileUrl) return uni.showToast({ content: '查看失败' });

        const res1 = await uni.$petro.downloadOpenFile(res.data.fileUrl);
        console.log('downloadOpenFile', res1);
      } catch (e) {}
    },
    // 数据校验
    verification() {
      if (!this.isAgree) {
        uni.showToast({ content: '请同意协议' });
        return false;
      }
      return true;
    },
    // 登录
    async onLogin() {
      if (!this.query?.token) {
        this.loaded = true;
        return;
      }
      const { accessToken } = await uni.$petro.getTokenInfo();
      if (accessToken) {
        this.loaded = true;
        return;
      }

      if (!this.verification()) return;

      try {
        const res = await uni.$petro.http(
          'user.login.h5',
          {
            loginType: 6, // 第三方token登录(app)
            token: this.query.token,
            loginLocation: this.location?.address?.province + this.location?.address?.city || '',
            loginGps: this.location?.longitude ? `${this.location?.longitude},${this.location?.latitude}` : '',
          },
          { riskField: true },
        );
        if (!res?.success || !res?.data) return uni.showToast({ content: res.message || '登录失败' });
        res.data.fleetType = 1; // 对外输出司机端进入必定是车队业务
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.$store.commit('setTokenInfo', res.data);
        this.loaded = true;

        const app = getApp();
        app.hookInit();
      } catch (err) {
        console.error(err);
        if (err?.errorCode === 'P_B00_200100') {
          uni.$petro.Bridge.zyzx.closeTinyApp();
        }
      }
    },
    // 获取车队业务类型
    // async getFleetType(businessNo) {
    //   try {
    //     const res = await uni.$petro.http('user.business.getFleetType.h5', {
    //       businessNo,
    //     });
    //     // 存储用户信息
    //     await uni.$petro.setTokenInfo(res.data);
    //     this.saveInfo = Object.assign(this.saveInfo, res.data);
    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
  },
};
</script>

<style scoped lang="scss">
.p-login-token {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  padding: 0 0 36rpx;
  box-sizing: border-box;
  position: relative;

  .header {
    width: 100%;
    display: flex;
    align-items: flex-start;
    padding: 260rpx 32rpx 200rpx;
    box-sizing: border-box;

    &-logo {
      width: 96rpx;
      height: 96rpx;
      margin-right: 32rpx;
    }

    &-bg {
      width: 100%;
      height: 556rpx;
    }

    &-value {
      flex: 1;
    }

    &-title {
      font-weight: 500;
      font-size: 44rpx;
      color: #333333;
      line-height: 62rpx;
    }

    &-subTitle {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;
      margin-top: 4rpx;
    }
  }

  .container {
    padding: 32rpx;
  }

  .agree {
    display: flex;
    text-align: left;
    margin-top: 40rpx;

    &-checkbox {
      width: 36rpx;
      height: 36rpx;
      margin-right: 6rpx;
    }

    &-content {
      flex: 1;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;

      & > view {
        color: #fa1919;
        display: inline-block;
      }
    }
  }

  button {
    height: 98rpx;
    background: #fa1919;
    border: none;
    border-radius: 49rpx;
    text-align: center;
    line-height: 98rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    margin: 48rpx 0 64rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .btn-login {
    letter-spacing: 18rpx;
    text-indent: 18rpx;
    margin-bottom: 48rpx;
  }
}
</style>
