<template>
  <view class="page-car">
    <!-- <u-navbar title="我的钱包" :autoBack="true" :placeholder="true" :bgColor="'#fff'"> </u-navbar> -->

    <u-navbar
      title="我的钱包"
      :autoBack="true"
      :placeholder="true"
      :bgColor="'#ADC5FF'"
      @rightClick="handleSelect('/pages/wallet/pages/set-up-page')"
    >
      <div slot="right" style="color: #666666; font-size: 28rpx">{{ role.roleDescribe === 'driver' ? '设置' : '设置' }}</div>
    </u-navbar>
    <view class="nav">
      <view class="tab-scroll">
        <view class="tab-scroll_box">
          <view @click="chenked(0)" class="tab-scroll_item" :class="{ active: isActive == 0 }"> <text>单位钱包</text><text></text> </view>
          <view @click="chenked(1)" class="tab-scroll_item" :class="{ active: isActive == 1 }"> <text>单位车辆</text><text></text> </view>
        </view>
      </view>
    </view>

    <swiper @change="change" :disable-touch="true" :current="isActive" class="swiper-content" :style="fullHeight">
      <swiper-item class="swiperitem-content">
        <!-- <zyzx-data-list
          :isLoad="tradeList.length > 0 ? true : false"
          style="flex: 1; background: #fff"
          ref="dataList"
          :showEmpty="showEmpty"
          @refreshPullDown="refreshPullDown"
          @scrolltolower="scrolltolower"
          v-if="isActive == 0"
        > -->
        <div class="wallet">
          <!-- 钱包基础信息  -->
          <zyzx-page-wallet />
          <!-- 账单列表 -->
          <div class="bill">
            <zyzx-page-bill ref="zyzxPageBillRef" :isShowBar="true" />
          </div>
        </div>
        <!-- </zyzx-data-list> -->
      </swiper-item>

      <swiper-item class="swiperitem-content">
        <zyzx-data-list
          :isLoad="tradeList.length > 0 ? true : false"
          style="flex: 1; background: #fff"
          ref="dataList"
          :showEmpty="showEmpty"
          @refreshPullDown="refreshPullDown"
          @scrolltolower="scrolltolower"
          v-if="isActive == 1"
        >
          <view>
            <view class="vehicle-information" @click="jumpToCarWallet">
              <view class="license-plate"> 京A 88888 </view>
              <view class="wallet-information">
                <view class="classification">
                  <view> </view>
                  <view> 总资产 (元)</view>
                  <view> 89800.88 </view>
                </view>
                <view class="classification">
                  <view> </view>
                  <view> 积分</view>
                  <view> 0 </view>
                </view>
                <view class="classification">
                  <view> </view>
                  <view> 优惠金</view>
                  <view> 0 </view>
                </view>
              </view>
              <view class="card-number"> NO.70000 0000 0000 0018 </view>
            </view>
          </view>
          <!-- <view class="empty-box" v-else-if="carList.length == 0">
            <image src="@/static/template.png" mode="scaleToFill" />
            <view>暂无数据</view>
          </view> -->
        </zyzx-data-list>
      </swiper-item>
    </swiper>
  </view>
</template>
<script>
// 引入账单列表组件
// import zyzxPageWallet from '@/components/zyzx-page-wallet/zyzx-page-wallet.vue';
// import zyzxPageBill from '@/components/zyzx-page-bill/zyzx-page-bill.vue';
export default {
  // 注册账单列表组件
  // components: { zyzxPageWallet, zyzxPageBill },

  watch: {
    // 监听currentindex变化，用于swiper与上面选项卡联动
    currentindex(newval) {
      // 将当前swiper的激活索引设置为新的值
      this.isActive = newval;
      // 重置滚动位置
      this.scrollLeft = 0;
      // 遍历前面的选项，累加它们的宽度，得到当前选项距离父元素最左侧的距离
      for (let i = 0; i < newval - 1; i++) {
        this.scrollLeft += this.category[i].width;
      }
    },
  },

  data() {
    return {
      systemInfo: uni.$petro.store.systemInfo,
      // 当前激活的选项卡索引
      isActive: 0,
      // swiper当前激活的索引，与isActive同步
      currentindex: 0,
      // 横向滚动条位置，用于swiper滚动时定位
      scrollLeft: 0,
      scrollViewId: '',
      carList: [], // vehicleType （1.乘用车 2.挂车 3.货车 4.客车） fillType 加注类型（汽油、柴油、氢气、天然气（LNG）、混合、充电）
      // 管理员已分配的车辆列表
      allocatedCartList: [],
      // 管理员未分配的车辆列表
      unallocatedCartList: [],
      usedTotal: 0, // 使用中车辆数量
      unUsedTotal: 0, // 未分配车辆数量
      pageNum: 1,
      pageSize: 10,
    };
  },

  onLoad(query) {},
  onShow() {
    this.$nextTick(() => {
      this.$refs.zyzxPageBillRef.getBillList(true);
    });
  },

  methods: {
    // 下拉刷新触发
    async refreshPullDown() {
      console.log('下拉刷新触发....');
      this.$refs.dataList.loadStatus = 'loading';
      this.$refs.dataList.stopRefresh();
    },

    // 上拉加载更多
    scrolltolower() {
      console.log('上拉加载更多....');
      // 可加载状态
      if (this.$refs.dataList.loadStatus == 'contentdown') {
        this.$refs.dataList.loadStatus = 'loading';
      }
    },

    // 点击选项卡时触发，将当前选项卡设置为激活状态，并计算滚动位置
    chenked(index) {
      this.isActive = index;
      this.scrollLeft = 0;
      // 遍历前面的选项卡，累加它们的宽度，得到当前选项卡距离父元素最左侧的距离
      for (let i = 0; i < index - 1; i++) {
        this.scrollLeft += this.category[i].width;
      }
    },

    // swiper滑动时触发，获取其当前激活的索引
    change(e) {
      // 从事件对象中解构出swiper当前激活的索引
      const { current } = e.detail;
      this.currentindex = current;
    },
    jumpToCarWallet() {
      uni.$petro.route({ url: '/pages/wallet/pages/car-wallet-page', type: 'to', params: {} });
    },
  },
};
</script>

<style lang="scss">
page {
  height: 100%;
  display: flex;
  background-color: #f2f2f2;
  overflow: hidden;
}

.page-car {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  .nav {
    width: 100%;
    height: 96rpx;
    // background: #ffffff;
    background: #adc5ff;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    .tab-scroll {
      box-sizing: border-box;
      justify-content: space-between;
      .tab-scroll_box {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        box-sizing: border-box;
        justify-content: space-between;
        .tab-scroll_item {
          width: 50%;
          flex-shrink: 0;
          padding-bottom: 20rpx;
          padding-top: 20rpx;
          display: flex;
          margin-right: 30rpx;
          font-weight: 600;
          align-items: flex-end;
          justify-content: center;
          font-weight: 400;
          font-size: 32rpx;
          color: #666666;
          line-height: 38rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        image {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
  }

  .swiper-content {
    padding-top: 10rpx;
    box-sizing: border-box;
    flex: 1;
    .swiperitem-content {
      background-color: #f2f2f2;
      .vehicle-information {
        background: #a7a5a5;
        padding: 30rpx;
        .license-plate {
          font-size: 36rpx;
          font-weight: bold;
        }
        .wallet-information {
          display: flex;
          justify-content: space-between;
          padding: 20rpx 0rpx;
          .classification {
            width: 30%;
            text-align: center;
            view {
              margin-top: 20rpx;
              &:nth-child(1) {
                background: #fa1919;
                width: 70rpx;
                height: 60rpx;
                margin: 0 auto;
              }
            }
          }
        }
        .card-number {
          text-align: right;
          margin: 20rpx 0;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
}

.active {
  position: relative;
  color: #000 !important;
  font-weight: 600 !important;
}

.active::after {
  content: '';
  position: absolute;
  width: 48rpx;
  height: 4rpx;
  background-color: #fa1919;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.car-box {
  margin: 24rpx auto;
  width: 686rpx;
  height: 160rpx;
  background: #ffffff;
  box-shadow: 0rpx 1rpx 0rpx 0rpx #eeeeee;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  .car-box-top {
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 5rpx solid #fff;
  }
}

.u-nav-slot {
  display: flex;
  /* #ifdef MP-WEIXIN || MP-ALIPAY */
  margin-right: 160rpx;
  /* #endif */
}

.empty-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 200rpx;
  image {
    width: 346.32rpx;
    height: 236.59rpx;
  }
  view {
    width: 100%;
    height: 40rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 33rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-top: 48rpx;
  }
}
</style>
