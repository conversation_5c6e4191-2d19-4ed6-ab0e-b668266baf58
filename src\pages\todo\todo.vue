<template>
  <div class="page-container">
    <u-navbar :title="'待办事项'" :autoBack="true" :placeholder="true" :bgColor="'transparent'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <zyzx-page-unit-setting :isRelate="isRelate" @success="onActivated" v-if="isShow" :data="params"></zyzx-page-unit-setting>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import zyzxPageUnitSetting from '@/components/zyzx-page-unit-setting/zyzx-page-unit-setting.vue';

export default {
  name: 'todo',
  data() {
    return {
      isShow: false,
      isRelate: false,
      params: {},
    };
  },
  components: {
    zyzxPageUnitSetting,
  },
  async onLoad(query) {
    console.log('onLoad', query);
    this.isRelate = Number(query?.isRelate) === 1;
    const { orgCode } = await uni.$petro.getTokenInfo();
    this.params = {
      enterpriseNo: orgCode,
    };
    this.isShow = true;
  },
  mounted() {},
  methods: {
    async onActivated(list) {
      const app = getApp();
      await app.hookInit();

      const { gsmsToken } = await uni.$petro.getTokenInfo();
      uni.$petro.route({
        url: '/pages/index/index',
        params: { token: gsmsToken },
        type: 'redirectTo',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;

  .page-content {
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
