---
description: UniApp + Vue 2 + Vuex 状态管理规范
globs: *.vue,*.js,store/*.js
alwaysApply: true
---

# UniApp + Vue 2 + Vuex 状态管理规范

## 总体描述

本文档主要介绍基于UniApp + Vue 2项目中Vuex状态管理的相关内容，包括Store模块化结构、状态管理使用规范、状态持久化和最佳实践，通过示例代码展示如何在UniApp项目中正确使用Vuex进行状态管理。

### 应用范围

本规范适用于所有使用Vuex进行状态管理的UniApp项目，帮助开发人员正确地设计和使用状态管理Store模块。

### 使用要求

开发人员在进行Vuex状态管理开发时，需要按照本规范中的模块化结构和使用规范进行开发。在定义Store模块时，要合理划分state、mutations、actions和getters，并确保代码的可读性和可维护性。在组件中使用Store时，要遵循规范中的访问和调用方式。

### 强制性约束

- **严格使用Vuex 3.6.2**，禁止使用Pinia或其他状态管理库
- **必须启用命名空间**，所有模块都要设置`namespaced: true`
- **严格模式**：开发环境必须启用strict模式
- **模块化组织**：按业务领域拆分store模块

## 规则1 Vuex模块化结构

### 项目实际Store结构

基于项目实际情况，定义Vuex store的模块化组织结构，按业务领域划分模块。

#### 完整目录结构

```text
src/services/store/
├── index.js                 # store入口文件
├── roles.js                 # 用户角色状态管理
├── account.js               # 账户公共状态管理
├── accountDriver.js         # 司机账户状态管理
├── car.js                   # 车辆状态管理
├── station.js               # 加油站状态管理
├── company.js               # 企业状态管理
├── tabBar.js                # 底部导航状态管理
├── test1.js                 # 测试模块1
└── test2.js                 # 测试模块2
```

#### 模块职责划分

**核心业务模块**

- **roles.js**: 用户角色、权限、登录状态管理
- **account.js**: 账户公共信息管理
- **accountDriver.js**: 司机专用账户信息管理
- **car.js**: 车辆信息、车辆列表管理
- **station.js**: 加油站信息、油站列表管理
- **company.js**: 企业信息、企业切换管理

**UI状态模块**

- **tabBar.js**: 底部导航栏状态管理

**测试模块**

- **test1.js/test2.js**: 开发测试用模块

### Store入口文件配置

```javascript
// src/services/store/index.js
import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

// 导入业务模块
import test1 from '@/services/store/test1';
import test2 from '@/services/store/test2';

// 油站相关
import station from '@/services/store/station';
// 车辆相关
import car from '@/services/store/car';
// 账户相关-公共
import account from '@/services/store/account';
// 企业相关
import company from '@/services/store/company';
// tabbar
import tabBar from '@/services/store/tabBar';
// 账户相关-司机
import accountDriver from '@/services/store/accountDriver';
// 角色
import roles from '@/services/store/roles';

const store = new Vuex.Store({
  modules: {
    // 集成petro公共store
    ...uni.$petro.store.store,
    // 业务模块
    test1,
    test2,
    station,
    car,
    account,
    company,
    tabBar,
    accountDriver,
    roles,
  },
  strict: process.env.NODE_ENV !== 'production' // 开发环境启用严格模式
});

export default store;
```

#### Store集成说明

**petro公共store集成**

项目集成了`uni.$petro.store.store`公共store模块，提供基础功能：

- 公共状态管理
- 通用工具方法
- 基础数据缓存

**模块加载顺序**

1. 首先加载petro公共store
2. 然后加载业务模块store
3. 确保业务模块可以覆盖公共模块的同名状态

### 模块标准结构

每个Vuex模块都应遵循统一的结构规范，确保代码的一致性和可维护性。

#### 结构规范要求

**标准结构顺序**

1. **state**: 状态定义 - 定义模块的初始状态
2. **mutations**: 同步状态变更 - 唯一修改state的方式
3. **actions**: 异步操作和业务逻辑 - 处理异步操作，调用mutations
4. **getters**: 计算属性和状态派生 - 从state派生出的计算属性
5. **export**: 模块导出配置 - 必须启用namespaced

#### 命名规范

- **mutations**: 使用大写字母和下划线，如`SET_USER_INFO`
- **actions**: 使用驼峰命名，如`getUserInfo`
- **getters**: 使用驼峰命名，如`isLoggedIn`
- **state属性**: 使用驼峰命名，如`userInfo`

#### 完整模块示例

```javascript
// src/services/store/roles.js - 用户角色状态管理示例
const state = {
  // 用户基本信息
  userInfo: null,
  // 用户角色信息
  role: null,
  // 登录状态
  isLogin: false,
  // Token信息
  tokenInfo: null
};

const mutations = {
  // 设置用户信息
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },

  // 设置用户角色
  SET_ROLE(state, role) {
    state.role = role;
  },

  // 设置登录状态
  SET_LOGIN_STATUS(state, status) {
    state.isLogin = status;
  },

  // 设置Token信息
  SET_TOKEN_INFO(state, tokenInfo) {
    state.tokenInfo = tokenInfo;
  },

  // 清除用户数据
  CLEAR_USER_DATA(state) {
    state.userInfo = null;
    state.role = null;
    state.isLogin = false;
    state.tokenInfo = null;
  }
};

const actions = {
  // 获取用户信息
  async getUserInfo({ commit, state }, payload = {}) {
    try {
      const { success, data } = await uni.$petro.http(
        'user.getUserDetailInfo.h5',
        {
          businessNo: state.role?.businessNo,
          type: 1
        },
        {
          mockResponse: {
            success: true,
            data: {
              staffNo: '25241121134005016',
              petroChinaNo: '35116154673278',
              memberType: 2,
              name: '雷天伟',
              identityNo: '511621********4177',
              businessRegionName: '长沙市',
              mobile: '183****0904'
            },
            message: '请求成功',
            errorCode: null
          }
        }
      );

      if (success) {
        // 处理用户名分割
        data.splitterName = uni.$petro.Utils.splitterName(data?.name);
        commit('SET_USER_INFO', data);
        return data;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  // 用户登录
  async login({ commit }, loginData) {
    try {
      const { success, data } = await uni.$petro.http(
        'user.login.h5',
        loginData,
        {
          mockResponse: {
            success: true,
            data: {
              token: 'mock-token-123',
              userInfo: { name: '测试用户' }
            }
          }
        }
      );

      if (success) {
        commit('SET_TOKEN_INFO', data.token);
        commit('SET_USER_INFO', data.userInfo);
        commit('SET_LOGIN_STATUS', true);
        return data;
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  },

  // 用户登出
  async logout({ commit }) {
    try {
      // 调用登出接口
      await uni.$petro.http('user.logout.h5', {});

      // 清除本地状态
      commit('CLEAR_USER_DATA');

      // 清除本地存储
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');

    } catch (error) {
      console.error('登出失败:', error);
      // 即使接口失败也要清除本地状态
      commit('CLEAR_USER_DATA');
    }
  }
};

const getters = {
  // 是否已登录
  isLoggedIn: state => state.isLogin && !!state.tokenInfo,

  // 用户名称
  userName: state => state.userInfo?.name || '',

  // 用户手机号
  userMobile: state => state.userInfo?.mobile || '',

  // 用户角色类型
  roleType: state => state.role?.type || '',

  // 格式化的用户信息
  formattedUserInfo: state => {
    if (!state.userInfo) return null;
    return {
      ...state.userInfo,
      displayName: state.userInfo.splitterName || state.userInfo.name
    };
  }
};

export default {
  namespaced: true, // 启用命名空间
  state,
  mutations,
  actions,
  getters
};
```

## 规则2 组件中使用Vuex

### 辅助函数使用规范
在Vue组件中使用Vuex辅助函数来访问store状态和方法。

#### 应用范围
适用于所有需要访问Vuex状态的Vue组件。

#### 使用要求
优先使用辅助函数，保持代码简洁和可读性。

#### 组件使用示例

```javascript
// 组件中使用Vuex示例
<template>
  <view class="user-info">
    <!-- 显示用户信息 -->
    <view v-if="isLoggedIn" class="user-card">
      <text class="user-name">{{ formattedUserInfo.displayName }}</text>
      <text class="user-mobile">{{ userMobile }}</text>
      <u-button @click="handleLogout">退出登录</u-button>
    </view>

    <!-- 未登录状态 -->
    <view v-else class="login-prompt">
      <u-button @click="handleLogin">立即登录</u-button>
    </view>

    <!-- 加载状态 -->
    <u-loading-icon v-if="loading" mode="flower"></u-loading-icon>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex';

export default {
  name: 'UserInfo',
  data() {
    return {
      loading: false
    };
  },
  computed: {
    // 映射state到计算属性
    ...mapState('roles', {
      userInfo: state => state.userInfo,
      tokenInfo: state => state.tokenInfo
    }),

    // 映射getters到计算属性
    ...mapGetters('roles', [
      'isLoggedIn',
      'userName',
      'userMobile',
      'formattedUserInfo'
    ]),

    // 映射其他模块的状态
    ...mapState('accountDriver', {
      walletList: state => state.walletList
    })
  },
  methods: {
    // 映射mutations到方法
    ...mapMutations('roles', [
      'SET_USER_INFO',
      'SET_LOGIN_STATUS'
    ]),

    // 映射actions到方法
    ...mapActions('roles', [
      'getUserInfo',
      'login',
      'logout'
    ]),

    // 组件方法
    async handleLogin() {
      try {
        this.loading = true;
        const loginData = {
          username: 'test',
          password: '123456'
        };

        await this.login(loginData);
        uni.showToast({ title: '登录成功', icon: 'success' });

        // 登录成功后获取用户信息
        await this.getUserInfo();

      } catch (error) {
        uni.showToast({ title: '登录失败', icon: 'none' });
      } finally {
        this.loading = false;
      }
    },

    async handleLogout() {
      try {
        await this.logout();
        uni.showToast({ title: '已退出登录', icon: 'success' });
      } catch (error) {
        uni.showToast({ title: '退出失败', icon: 'none' });
      }
    }
  },
  async mounted() {
    // 组件挂载时检查登录状态
    if (this.isLoggedIn) {
      await this.getUserInfo();
    }
  }
};
</script>
```

### 直接访问Store
在某些情况下需要直接访问store实例。

```javascript
export default {
  methods: {
    // 直接访问state
    getUserData() {
      const userInfo = this.$store.state.roles.userInfo;
      return userInfo;
    },

    // 直接调用getter
    checkLoginStatus() {
      const isLoggedIn = this.$store.getters['roles/isLoggedIn'];
      return isLoggedIn;
    },

    // 直接提交mutation
    updateUserInfo(newInfo) {
      this.$store.commit('roles/SET_USER_INFO', newInfo);
    },

    // 直接分发action
    async refreshUserData() {
      try {
        const result = await this.$store.dispatch('roles/getUserInfo', {
          refresh: true
        });
        return result;
      } catch (error) {
        console.error('刷新用户数据失败:', error);
      }
    }
  }
};
```

## 规则3 状态持久化

### 本地存储集成
将重要的状态数据持久化到本地存储。

#### 应用范围
适用于需要持久化的用户状态，如登录信息、用户设置等。

#### 使用要求
合理选择需要持久化的状态，避免存储敏感信息。

#### 持久化示例

```javascript
// store模块中的持久化处理
const actions = {
  // 登录时保存状态
  async login({ commit }, loginData) {
    try {
      const { success, data } = await uni.$petro.http('user.login.h5', loginData);

      if (success) {
        // 更新store状态
        commit('SET_TOKEN_INFO', data.token);
        commit('SET_USER_INFO', data.userInfo);
        commit('SET_LOGIN_STATUS', true);

        // 持久化到本地存储
        uni.setStorageSync('token', data.token);
        uni.setStorageSync('userInfo', data.userInfo);
        uni.setStorageSync('loginTime', Date.now());

        return data;
      }
    } catch (error) {
      throw error;
    }
  },

  // 应用启动时恢复状态
  async restoreState({ commit }) {
    try {
      const token = uni.getStorageSync('token');
      const userInfo = uni.getStorageSync('userInfo');
      const loginTime = uni.getStorageSync('loginTime');

      // 检查token是否过期（7天）
      const isExpired = Date.now() - loginTime > 7 * 24 * 60 * 60 * 1000;

      if (token && userInfo && !isExpired) {
        commit('SET_TOKEN_INFO', token);
        commit('SET_USER_INFO', userInfo);
        commit('SET_LOGIN_STATUS', true);
        return true;
      } else {
        // 清除过期数据
        uni.removeStorageSync('token');
        uni.removeStorageSync('userInfo');
        uni.removeStorageSync('loginTime');
        return false;
      }
    } catch (error) {
      console.error('恢复状态失败:', error);
      return false;
    }
  }
};
```

### 状态同步机制
确保多个页面间的状态同步。

```javascript
// 在App.vue中处理全局状态同步
export default {
  async onLaunch() {
    // 应用启动时恢复状态
    await this.$store.dispatch('roles/restoreState');
  },

  onShow() {
    // 应用显示时检查状态
    this.checkStateSync();
  },

  methods: {
    checkStateSync() {
      // 检查关键状态是否需要同步
      const localToken = uni.getStorageSync('token');
      const storeToken = this.$store.state.roles.tokenInfo;

      if (localToken !== storeToken) {
        // 状态不一致时重新同步
        this.$store.dispatch('roles/restoreState');
      }
    }
  }
};
```

## 规则4 最佳实践

### 状态设计原则
1. **单一数据源**: 每个状态只有一个来源
2. **状态扁平化**: 避免深层嵌套的状态结构
3. **最小化状态**: 只存储必要的状态数据
4. **状态不可变**: 通过mutations修改状态

### 性能优化
```javascript
// 使用getter缓存计算结果
const getters = {
  expensiveComputation: state => {
    // 复杂计算会被缓存
    return state.data.filter(item => item.active)
      .map(item => ({
        ...item,
        computed: heavyComputation(item)
      }));
  }
};

// 在组件中按需订阅状态
computed: {
  // 只订阅需要的状态
  ...mapState('roles', ['userInfo']),

  // 避免订阅整个复杂对象
  userName() {
    return this.$store.state.roles.userInfo?.name;
  }
}
```

### 错误处理
```javascript
const actions = {
  async fetchData({ commit }, payload) {
    try {
      commit('SET_LOADING', true);
      const result = await api.getData(payload);
      commit('SET_DATA', result.data);
      commit('SET_ERROR', null);
    } catch (error) {
      commit('SET_ERROR', error.message);
      console.error('获取数据失败:', error);
      throw error; // 重新抛出错误供组件处理
    } finally {
      commit('SET_LOADING', false);
    }
  }
};
```
```

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const isAuthenticated = ref(false)
  const preferences = reactive({ theme: 'light' })

  // 获取器
  const userName = computed(() => user.value?.name || 'Guest')

  // 动作
  const login = async (credentials) => {
    try {
      const response = await fetchUserInfo(credentials)
      user.value = response.data
      isAuthenticated.value = true
      return true
    } catch (error) {
      console.error('Login failed', error)
      return false
    }
  }

  const logout = () => {
    user.value = null
    isAuthenticated.value = false
  }

  return {
    user,
    isAuthenticated,
    preferences,
    userName,
    login,
    logout
  }
})
```

#### 规则2 Store使用规范

代码参考如下：

```typescript
<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 访问状态
console.log(userStore.user)

// 调用动作
const handleLogin = async () => {
  const success = await userStore.login({
    username: 'test',
    password: '123456'
  })
  
  if (success) {
    // 登录成功
  }
}
</script>
```

反例

```typescript
// 违反Store结构规范，未正确划分状态、获取器和动作
import { defineStore } from 'pinia';

export const useWrongUserStore = defineStore('wrong-user', () => {
  const user = { name: 'John Doe' };

  const login = () => {
    console.log('Login');
  };

  return {
    user,
    login
  };
});

// 违反Store使用规范，未正确调用动作
<script setup>
import { useWrongUserStore } from '@/stores/wrong-user';

const wrongUserStore = useWrongUserStore();

// 错误的访问方式
console.log(wrongUserStore.user.name);

// 错误的调用方式
wrongUserStore.login();
</script>
```
