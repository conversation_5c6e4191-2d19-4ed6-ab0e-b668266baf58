<template>
  <div class="page-container">
    <div class="page-content" :class="{ 'code-bg': oilTabsValue == 'code' }">
      <div class="header-placeholder" :style="{ height: systemInfo.statusBarHeight }"></div>
      <div class="tabs" :class="[oilTabsValue]">
        <div
          class="tab-item"
          :class="{ active: oilTabsValue == tab.value }"
          v-for="(tab, index) in oilTabs"
          :key="index"
          @click="tabChange(tab)"
        >
          {{ tab.label }}
        </div>
      </div>
      <div class="oil-content">
        <div class="oil-e" v-if="oilTabsValue == 'reserve'">
          <oil-e :showMap="oilTabsValue == 'reserve'" :showHeader="false"></oil-e>
        </div>
        <div class="oil-code" v-if="oilTabsValue == 'code'">
          <oil-code></oil-code>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-container {
  height: 100%;
}
.page-content {
  background: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
  &.code-bg {
    background: url('@/static/images/code-oil-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .header-placeholder {
  }
  .tabs {
    padding: 26rpx 30rpx;
    // margin-top: 60rpx;
    display: flex;
    justify-content: space-evenly;
    .tab-item {
      height: 48rpx;
      font-size: 34rpx;
      color: #666666;
      line-height: 40rpx;
      text-align: center;
      position: relative;
      &.active {
        color: #333333;
        &::after {
          content: '';
          position: absolute;
          left: calc(50% - 24rpx);
          bottom: -22rpx;
          width: 48rpx;
          height: 4rpx;
          background: #fa1919;
          border-radius: 4rpx;
        }
      }
    }
    &.code {
      .tab-item {
        height: 48rpx;
        font-size: 34rpx;
        color: rgba(255, 255, 255, 0.7);
        line-height: 40rpx;
        text-align: center;
        &.active {
          color: #ffffff;
          &::after {
            background: #ffffff;
          }
        }
      }
    }
  }
  .oil-content {
    flex: 1;
    margin-top: 40rpx;
    width: 100%;
    .oil-e {
      height: 100%;
    }
  }
}
</style>

<script>
import { mapState, mapGetters } from 'vuex';

import oilE from '@/components/page-refuel-oil/components/oil-e.vue';
import oilCode from '@/components/page-refuel-oil/components/oil-code.vue';

export default {
  name: 'page-refuel-oil',
  components: {
    oilE,
    oilCode,
  },
  props: {
    showMap: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      systemInfo: uni.$petro.store?.systemInfo,
      osName: uni.$petro.store.systemInfo.osName,
      // 权限
      permissions: null,
      // 为了记录用户第一次进来点的是去开启还是取消,再切换回来app后需要操作(zk要求)
      sysModalStatus: '',
      // 标签栏
      tabsActive: 'reserve',
      tabs: [
        { label: 'e享加油', value: 'reserve' },
        { label: '扫码付款', value: 'code' },
      ],
    };
  },
  computed: {
    ...mapState({
      oilTabs: state => state?.tabBar?.oilTabs,
      oilTabsValue: state => state?.tabBar?.oilTabsValue,
    }),
  },
  watch: {},
  async mounted() {
    uni.onAppShow(async res => {
      this.permissions = await this.getPermissions();
      // 系统和app都有权限, 并且当前油站列表没有数据, 获取油站列表
      if (this.permissions?.gps == 1 && !this.stationList?.length) {
        this.$store.commit('updateLocalPre', {
          page: 'oil',
          localPre: true,
          isDefaultGPS: false,
        });
      }

      // zk要求: 安卓流程,用户打开app后无权限，弹出系统权限弹窗点击去设置后再回来app,需要再次判断弹出app权限弹窗,只弹一次
      if (this.osName == 'android' && this.sysModalStatus === 'confirm' && [2, 4].includes(this.permissions?.gps)) {
        this.sysModalStatus = '';
        this.showAppPermissionModal();
      }
    });

    // 权限判断
    // 1 系统开,app开
    // 2 系统开,app关
    // 3 系统关,app开
    // 4 系统关,app关
    this.permissions = await this.getPermissions();
    if ([3, 4].includes(this.permissions?.gps)) {
      // 系统权限未开启
      this.showSysPermissionModal();
    } else if ([2].includes(this.permissions?.gps)) {
      // app权限未开启
      this.showAppPermissionModal();
    } else {
      this.$store.commit('updateLocalPre', {
        page: 'oil',
        localPre: true,
        isDefaultGPS: false,
      });
    }
  },
  methods: {
    // 权限-显示系统定位权限弹窗
    async showSysPermissionModal() {
      uni.showModal({
        title: '定位服务未开启,无法根据您的位置信息为您服务,如不开启则为您选择默认城市服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            this.sysModalStatus = 'confirm';
            // 跳去系统设置界面
            if (this.osName == 'ios') {
              this.toSysSetting();
            } else {
              this.toSysSetting('locsyssetting');
            }
          } else {
            this.sysModalStatus = 'cancel';
            // ios无法检测系统关,app开的情况,不做处理
            if (this.osName == 'android') {
              if (this.permissions?.gps == 3) {
                this.$store.commit('updateLocalPre', {
                  page: 'oil',
                  localPre: true,
                  isDefaultGPS: true,
                });
              } else if (this.permissions?.gps == 4) {
                // 系统关,app关 显示app设置提示弹窗
                this.showAppPermissionModal();
              }
            }
          }
        },
      });
    },
    // 权限-显示app定位权限弹窗
    async showAppPermissionModal() {
      uni.showModal({
        title: '位置权限未开启,无法根据您的位置信息获取您附近的加油站网点信息为您服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            // 跳去app设置界面
            this.toSysSetting();
          }
          // 界面显示无位置信息状态
        },
      });
    },
    // 权限-跳转系统设置页面
    async toSysSetting(url = 'setting') {
      const res = await uni.$petro.Bridge.zyzx.launchUrl({
        url: url,
      });
    },
    // 权限-获取权限
    getPermissions() {
      if (my.isIDE) {
        return {
          gps: 1,
        };
      }
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera', 'gps'],
        });
        resolve(res);
      });
    },
    // 切换 扫码付款（code）、e享加油（reserve）
    tabChange(tab) {
      console.log(tab, '切换tab触发');
      if (tab == this.oilTabsValue) return;
      this.$store.commit('switchOilTab', tab.value);
    },
  },
  destroyed() {},
};
</script>

<style lang="scss" scoped></style>
