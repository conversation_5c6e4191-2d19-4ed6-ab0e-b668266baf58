import icon_oil from '@/static/images/tab/oil.png';
import icon_oil_active from '@/static/images/tab/oil-checked.png';
import icon_navigation from '@/static/images/tab/navigation.png';
import icon_navigation_active from '@/static/images/tab/navigation-checked.png';
import icon_order from '@/static/images/tab/order.png';
import icon_order_active from '@/static/images/tab/order-checked.png';
import icon_mine from '@/static/images/tab/mine.png';
import icon_mine_active from '@/static/images/tab/mine-checked.png';

export default {
  state: {
    // tabBar
    tabBarValue: 'refuel-home',
    tabBarCache: ['refuel-home'],
    tabBarList: [
      {
        page: 'refuel-home',
        name: '首页',
        icon: icon_oil,
        iconActive: icon_oil_active,
      },
      {
        page: 'navigation',
        name: '导航',
        icon: icon_navigation,
        iconActive: icon_navigation_active,
      },
      {
        page: 'refuel-oil',
        name: '加油',
        icon: icon_oil,
        iconActive: icon_oil_active,
      },
      {
        page: 'order',
        name: '订单',
        icon: icon_order,
        iconActive: icon_order_active,
      },
      {
        page: 'mine',
        name: '我的',
        icon: icon_mine,
        iconActive: icon_mine_active,
      },
    ],
    // 加油页面的tab
    oilTabsValue: 'reserve',
    oilTabs: [
      { label: 'e享加油', value: 'reserve' },
      { label: '扫码付款', value: 'code' },
    ],
    tabBarPermissions: {
      home: {
        localPre: false,
        isDefaultGPS: false,
      },
      navigation: {
        localPre: false,
        isDefaultGPS: false,
      },
      oil: {
        localPre: false,
        isDefaultGPS: false,
      },
    },
    indexPageShow: true,
  },
  mutations: {
    switchTab(state, payload) {
      state.tabBarValue = payload;
      // 记录已经加载过的页面
      if (!state.tabBarCache.includes(payload)) {
        state.tabBarCache.push(payload);
      }
    },
    switchOilTab(state, payload) {
      state.oilTabsValue = payload;
    },
    updateLocalPre(state, payload) {
      state.tabBarPermissions[payload?.page] = {
        localPre : payload?.localPre,
        isDefaultGPS: payload?.isDefaultGPS,
      }
    },
    setIndexPageStatus(state, isShow) {
      state.indexPageShow = isShow;
    }
  },
  actions: {},
  getters: {},
};
