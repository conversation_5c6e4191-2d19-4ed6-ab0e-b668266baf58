export default {
  state: {
    driverWalletList: [],
    driverWalletInfo: {
      enterpriseAccountNo: '',
      mainAccountNo: '',
      enterpriseNo: '',
      enterpriseStaffNo: '',
      bdCardNo: '',
      userId: '',
      userName: '',
      licencePlate: '',
      accountType: '',
      unitAlias: '',
      businessNo: '',
      accountStatus: '',
      businessType: '',
      invoiceType: '',
      accountPlace: '',
      accountPlaceName: '',
      serialNo: '',
      loyaltyAccountList: [],
      walletAccountList: [],
    },
    cardInfo: {
      bdCardNo: '',
      carLicense: '',
      unitAccRDto: {
        unitMainAccountNo: '',
        mainAccountNo: '',
        carLicense: '',
        unitAlias: '',
        mainAccountType: '',
        accountStatus: '',
        createTime: '',
        accountPlace: '',
        accountPlaceName: '',
        enterpriseStaffName: '',
        availableAmount: '',
        allocationAmount: '',
        summaryAmount: '',
        frozenAmount: '',
      },
    },
  },
  mutations: {
    setCardInfo(state, value) {
      Object.assign(state.cardInfo, value);
      console.log(state.cardInfo);
    },
    setDriverWalletList(state, value) {
      if (value.isBd) {
        this.commit('setDriverWalletInfo', value);
        return;
      }
      Object.assign(state.driverWalletList, value);
      if (!state.driverWalletList.length) return;
      // 如果当前已经选择了账户,则重新设置账户信息
      if (state.driverWalletInfo?.mainAccountNo) {
        const newInfo = state.driverWalletList.find(
          v => v.mainAccountNo == state.driverWalletInfo?.mainAccountNo && v.accountType == state.driverWalletInfo?.accountType,
        );
        if (newInfo) {
          this.commit('setDriverWalletInfo', newInfo);
          return;
        }
      }
      // 室内支付默认的账户-司机账户
      this.commit('setDriverWalletInfo', state.driverWalletList.find(v => v.accountType == 2) || state.driverWalletList[0]);
    },
    setDriverWalletInfo(state, value) {
      Object.assign(state.driverWalletInfo, value);
    },
  },
  actions: {
    // 单位司机查询账户钱包列表
    async getWalletList({ commit, dispatch, state, rootState }, payload = {}) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.driverWalletList?.length) return state.driverWalletList;
      try {
        const params = {
          enterpriseAccountNo: payload?.enterpriseAccountNo, // 单位账户编号
        };
        let showLoading = true;
        if (typeof payload?.showLoading === 'boolean') showLoading = payload?.showLoading;
        const { success, data } = await uni.$petro.http('account.driver.queryWalletList.h5', params, {
          showLoading: showLoading,
          mockResponse: {
            success: true,
            data: [
              {
                enterpriseAccountNo: '',
                mainAccountNo: '111',
                enterpriseNo: '',
                userId: '123',
                userName: '赵忠祥',
                licencePlate: '川A23324',
                accountType: '2',
                unitAlias: '北京运满满有限公司',
                businessNo: '*********',
                businessType: 0,
                invoiceType: 0,
                accountPlace: '',
                accountPlaceName: '',
                serialNo: 0,
                walletAccount: {
                  cardNo: '**************',
                  accountNo: '1231312',
                  frozenAmount: 0.0,
                  availableAmount: 120.0,
                  accountStatus: 0,
                },
              },
              {
                enterpriseAccountNo: '',
                mainAccountNo: '222',
                enterpriseNo: '',
                userId: '1234',
                userName: 'testName2',
                licencePlate: '京Q67T60',
                accountType: '3',
                unitAlias: '北京运满满有限公司2',
                businessNo: '*********',
                businessType: 0,
                invoiceType: 0,
                accountPlace: '',
                accountPlaceName: '',
                serialNo: 0,
                walletAccount: {
                  cardNo: '**************',
                  accountNo: '1231312',
                  frozenAmount: 0.0,
                  availableAmount: 100.0,
                  accountStatus: 0,
                },
              },
            ],
            message: '请求成功',
            errorCode: null,
          },
        });
        if (success) {
          if (payload.isBd) {
            let driverWalletInfo = data.find(item => item?.bdCardNo === state.cardInfo?.bdCardNo);
            commit('setDriverWalletList', { ...driverWalletInfo, isBd: true });
            return;
          }
          commit('setDriverWalletList', data);
          return data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    async getDriverWalletInfo({ commit, dispatch, state, rootState }, payload = {}) {
      const { accessToken } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      return state.driverWalletInfo;
    },
    async getCardInfo({ commit, dispatch }, data) {
      console.log('getCardInfo', data);
      commit('setCardInfo', data);
      if (data?.bdCardNo) {
        await dispatch('getWalletList', { isBd: true, refresh: true, enterpriseAccountNo: data?.enterpriseAccountNo });
      } else {
        commit('setDriverWalletInfo', {
          walletAccountList: [],
          loyaltyAccountList: [],
        });
      }
    },
  },
  getters: {},
};
