// 获取油站列表 /oilstation/station/list
export function getStationListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageSize: '10',
      pageNum: '1',
      pageSum: '12',
      rows: [
        {
          activityList: [],
          orgCode: '1-A4301-C001-S010',
          orgName: '湖南长沙万家丽加油站',
          distance: '3.9',
          tagList: ['洗车', '便利店', '餐厅', 'ETC'],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市芙蓉区万家丽路与人民路交汇处西南角（中庭国际旁）',
          longitude: '113.036073',
          latitude: '28.189321',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A0C',
          stationType: null,
          stationId: '10',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S013',
          orgName: '湖南长沙古曲路加油站',
          distance: '5.6',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市芙蓉区远大一路894号与古曲路交汇处',
          longitude: '113.045181',
          latitude: '28.203695',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A0R',
          stationType: null,
          stationId: '13',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S007',
          orgName: '湖南长沙农科院加油站',
          distance: '8.3',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市芙蓉区马坡岭（远大二路与红旗路交汇处往南220米）',
          longitude: '113.091028',
          latitude: '28.207142',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A09',
          stationType: null,
          stationId: '7',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S015',
          orgName: '湖南长沙新开铺加油站',
          distance: '8.3',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市天心区新开铺路1131号西南方向110米',
          longitude: '112.969382',
          latitude: '28.102539',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A0U',
          stationType: null,
          stationId: '15',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S005',
          orgName: '湖南长沙钢材加油站',
          distance: '8.7',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市天心区书院南路钢材大市场批塘村新开铺路',
          longitude: '112.975557',
          latitude: '28.092509',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A06',
          stationType: null,
          stationId: '5',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S023',
          orgName: '湖南长沙长皇加油站',
          distance: '9.9',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市长沙县星沙镇泉塘村远大二路1102号',
          longitude: '113.106721',
          latitude: '28.213398',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A21',
          stationType: null,
          stationId: '23',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S034',
          orgName: '湖南长沙梨江路加油站',
          distance: '10.4',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市长沙县泉塘街道东四路12号水利水电学校旁',
          longitude: '113.109837',
          latitude: '28.217331',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A2F',
          stationType: null,
          stationId: '34',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S012',
          orgName: '湖南长沙中意路加油站',
          distance: '10.9',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市天心区中意二路292号',
          longitude: '112.986386',
          latitude: '28.064441',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A0N',
          stationType: null,
          stationId: '12',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S024',
          orgName: '湖南长沙机梨西加油站',
          distance: '11.2',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市长沙县榔梨镇黄兴大道西侧八字槽门广场对面',
          longitude: '113.140908',
          latitude: '28.182306',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A23',
          stationType: null,
          stationId: '24',
          bookingRefueling: null,
        },
        {
          activityList: [],
          orgCode: '1-A4301-C001-S036',
          orgName: '湖南长沙黄兴镇加油站',
          distance: '11.4',
          tagList: [],
          stationStatus: '20',
          phone: '***********',
          address: '长沙市长沙县黄兴镇树新路与035县道交汇处打卦岭村',
          longitude: '113.146321',
          latitude: '28.140814',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A2I',
          stationType: null,
          stationId: '36',
          bookingRefueling: null,
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('oilstation.station.list.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取网点基本信息并根据经纬度计算距离 /oilstation/getDistanceByStationCode
export function getDistanceByStationCodeApi(data, config) {
  const mockData = {
    success: true,
    data: {
      stationCode: '',
      stationName: '',
      distance: '',
      stationStatus: '',
      address: '',
      longitude: '',
      latitude: '',
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('oilstation.getDistanceByStationCode.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取油站油品集合 /oilstation/station/getFuelGunByOrgCode
export function getFuelGunByOrgCodeApi(data, config) {
  const mockData = {
    success: true,
    data: [
      {
        fuelNo: '300667',
        fuelGunNo: ['1', '2', '4', '11', '28', '46'],
        fuelName: '95号 车用汽油(Ⅴ)',
        fuelType: 12,
      },
    ],
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('oilstation.station.getFuelGunByOrgCode.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [查询服务内容列表/oilstation/station/stationServiceList接口]
export function getStationServiceListApi(data, config) {
  const mockData = {
    success: true,
    data: [
      { name: '普通', code: 1 },
      { name: '加气', code: 2 },
      { name: '便利店', code: 3 },
    ],
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('oilstation.station.stationServiceList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
