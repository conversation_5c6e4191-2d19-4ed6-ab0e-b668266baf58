<template>
  <div class="page-container">
    <template v-if="showHeader">
      <zyzx-data-list ref="dataList" :showEmpty="false" :isLoad="false" @refreshPullDown="refreshPullDown">
        <div class="page-header">
          <zyzx-company-bar @onSelect="onCompanySwitch()" :menu="true" :menuList="COMPANY_BAR_MENU"></zyzx-company-bar>
          <header-menu v-if="[0].includes(tokenInfo.fleetType)"></header-menu>
        </div>

        <div class="map-content" :style="{ height: '38vh' }">
          <p-map :mapId="'map-home-header'" pageChannel="home" :showMap="showMap" :iconTop="true" :height="'38vh'"></p-map>
        </div>
      </zyzx-data-list>
    </template>
    <template v-else>
      <div class="map-content" style="height: 38vh">
        <p-map :mapId="'map-home'" :showMap="showMap" :iconTop="true" height="38vh"></p-map>
      </div>
    </template>

    <!-- e享加油 -->
    <div class="page-content" v-if="[1].includes(tokenInfo.fleetType)">
      <div class="status-bar" v-if="refuelStatus != 'DEFAULT'">
        <div class="available" v-if="refuelStatus == 'AVAILABLE' && refuelCode && orderCountDown">
          <u-count-down :time="orderCountDown" format="mm:ss" @change="countDownChange">
            <div class="timer">
              {{ timeData.minutes >= 10 ? timeData.minutes : '0' + timeData.minutes }}:{{
                timeData.seconds >= 10 ? timeData.seconds : '0' + timeData.seconds
              }}
            </div>
          </u-count-down>
          <div class="tips">加油码超过60分钟未使用，订单取消，自动退款</div>
        </div>
        <div class="inservice" v-if="refuelStatus == 'INSERVICE'">
          <div class="dot-left">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <div class="text">加油服务中</div>
          <div class="dot-right">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
      <view class="refuel-panel">
        <div class="panel-content">
          <!-- 未下单状态 -->
          <template v-if="refuelStatus == 'DEFAULT'">
            <div class="station-info">
              <div class="info">
                <template v-if="!isLocalPre">
                  <div class="no-location" @click="showAppPermissionModal()"
                    >{{ isIos ? '暂未查询到油站' : '位置权限未开启,暂未查询到油站' }}
                    <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                  </div>
                  <div class="no-address">暂无地址</div>
                </template>
                <template v-else>
                  <div class="name-row" v-if="!stationInfo || !stationInfo.orgCode">暂未查询到网点</div>
                  <div class="name-row" v-else @click="toStationList">
                    <div class="name text-overflow">{{ stationInfo.orgName }}</div>
                    <div class="tag color-118920" v-if="stationInfo.stationStatus == 20 || stationInfo.stationStatus == 10">正常营业 </div>
                    <div class="tag color-6A6A6A" v-if="stationInfo.stationStatus == 30 || stationInfo.stationStatus == 50">暂停营业 </div>
                    <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                  </div>
                  <div class="address-row" @click="toStationList">{{ stationInfo.address }}</div>
                </template>
              </div>

              <div class="navigation" v-if="!isLocalPre" @click="showAppPermissionModal()">
                <image class="icon" src="@/static/images/oil-station-navigation.png"></image>
              </div>
              <div class="navigation" v-if="isLocalPre && stationInfo && stationInfo.distanceInKm" @click="openLocation()">
                <image class="icon" src="@/static/images/oil-station-navigation.png"></image>
                <div>{{ stationInfo.distanceInKm || '' }}km</div>
              </div>
            </div>
            <div class="oil-box">
              <div class="car-info">
                <div class="bd-info-header" v-if="driverWalletList.length" @click="onAccountPopupShow()">
                  <div class="bd-info-licencePlate">{{ driverWalletInfo.licencePlate }}</div>
                  <span class="bd-info-trans">切换</span>
                  <image class="icon-right" src="@/static/images/icon-arrow-right.png"></image>
                </div>
                <div class="bd-info-header" v-else>
                  <div class="bd-info-licencePlate">暂无车牌卡信息</div>
                </div>
              </div>
              <div class="oil-info">
                <div class="select-row">
                  <!-- app无定位权限 -->
                  <div class="no-location" v-if="!isLocalPre">{{ isIos ? '暂未查询到油站' : '位置权限未开启,暂未查询到油站' }} </div>
                  <!-- app有定位权限 -->
                  <template v-else>
                    <!-- 油站不支持e享加油 -->
                    <div class="no-service" v-if="stationInfo && stationInfo.orgCode && stationInfo.bookingRefueling != 1">
                      <div>此站不支持e享加油</div>
                      <div>请切换油站或在“加油”选择其它加油方式</div>
                    </div>
                    <template v-else>
                      <div class="oil-num">
                        <div class="title">加注品号</div>
                        <div class="input-wrap" @click.stop="chooseOilNumber('oilDialogFlag')">
                          <!-- <span class="text" v-if="seletFuelActive && seletFuelActive.fuelName">{{
                    getOilNum(seletFuelActive.fuelName) || ''
                  }}</span> -->
                          <span class="text" v-if="seletFuelActive && seletFuelActive.fuelName">{{ seletFuelActive.fuelName || '' }}</span>
                          <span class="placeholder" v-else>请选择</span>
                        </div>
                      </div>
                      <div class="oil-amount">
                        <div class="title">加注金额</div>
                        <div class="input-wrap" @click.stop="chooseOilNumber('moneyDialogFlag')">
                          <div class="text" v-if="preaAuthNumber">
                            <div class="amount-icon"></div>
                            <div>{{ preaAuthNumber }}</div>
                          </div>
                          <div class="placeholder" v-else>请输入</div>
                        </div>
                      </div>
                    </template>
                  </template>
                </div>
                <div class="wallet-row">
                  <div class="title">余额</div>
                  <div class="amount">
                    <span class="amount-icon">￥</span>
                    <span>{{ accountAmount(driverWalletInfo.walletAccountList, 'availableAmount') }}</span>
                  </div>
                </div>
              </div>
              <div class="tips-info" @click="handleToCardList">
                <span>我的钱包</span>
                <image class="icon-right" src="@/static/images/icon-arrow-right.png"></image>
              </div>
            </div>
          </template>

          <!-- 下单状态 -->
          <template v-if="refuelStatus != 'DEFAULT'">
            <div class="station-info">
              <div class="info">
                <div class="name-row" v-if="!preStationData || !preStationData.stationCode">暂未查询到网点</div>
                <template v-else>
                  <div class="name-row">
                    <div class="name text-overflow">{{ preStationData.stationName }}</div>
                    <div class="tag color-118920" v-if="preStationData.stationStatus == 20 || preStationData.stationStatus == 10"
                      >正常营业
                    </div>
                    <div class="tag color-6A6A6A" v-if="preStationData.stationStatus == 30 || preStationData.stationStatus == 50"
                      >暂停营业
                    </div>
                  </div>
                  <div class="address-row">{{ preStationData.address }}</div>
                </template>
              </div>
              <div class="navigation" v-if="preStationData && preStationData.distance" @click="openLocation()">
                <image class="icon" src="@/static/images/oil-station-navigation.png"></image>
                <div>{{ preStationData.distanceInKm || '' }}km</div>
              </div>
            </div>
            <div class="oil-box">
              <div class="order-info">
                <div class="hint">
                  <span>请在加油机上输入授权码</span>
                  <template v-if="preStationData.distanceInM <= distanceLimit && refuelCode">
                    <image class="eye-fill" src="@/static/images/oil-eye-fill.png" v-if="!showCode" @click="codeStatusChange"></image>
                    <image class="eye-off" src="@/static/images/oil-eye-off.png" v-else @click="codeStatusChange"></image>
                  </template>
                </div>
                <div class="code-row">
                  <template v-if="preStationData.distanceInM > distanceLimit">
                    <div class="code">****</div>
                    <image src="@/static/images/oil-icon-refresh.png" @click="handleOrder()"></image>
                  </template>
                  <template v-else>
                    <div class="code">{{ showCode ? refuelCode : '****' }}</div>
                  </template>
                </div>
                <div class="detail">
                  <span>加注品号：{{ getOilNum(preOrderData.productName) || '' }}</span>
                  <span>加注金额（元）：{{ preOrderData.preAuthAmount }}</span>
                </div>
              </div>
              <div class="tips-info">
                <template v-if="refuelStatus == 'AVAILABLE'">为保证交易安全，到站后显示授权码，如不显示可刷新授权码 </template>
                <template v-else-if="refuelStatus == 'INSERVICE'">加注完成将根据实际订单金额完成扣款</template>
              </div>
            </div>
          </template>

          <div class="btn-box">
            <button
              class="custom-btn-block circle red"
              :class="{ disabled: !isOil || !driverWalletList.length }"
              size="default"
              v-if="refuelStatus == 'DEFAULT'"
              @click.stop="submitOrderBefore()"
              :disabled="!isOil || !driverWalletList.length"
              >e享加油
            </button>
            <button
              class="custom-btn-block circle red-plain no-border"
              size="default"
              v-if="refuelStatus != 'DEFAULT' && refuelStatus != 'INSERVICE'"
              @click="cancelOrder()"
              >取消订单
            </button>
          </div>
        </div>
      </view>
    </div>
    <!-- 会员码支付 -->
    <div class="page-content" v-else>
      <view class="refuel-panel">
        <div class="panel-content">
          <div class="station-info">
            <div class="info">
              <template v-if="!isLocalPre">
                <div class="no-location" @click="showAppPermissionModal()">
                  {{ isIos ? '暂未查询到油站' : '位置权限未开启,暂未查询到油站' }}
                  <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                </div>
                <div class="no-address">暂无地址</div>
              </template>
              <template v-else>
                <div class="name-row" v-if="!stationInfo || !stationInfo.orgCode">暂未查询到网点</div>
                <div class="name-row" v-else @click="toStationList">
                  <div class="name text-overflow">{{ stationInfo.orgName }}</div>
                  <div class="tag color-118920" v-if="stationInfo.stationStatus == 20 || stationInfo.stationStatus == 10">正常营业 </div>
                  <div class="tag color-6A6A6A" v-if="stationInfo.stationStatus == 30 || stationInfo.stationStatus == 50">暂停营业 </div>
                  <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                </div>
                <div class="address-row" @click="toStationList">{{ stationInfo.address }}</div>
              </template>
            </div>

            <div class="navigation" v-if="!isLocalPre" @click="showAppPermissionModal()">
              <image class="icon" src="@/static/images/oil-station-navigation.png"></image>
            </div>
            <div class="navigation" v-if="isLocalPre && stationInfo && stationInfo.distanceInKm" @click="openLocation()">
              <image class="icon" src="@/static/images/oil-station-navigation.png"></image>
              <div>{{ stationInfo.distanceInKm || '' }}km</div>
            </div>
          </div>
          <div class="bd-info">
            <div class="bd-info-content">
              <div class="bd-info-header" @click="onAccountPopupShow()">
                <div class="bd-info-licencePlate">{{ driverWalletInfo.licencePlate }}</div>
                <span class="bd-info-trans">切换</span>
                <image class="icon-right" src="@/static/images/icon-arrow-right.png"></image>
              </div>
              <div class="bd-info-value">
                <div class="bd-info-amount">
                  <div class="title">钱包余额</div>
                  <div class="value"
                    ><span class="amount-icon">￥</span>{{ accountAmount(driverWalletInfo.walletAccountList, 'availableAmount') }}
                  </div>
                </div>
                <div class="bd-info-discount">
                  <div class="title">优惠金</div>
                  <div class="value"> &nbsp;{{ accountAmount(driverWalletInfo.loyaltyAccountList, 'availablePointAmount', 13) }} </div>
                </div>
              </div>
              <div class="bd-info-integral">
                <div class="title">积分</div>
                <div class="value"> &nbsp;{{ accountAmount(driverWalletInfo.loyaltyAccountList, 'availablePointAmount', 8) }} </div>
              </div>
            </div>
            <div class="tips-info" @click="handleToCardList">
              <span>我的钱包</span>
              <image class="icon-right" src="@/static/images/icon-arrow-right.png"></image>
            </div>
          </div>
        </div>
        <div class="btn-box">
          <button
            class="custom-btn-block circle red"
            :class="{ disabled: !driverWalletInfo.licencePlate }"
            size="default"
            @click="codePay()"
            >会员码支付
          </button>
        </div>
      </view>
    </div>

    <root-portal :enable="true">
      <account-popup ref="accountPopup"></account-popup>
    </root-portal>

    <root-portal :enable="true">
      <u-popup :show="showOilPopup" mode="center" round="10" :safeAreaInsetBottom="false">
        <div class="oil-modal">
          <div class="oil-pop">
            <!-- 无油号选择 -->
            <div class="no-fuedata" v-if="oilDialogFlagType == 'noFuelDataDialogFlag'">
              <div>当前网点非加油站或正在维护中</div>
            </div>

            <!-- 油号选择 -->
            <div class="oil-num-content" v-if="oilDialogFlagType == 'oilDialogFlag' && fuelData.length > 0">
              <div class="title">请选择油品号</div>
              <div class="num-list">
                <div
                  :class="seletFuel.fuelNo == item.fuelNo ? 'active-item' : 'item'"
                  class="num-item"
                  v-for="(item, index) in fuelData"
                  :key="index"
                  @click.stop="chooseOilItem(item)"
                >
                  <div class="f-1">{{ item.fuelName }}</div>
                  <u-icon name="checkbox-mark" color="#ff6b2c" size="20" v-if="seletFuel.fuelNo == item.fuelNo"></u-icon>
                </div>
              </div>
            </div>

            <!-- 加油金额 -->
            <div v-if="oilDialogFlagType == 'moneyDialogFlag'">
              <div class="money-num-content">
                <div class="content-title">本次加油金额</div>
                <div
                  class="warning-text"
                  v-if="Number(priceText) > Number(accountAmount(driverWalletInfo.walletAccountList, 'availableAmount'))"
                >
                  当前输入金额已超过余额请充值~
                </div>
                <div class="tips-text" v-else>
                  <div>最大加油金额为5000元</div>
                  <div>最小加油金额为10元</div>
                </div>
                <div
                  class="price-input-area"
                  :class="{
                    'card-input-border': Number(priceText) > Number(accountAmount(driverWalletInfo.walletAccountList, 'availableAmount')),
                  }"
                >
                  <input type="number" class="price-input" @input="otherAmountsInput" placeholder="请输入加油金额" v-model="priceText" />
                </div>
              </div>
            </div>
          </div>

          <view class="slot-btn-box">
            <view class="btn cancel_btn" :style="{ color: cancelColor }" @click.stop="clickBtn('cancel')" v-if="cancelText">
              {{ cancelText }}
            </view>
            <view
              class="btn confirm"
              :style="{
                color: confirmColor,
                background: confirmBackgroundColor,
              }"
              @click.stop="clickBtn('confirm')"
              v-if="confirmText"
              >{{ confirmText }}
            </view>
          </view>
        </div>
      </u-popup>
    </root-portal>
    <petro-layout ref="layout" :petroKeyboard="true"></petro-layout>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import {
  generateOrderApi,
  preAuthCancelApi,
  getRefuelCodeApi,
  getPreAuthOrderApi,
  getUnPayPreAuthOrderApi,
  getMainAccountInfoByBDCardNoApi,
} from '@/services/http';
import { COMPANY_BAR_MENU, FUEL_TYPE } from '@/services/enum';

import headerMenu from './components/header-menu.vue';
import pMap from '@/components/p-map/p-map.vue';
import zyzxCompanyBar from '@/components/zyzx-company-bar/zyzx-company-bar.vue';
import AccountPopup from '@/components/page-refuel-oil/components/account-popup.vue';

export default {
  name: 'page-home',
  components: {
    AccountPopup,
    pMap,
    headerMenu,
    zyzxCompanyBar,
  },
  props: {
    showMap: {
      type: Boolean,
      default: true,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      osName: uni.$petro.store.systemInfo.osName,
      FUEL_TYPE: FUEL_TYPE,
      // 加油距离限制 TODO
      distanceLimit: 500,
      // 加油状态 DEFAULT(默认状态)、AVAILABLE(待使用)、INSERVICE(加油中)
      refuelStatus: 'DEFAULT',
      showOilPopup: false,
      // 打开油品弹窗标识
      oilDialogFlagType: '',
      cancelText: '取消',
      cancelColor: '#666',
      confirmText: '确认',
      confirmColor: '#000',

      // 油品选择
      seletFuel: {},
      seletFuelActive: {},
      // 显示的预授权金额
      preaAuthNumber: '',
      // 输入框的预授权金额
      priceText: '',

      // 下单成功的数据
      preOrderData: {},
      // 下单油站数据
      preStationData: {},
      // e享加油码
      refuelCode: '',
      //是否存在预授权订单
      circularOrder: false,

      // 订单倒计时24小时时间
      orderCountDown: 0,
      timeData: {},

      // 是否显示code
      showCode: true,

      // 获取加油码轮询id
      getCodeTaskId: null,
      // 预售权订单轮询id
      preOrderTaskId: null,
      COMPANY_BAR_MENU,

      // 权限
      permissions: null,
      // 为了记录用户第一次进来点的是去开启还是取消,再切换回来app后需要操作(zk要求)
      sysModalStatus: '',

      // 初始化查询预授权订单表示
      initPre: true,
      isReading: false, // 是否读卡中
      nfcCode: '100', // nfc状态码 100-正常 101-nfc不支持 102-nfc未打开
    };
  },
  computed: {
    ...mapState({
      companyInfo: state => state.company?.companyInfo,
      stationList: state => state.station?.stationList,
      stationInfo: state => state.station?.stationInfo,
      fuelData: state => state.station?.fuelData,
      carInfo: state => state.car?.carInfo,
      walletInfo: state => state.account?.walletInfo,
      serialNo: state => state.account?.walletInfo?.serialNo,
      role: state => state?.roles?.role,
      userInfo: state => state?.roles?.userInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
      accountInfo: state => state.company?.accountInfo,
      pagePre: state => state.tabBar?.tabBarPermissions?.home,
      tabBarValue: state => state?.tabBar?.tabBarValue,
      cardInfo: state => state?.accountDriver?.cardInfo,
      driverWalletInfo: state => state?.accountDriver?.driverWalletInfo,
      driverWalletList: state => state?.accountDriver?.driverWalletList,
    }),
    isLocalPre() {
      if (this.pagePre && !this.pagePre.localPre) {
        return false;
      }
      return true;
    },
    isOil() {
      // 油站
      if (!this.stationInfo?.orgCode) {
        return false;
      }
      // 油品号
      if (!this.seletFuelActive?.fuelName) {
        return false;
      }
      // 金额
      if (!this.preaAuthNumber) {
        return false;
      }
      return true;
    },
    isIos() {
      return this.osName == 'ios';
    },
  },
  watch: {
    'accountInfo.enterpriseAccountNo': {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.getWalletList();
        }
      },
      immediate: true,
    },
    // 监听回到首页
    tabBarValue: {
      handler(newValue, oldValue) {
        if (oldValue && newValue === 'refuel-home') {
          // 刷新钱包信息
          this.$store.dispatch('getWalletInfo', { staffRole: 4, refresh: true });
          this.getWalletList();
          this.onCompanySwitch();
        }
      },
    },
    // 监听倒计时展示状态, 清除倒计时
    refuelStatus(newVal, oldVal) {
      if (newVal !== 'AVAILABLE' && this.orderCountDown) {
        this.orderCountDown = 0;
      }
    },
    // 监听油站改变, 清除已选择油号
    'stationInfo.orgCode': {
      handler(newVal, oldVal) {
        if (newVal) {
          this.seletFuel = {};
          this.seletFuelActive = {};
          this.preaAuthNumber = '';
          this.priceText = '';
        }
      },
      immediate: false,
    },
    // 监听初始化完成并且企业获取未支付订单
    'userInfo.staffNo': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.companyInfo.enterpriseNo && this.initPre) {
          this.initPre = false;
          this.onCompanySwitch();
        }
      },
      immediate: false,
    },
  },
  async mounted() {
    uni.onAppShow(async res => {
      this.permissions = await this.getPermissions();
      // 系统和app都有权限, 并且当前油站列表没有数据, 获取油站列表
      if (this.permissions?.gps == 1 && !this.stationList?.length) {
        this.$store.commit('updateLocalPre', {
          page: 'home',
          localPre: true,
          isDefaultGPS: false,
        });
        this.$store.commit('updateLocalPre', {
          page: 'oil',
          localPre: true,
          isDefaultGPS: false,
        });
        this.$store.dispatch('getStationList');
      }

      // zk要求: 安卓流程,用户打开app后无权限，弹出系统权限弹窗点击去设置后再回来app,需要再次判断弹出app权限弹窗,只弹一次
      if (this.osName == 'android' && this.sysModalStatus === 'confirm' && [2, 4].includes(this.permissions?.gps)) {
        this.sysModalStatus = '';
        this.showAppPermissionModal();
      }
    });

    // 权限判断
    // 1 系统开,app开
    // 2 系统开,app关
    // 3 系统关,app开
    // 4 系统关,app关
    this.permissions = await this.getPermissions();
    console.log('permissions:', this.permissions);
    if ([3, 4].includes(this.permissions?.gps)) {
      // 系统权限未开启
      this.showSysPermissionModal();
    } else if ([2].includes(this.permissions?.gps)) {
      // app权限未开启
      this.showAppPermissionModal();
    } else {
      this.$store.commit('updateLocalPre', {
        page: 'home',
        localPre: true,
        isDefaultGPS: false,
      });
      this.$store.commit('updateLocalPre', {
        page: 'oil',
        localPre: true,
        isDefaultGPS: false,
      });
      this.$store.dispatch('getStationList');
    }
  },
  methods: {
    /**
     * 根据账户类型获取账户金额
     *
     * @param item 账户数据数组
     * @param value 需要获取的金额类型，如'availableAmount'
     * @param key 账户类型，默认为5
     * @returns 返回指定账户类型的金额，如果未找到则返回0
     */
    accountAmount(item = [], value, key = 5) {
      const data = (item || []).find(it => key == it.accountType);
      if (data) {
        return data[value] || '0';
      } else {
        return 0;
      }
    },
    // 切换读卡状态
    async handleChangeReadStatus() {
      if (!this.isReading) {
        this.nfcCode = await this.checkNfcStatus();
        if (!this.nfcCode || this.nfcCode == '101') return;
        if (this.nfcCode == '102') {
          uni.showModal({
            title: '未开启NFC',
            content: '是否确认开启？',
            showCancel: true,
            confirmText: '确认',
            success: async modalRes => {
              if (modalRes.confirm) {
                await this.checkNfcStatus(true);
              }
            },
          });
          return;
        }
        this.startReadCard();
      } else {
        // this.stopReadCard();
      }
      this.isReading = !this.isReading;
    },
    // BD卡详情
    handleToCardList() {
      uni.$petro.route(
        {
          url: '/pages/wallet/pages/car-wallet-list',
          type: 'to',
          params: {},
        },
        true,
      );
    },
    // 重新刷卡
    refreshCard(refresh = true) {
      if (this.tokenInfo?.fleetType === 2) {
        this.$store.dispatch('getCardInfo', { bdCardNo: '' });
        refresh && this.startReadCard();
      }
    },
    // 判断NFC权限
    async checkNfcStatus(goToNfcSettings = false) {
      return new Promise(async resolve => {
        try {
          const res = await uni.$petro.Bridge.zyzx.oilNfc({
            bizType: 'checkNfcStatus', // 检查NFC状态
            data: {
              showTips: false, // 不支持或未开启是否toast提示，默认false
              goToNfcSettings, // 未打开NFC是否去设置，默认false
            },
          });
          resolve(res?.code);
        } catch (error) {
          console.error('checkNfcStatus--', error);
          resolve(false);
        }
      });
    },
    // NFC刷卡
    async startReadCard() {
      try {
        const { success, data } = await uni.$petro.Bridge.zyzx.oilNfc({
          bizType: 'startReadCard',
          data: {
            cardGeneration: 'first',
          },
        });
        console.log(data);
        if (!success || !data?.cardId) return;
        this.getCardDetailInfo(data?.cardId);
      } catch (error) {
        console.error('startReadCard--', error);
        this.handleChangeReadStatus();
      }
    },
    // 获取BD卡详情
    async getCardDetailInfo(cardId) {
      try {
        const { success, data } = await getMainAccountInfoByBDCardNoApi({
          bdCardNo: cardId,
          businessNo: this.companyInfo?.businessNo,
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
        });
        if (!success) return this.refreshCard();
        this.$store.dispatch('getCardInfo', {
          ...data?.[0],
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
        });
      } catch (error) {
        console.error('getCardDetailInfo--', error);
        this.refreshCard();
      }
    },
    // NFC停止刷卡
    async stopReadCard() {
      try {
        const res = await uni.$petro.Bridge.zyzx.oilNfc({
          bizType: 'stopReadCard',
          data: {},
        });
        console.log(res);
      } catch (error) {
        console.error('stopReadCard--', error);
      }
    },
    // 会员码支付
    codePay() {
      this.$store.commit('switchOilTab', 'code');
      uni.$petro.route('/pages/member-code/member-code');
    },
    // 权限-显示系统定位权限弹窗
    async showSysPermissionModal() {
      uni.showModal({
        title: '定位服务未开启,无法根据您的位置信息为您服务,如不开启则为您选择默认城市服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            this.sysModalStatus = 'confirm';
            // 跳去系统设置界面
            if (this.osName == 'ios') {
              this.toSysSetting();
            } else {
              this.toSysSetting('locsyssetting');
            }
          } else {
            this.sysModalStatus = 'cancel';
            // ios无法检测系统关,app开的情况,不做处理
            if (this.osName == 'android') {
              if (this.permissions?.gps == 3) {
                this.$store.commit('updateLocalPre', {
                  page: 'home',
                  localPre: true,
                  isDefaultGPS: true,
                });
                // 系统关,app开 设置默认定位
                const defaultGPS = uni.$petro.config?.defaultGPS || {};
                this.$store.dispatch('getStationList', defaultGPS);
              } else if (this.permissions?.gps == 4) {
                // 系统关,app关 显示app设置提示弹窗
                this.showAppPermissionModal();
              }
            }
          }
        },
      });
    },
    // 权限-显示app定位权限弹窗
    async showAppPermissionModal() {
      uni.showModal({
        title: '位置权限未开启,无法根据您的位置信息获取您附近的加油站网点信息为您服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            // 跳去app设置界面
            this.toSysSetting();
          }
          // 界面显示无位置信息状态
        },
      });
    },
    // 权限-跳转系统设置页面
    async toSysSetting(url = 'setting') {
      const res = await uni.$petro.Bridge.zyzx.launchUrl({
        url: url,
      });
    },
    // 权限-获取权限
    getPermissions() {
      // TODO 测试
      // if (my.isIDE) {
      //   return {
      //     gps: 1,
      //   };
      // }
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera', 'gps'],
        });
        resolve(res);
      });
    },
    // 切换企业成功
    async onCompanySwitch() {
      this.refreshCard(false);
      this.isReading = false;
      // 清除定时器
      this.clearIntervalAll();
      // 重置油品和金额
      this.seletFuel = {};
      this.seletFuelActive = {};
      this.preaAuthNumber = '';
      this.priceText = '';
      // 重置订单参数
      this.refuelStatus = 'DEFAULT';
      this.preOrderData = {};
      this.preStationData = {};
      this.refuelCode = '';
      // 查询是否有未完成预授权订单
      this.getUnPayPreAuthOrder();
    },
    // 查询未完成预授权订单
    async getUnPayPreAuthOrder() {
      try {
        const params = {
          businessNo: this.companyInfo?.businessNo, // 业务编号
          enterpriseNo: this.companyInfo?.enterpriseNo, // 单位会员编号
          memberType: 2, // 会员类型
          staffNo: this.userInfo?.staffNo, // 单位员工编号
        };
        const { success, data } = await getUnPayPreAuthOrderApi(params);
        if (!success || !data?.length) return;
        this.preOrderData = data[0];
        // 预授权订单状态: 1创建、5已取消、3开始加油、4加油结束
        if (this.preOrderData?.preOrderStatus == 1) {
          if (!this.preOrderData?.fuelCode) {
            return this.cancelOrderModal();
          } else {
            // 处理订单
            this.handleOrder();
          }
        } else if (this.preOrderData?.preOrderStatus == 3) {
          // 处理订单
          this.handleOrder();
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 一键加油提交订单
    async submitOrderBefore() {
      if (this.stationInfo.stationStatus != 20 && this.stationInfo.stationStatus != 10) {
        uni.showToast({
          title: '网点暂未营业',
          icon: 'none',
        });
        return;
      }
      if (Object.keys(this.seletFuelActive).length == 0 || this.seletFuelActive.fuelName == '') {
        uni.showToast({
          title: '请选择油号',
          icon: 'none',
        });
        return;
      }
      if (!this.preaAuthNumber) {
        uni.showToast({
          title: '请输入预授权金额',
          icon: 'none',
        });
        return;
      }
      try {
        const params = {
          businessNo: this.companyInfo?.businessNo,
          enterpriseNo: this.companyInfo?.enterpriseNo,
          stationCode: this.stationInfo?.orgCode, // 网点编码
          stationName: this.stationInfo?.orgName, // 网点名称
          preAuthAccount: this.accountAmount(this.driverWalletInfo.walletAccountList, 'cardNo'), // 电子钱包账号（e享加油必填）
          productNo: this.seletFuelActive.fuelNo, // 商品编码（油品编码)
          productName: this.seletFuelActive.fuelName, // 商品名称（油品名称）
          preAuthAmount: this.preaAuthNumber, // 预授权金额（e享加油必填，单位：元）
          usedInterestsAccount: '1', // 是否使用权益账户（e享加油，默认使用）1—是；0—否
          memberType: 2, // 1(个人会员)、2(单位会员) 固定传2
          staffNo: this.userInfo?.staffNo, // 单位员工编号
          // v430新增账户信息字段
          enterpriseAccountNo: this.driverWalletInfo?.enterpriseAccountNo, // 单位账户编号
          mainAccountNo: this.driverWalletInfo?.mainAccountNo, // 主账户编号
          petroChinaNo: this.accountInfo?.petroChinaNo, // 人员档案编号
        };
        if (this.driverWalletInfo?.licencePlate) {
          params.licensePlate = this.driverWalletInfo?.licencePlate;
        }
        // 如果需要强校验车牌号，需要传车牌号
        if (this.driverWalletInfo?.checkLicensePlate == '1') {
          params.licensePlateNum = this.driverWalletInfo?.licencePlate; // 车牌强校验字段，传值则校验、反之则不校验
        }
        const res = await generateOrderApi(params);
        if (res.success) {
          this.preOrderData = res.data;
          // 调用插件进行预下单
          const status = await this.callAccount();
          if (status) {
            // 刷新钱包信息
            this.$store.dispatch('getWalletInfo', { staffRole: 4, refresh: true });
            this.getWalletList();
            // 处理订单
            this.handleOrder();
          }
        } else {
          // 有待支付订单弹窗提示并取消
          if (res.errorCode === 'B_B05_600302') {
            return this.cancelOrderModal('同一时间下单人数过多，请稍候再试');
          } else {
            uni.showModal({
              title: '获取订单失败，请重新预约！',
              confirmText: '确认',
              showCancel: false,
              success: async res => {
                // if (res.confirm) {
                //   const isSuccess = await this.cancelOrder();
                //   if (!isSuccess) {
                //     this.cancelOrderModal();
                //   }
                // }
              },
            });
          }
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 调用支付插件
    async callAccount() {
      if (
        !this.userInfo?.staffNo ||
        !this.userInfo?.petroChinaNo ||
        (!this.serialNo && this.serialNo != 0) ||
        !this.preOrderData?.preAuthOrderNo
      ) {
        console.log(
          '参数异常',
          JSON.stringify({
            ...this.userInfo,
            serialNo: this.serialNo,
            preAuthOrder: this.preOrderData,
          }),
        );
        return uni.showModal({
          title: '参数异常',
          confirmText: '确认',
          showCancel: false,
        });
      }
      try {
        const stationInfo = await this.refreshDistance();
        const payInfo = {
          mainAccountNo: this.accountInfo?.mainAccountNo, // 主账户编号
          unitMainAccountNo: this.accountInfo?.enterpriseAccountNo, // 单位主账户编号
          enterpriseNo: String(this.companyInfo?.enterpriseNo || ''), // 单位会员编号
          businessNo: String(this.role?.businessNo || ''), // 业务编码
          enterpriseStaffNo: String(this.userInfo?.staffNo || ''), // 单位员工编号
          petrolChinaNo: String(this.userInfo?.petroChinaNo || ''), // 电子钱包账号
          memberType: String(2), // 1(个人会员)、2(单位会员)
          amount: String(this.preOrderData?.preAuthAmount || ''), // 金额
          stationCode: String(this.preOrderData?.stationCode || ''), // 网点编码
          bizOrderNo: String(this.preOrderData?.preAuthOrderNo || ''), // 订单号
          payType: String(5), // 5:电子账户
          extendFiled: '',
        };
        let res;
        if (uni.$petro.PayPlugin?.isPlugin) {
          const params = {
            bizOrderNo: this.preOrderData?.preAuthOrderNo,
            amount: this.preOrderData?.preAuthAmount,
            stationCode: stationInfo?.stationCode,
            extendFiled: '',
            payType: 5,
            bioType: '',
            signature: '',
            bioCode: '',
            accountPreType: 3,
            enterpriseNo: this.companyInfo?.enterpriseNo,
            businessNo: this.companyInfo?.businessNo, // 业务编码
            enterpriseStaffNo: this.userInfo?.staffNo, // 员工编号
            petrolChinaNo: this.tokenInfo?.orgCode,
            mainAccountNo: this.driverWalletInfo?.mainAccountNo, // 主账户编号
            unitMainAccountNo: this.driverWalletInfo?.enterpriseAccountNo, // 单位主账户编号
            userId: this.tokenInfo?.memberNo,
            sourceChannelNo: 53,
            // ...payInfo,
          };
          console.log('预授权下单', JSON.stringify(params), uni.$petro.AccountPlugin?.ref);
          res = await uni.$petro.PayPlugin.UnitQryPreOrder(
            params,
            uni.$petro.AccountPlugin?.ref,
            uni.$petro.AccountPlugin?.securityPluginInstance,
          );
          if (res?.code === 'PAY_SUCCESS') {
            return true;
          }
          console.log('预授权下单结果', res);

          if (!res?.msg?.includes('P_SDK07_200001')) {
            // 不抛错-键盘取消:P_SDK07_200001
            uni.showModal({
              title: res?.msg || '异常',
              content: res?.code,
              confirmText: '确认',
              showCancel: false,
            });
          }
          this.cancelOrder();
          return;
        } else {
          res = await uni.$petro.PayPlugin.caller(
            {
              bizType: 'businessPreOrder', // 企业账户预授权下单
              data: {
                businessInfo: {
                  mainAccountNo: this.accountInfo?.mainAccountNo, // 主账户编号
                  unitMainAccountNo: this.accountInfo?.enterpriseAccountNo, // 单位主账户编号
                  businessNo: this.role?.businessNo, // 业务编码
                  staffNo: this.userInfo?.staffNo, // 员工编号
                  accountType: 5, // 账户类型 1:资金账户 2:积分账户 3:优惠券 4:优惠金 5:e享卡
                  capitalType: 1, // 金额类型 1:资金 2:信用
                },
                businessCodeInfo: {
                  businessIdx: this.serialNo, // 业务序号
                  // charType: 4, // 角色类型 2:管理员 4:司机
                  mainAccountType: Number(this.accountInfo?.accountType),
                },
                businessPayInfo: JSON.stringify(payInfo), // 支付信息
                extInfo: {},
                headers: {},
              },
            },
            {
              ignoreErrs: true,
            },
          );
        }

        console.log('预授权下单结果', res);

        if (res?.success) {
          return true;
        } else {
          // 不抛错-键盘取消:P_SDK07_007000
          if (res?.message.indexOf('P_SDK07_007000') === -1) {
            uni.showModal({
              title: res?.message || '异常',
              content: res?.code,
              confirmText: '确认',
              showCancel: false,
            });
          }
          this.cancelOrder();
        }
      } catch (error) {
        console.error(error);
        this.cancelOrder();
      }
      return false;
      // 同一时间下单人数过多，需要弹窗提示
    },
    // 订单逻辑处理 pre:预授权待支付订单 刷新距离，范围内获取预约码
    async handleOrder() {
      // if (this.refuelStatus == 'DEFAULT') {
      //   this.refuelStatus = 'AVAILABLE';
      // }
      // this.getRefuelCode();
      // return;
      const stationInfo = await this.refreshDistance();
      if (stationInfo?.stationCode && stationInfo?.distanceInM <= this.distanceLimit) {
        this.getRefuelCode();
      }
      this.refuelStatus = 'AVAILABLE';
    },
    // 刷新距离判断是否获取预约码
    async refreshDistance() {
      const stationInfo = await this.$store.dispatch('getDistanceByStationCode', {
        stationCode: this.preOrderData?.stationCode,
      });
      if (!stationInfo?.stationCode) return;
      this.preStationData = { ...this.preStationData, ...stationInfo };
      return stationInfo;
    },
    // 获取加油预约码
    async getRefuelCode() {
      let count = 1;
      if (this.getCodeTaskId) {
        clearInterval(this.getCodeTaskId);
        this.getCodeTaskId = null;
      }
      this.getCodeTaskId = uni.$petro.Utils.createPollingTask(async () => {
        count++;
        const params = {
          preAuthOrderNo: this.preOrderData?.preAuthOrderNo,
          stationCode: this.preOrderData?.stationCode,
        };
        const res = await getRefuelCodeApi(params);
        if (res?.success && res?.data?.refuelCode) {
          this.refuelCode = res.data.refuelCode;
          this.getPreAuthOrder();
          return true;
        }
        if (count > 5) {
          this.cancelOrderModal();
          return true;
        }
      }, 5000);
    },
    // 查询预授权订单(轮询)
    async getPreAuthOrder() {
      if (this.preOrderTaskId) {
        clearInterval(this.preOrderTaskId);
        this.preOrderTaskId = null;
      }
      this.preOrderTaskId = uni.$petro.Utils.createPollingTask(async () => {
        try {
          const params = {
            preAuthOrderNo: this.preOrderData.preAuthOrderNo,
            stationCode: this.preOrderData.stationCode,
          };
          const res = await getPreAuthOrderApi(params);
          if (!this.orderCountDown && this.refuelStatus === 'AVAILABLE' && res.data.createTime) {
            this.startCountdown(res.data.createTime);
          }

          // 预授权订单状态1创建；3开始加油；5已取消；4加油结束；
          if (res.data.preOrderStatus == 1) {
            if (this.refuelStatus !== 'AVAILABLE') {
              this.refuelStatus = 'AVAILABLE';
            }
          } else if (res.data.preOrderStatus == 3) {
            this.refuelStatus = 'INSERVICE';
          } else if (res.data.preOrderStatus == 5) {
            this.refuelStatus = 'DEFAULT';
            this.initOrderData();
            return true;
          } else if (res.data.preOrderStatus == 4) {
            this.refuelStatus = 'DEFAULT';
            await this.initOrderData();
            const orderNo = res?.data?.orderNo;
            // 跳转订单详情
            uni.$petro.route('/pages/pay-result/pay-result', {
              stationCode: this.preOrderData.stationCode,
              orderNo: orderNo,
            });
            return true;
          }
        } catch (error) {
          console.log(error);
        }
      }, 10000);
    },
    // 取消订单
    async cancelOrder() {
      let isSuccess = false;
      try {
        const params = {
          preAuthOrderNo: this.preOrderData.preAuthOrderNo,
          stationCode: this.preOrderData.stationCode,
        };
        const res = await preAuthCancelApi(params);
        if (res && res.success) {
          this.refuelStatus = 'DEFAULT';
          this.initOrderData();
        }
        isSuccess = true;
      } catch (error) {
        console.log(error);
      }
      return isSuccess;
    },
    // 初始化界面状态,清除订单数据
    async initOrderData() {
      this.refuelCode = '';
      // 刷新钱包信息
      await this.$store.dispatch('getWalletInfo', { staffRole: 4, refresh: true });
      this.getWalletList();
      // this.preOrderData = {};
      // 清除定时器
      this.clearIntervalAll();
    },
    // 预售权订单取消
    async cancelOrderModal(text = '') {
      uni.showModal({
        title: text || '获取订单失败，请重新预约！',
        confirmText: '确认',
        showCancel: false,
        success: async res => {
          if (res.confirm) {
            const isSuccess = await this.cancelOrder();
            if (!isSuccess) {
              this.cancelOrderModal(text);
            }
          }
        },
      });
    },
    // 选择油号,金额弹窗
    async chooseOilNumber(data) {
      if (!this.stationInfo?.orgCode) {
        return uni.showToast({
          title: '请选择网点',
          icon: 'none',
        });
      }
      // if (!this.carInfo?.licensePlate) {
      //   return uni.showToast({
      //     title: '请选择车辆',
      //     icon: 'none',
      //   });
      // }
      this.oilDialogFlagType = data;
      if (this.oilDialogFlagType == 'oilDialogFlag') {
        await this.$store.dispatch('getFuelGunByOrgCodePost');
        if (this.fuelData.length <= 0) {
          this.oilDialogFlagType = 'noFuelDataDialogFlag';
        }
        this.showOilPopup = true;
      } else {
        this.showOilPopup = true;
      }
    },
    // 选择油号
    chooseOilItem(item) {
      this.seletFuel = item;
    },
    // 弹窗按钮事件
    clickBtn(event) {
      this.showOilPopup = false;
      if (event == 'confirm') {
        if (this.confirmText == '确认') {
          // 油品选择
          if (this.oilDialogFlagType == 'oilDialogFlag') {
            this.seletFuelActive = this.fuelData.find(item => this.seletFuel.fuelNo == item.fuelNo);
            console.log('seletFuelActive', this.seletFuelActive);
          }
          // 加油金额输入
          if (this.oilDialogFlagType == 'moneyDialogFlag') {
            if (Number(this.priceText) < 10 || Number(this.priceText) > 5000) {
              uni.showToast({
                title: '输入金额不符合加油金额限制',
                icon: 'none',
              });
              this.priceText = '';
              return;
            } else if (
              // Object.keys(this.walletAccount).length == 0 ||
              this.accountAmount(this.driverWalletInfo.walletAccountList, 'availableAmount') * 1 == 0 ||
              Number(this.priceText) > Number(this.accountAmount(this.driverWalletInfo.walletAccountList, 'availableAmount'))
            ) {
              // 判断输入金额是否大于钱包金额 && 钱包金额必须有值
              uni.showToast({
                title: '余额不足',
                icon: 'none',
              });
              this.priceText = '';
              return;
            } else {
              this.preaAuthNumber = this.priceText;
            }
          }
        }
      }
    },
    // 获取匹配95,92油品字段
    getOilNum(oilTypeStr) {
      return oilTypeStr.replace(/[^0-9]/gi, '') ? oilTypeStr.replace(/[^0-9]/gi, '') : oilTypeStr;
    },
    openLocation() {
      uni.$petro.Utils.debounce(
        () => {
          this.selectType();
        },
        1000,
        this,
      );
    },
    // 打开导航
    async selectType() {
      if (this.pagePre?.isDefaultGPS) {
        uni.showModal({
          title: '定位失败，定位服务未开启，无法定位当前位置，请开启定位服务',
          confirmText: '确认',
          showCancel: false,
        });
        return;
      }

      try {
        let stationLocation = {
          latitude: '',
          longitude: '',
        };
        if (this.refuelStatus !== 'DEFAULT') {
          stationLocation.latitude = this.preStationData?.latitude;
          stationLocation.longitude = this.preStationData?.longitude;
        } else {
          stationLocation.latitude = this.stationInfo?.latitude;
          stationLocation.longitude = this.stationInfo?.longitude;
        }
        const location = await uni.$petro.getLocation({}, true, {
          showModal: false,
        });

        if (!stationLocation?.latitude || !stationLocation?.longitude) return;
        // uni.$petro.openLocation({
        //   startLat: location.latitude,
        //   startLon: location.longitude,
        //   endLat: stationLocation.latitude,
        //   endLon: stationLocation.longitude,
        // });
        uni.$petro.route(
          '/pages/car/pages/car-type',
          {
            startLat: location.latitude,
            startLon: location.longitude,
            endLat: stationLocation.latitude,
            endLon: stationLocation.longitude,
          },
          true,
        );
      } catch (error) {
        console.log(error);
      }
    },
    // 跳转油站列表
    toStationList() {
      uni.$petro.route('/pages/station-list/station-list');
    },
    // 跳转我的车辆
    carChange() {
      uni.$petro.route('/pages/car/car');
    },
    // 订单有效时间倒计时
    startCountdown(timeStr) {
      // 确保传入的timeStr是有效的日期字符串
      if (!timeStr) {
        throw new Error('timeStr must be a valid Date String');
      }
      // 替换空格为T，确保是标准时间字符串(部分ios转换会报Invalid Date)
      timeStr = timeStr.replace(' ', 'T');
      const orderTime = new Date(timeStr);

      // 获取当前时间
      const now = new Date();
      // 计算过期时间（下单时间+24小时）
      const expirationTime = new Date(orderTime.getTime() + 1 * 60 * 60 * 1000);
      // 如果当前时间超过了过期时间，停止倒计时
      if (now >= expirationTime) {
        this.orderCountDown = 0;
        // 订单到期时间已到
        return;
      }
      // 计算剩余时间
      this.orderCountDown = expirationTime - now;
    },
    // 加油码显示状态改变
    codeStatusChange() {
      this.showCode = !this.showCode;
    },
    // 输入自定义金额
    otherAmountsInput(e) {
      let value = e.detail.value;
      let price = value.toString().match(/^\d+(?:\.\d{0,2})?/) || '';
      this.$nextTick(() => {
        this.priceText = typeof price === 'string' ? price : price[0];
        this.confirmText = '确认';
        this.confirmColor = '#000';
      });
    },
    // 倒计时组件change事件
    countDownChange(e) {
      this.timeData = e;
    },
    // 下拉刷新
    async refreshPullDown() {
      console.log('refreshPullDown');
      // this.$store.dispatch('getCarList');
      await this.$store.dispatch('getWalletInfo', { staffRole: 4, refresh: true });
      await this.getWalletList();
      this.$refs.dataList.stopRefresh();
    },
    async getWalletList() {
      try {
        const params = {
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
          refresh: true,
          isBd: [2].includes(this.tokenInfo.fleetType),
        };
        await this.$store.dispatch('getWalletList', { ...params });
      } catch (err) {
        console.error(err);
      }
    },
    // 清除定时器
    clearIntervalAll() {
      clearInterval(this.getCodeTaskId);
      clearInterval(this.preOrderTaskId);
      this.getCodeTaskId = null;
      this.preOrderTaskId = null;
    },
    // 打开选择账户弹窗
    onAccountPopupShow() {
      if ([2].includes(this.tokenInfo.fleetType)) return;
      this.$refs.accountPopup.show();
    },
  },
  destroyed() {
    this.clearIntervalAll();
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #ffffff;
  position: relative;

  .page-header {
    position: relative;
    z-index: 101;
    background: linear-gradient(45deg, #3b4b82 0%, #6370b4 100%);

    .company-bar {
      box-sizing: content-box !important;
      padding: 0 24rpx;
      display: flex;
      align-items: center;

      .name-wrap {
        display: flex;
        align-items: center;

        .name {
          margin-left: 10rpx;
          color: #ffffff;
          font-size: 32rpx;
          margin-right: 10rpx;
        }
      }
    }
  }

  .map-content {
    margin-top: -20rpx;
  }

  .page-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
    min-height: 44vh;
    display: flex;
    flex-direction: column;
    // border-radius: 32rpx 32rpx 0rpx 0rpx;
    // background: linear-gradient(180deg, #ffffff 0%, #f7f7fb 100%);

    .status-bar {
      padding-bottom: 20rpx;
      width: 100%;
      background-color: #ffefef;
      position: absolute;
      left: 0;
      bottom: calc(100% - 20rpx);
      z-index: 9;
      border-radius: 32rpx 32rpx 0rpx 0rpx;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #fa1919;

      .available {
        padding: 23rpx 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fa1919;

        .timer {
          width: 112rpx;
          font-size: 40rpx;
          line-height: 56rpx;
        }

        .tips {
          margin-left: 19rpx;
          font-size: 24rpx;
          line-height: 33rpx;
        }
      }

      .inservice {
        padding: 20rpx 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fa1919;

        span {
          display: inline-block;
          margin-top: 10px;
          height: 4px;
          width: 4px;
          border-radius: 50%;
        }

        .dot-left {
          span:not(:first-child) {
            margin-left: 5px;
          }

          span:nth-child(1) {
            background: #fa1919;
            animation: up-down 1s ease-in-out -1.25s infinite alternate;
          }

          span:nth-child(2) {
            background: #fa1919;
            animation: up-down 1s ease-in-out -1s infinite alternate;
          }

          span:nth-child(3) {
            background: #fa1919;
            animation: up-down 1s ease-in-out -0.75s infinite alternate;
          }
        }

        .dot-right {
          span:not(:first-child) {
            margin-left: 5px;
          }

          span:nth-child(1) {
            background: #fa1919;
            animation: down-up 1s ease-in-out -0.75s infinite alternate;
          }

          span:nth-child(2) {
            background: #fa1919;
            animation: down-up 1s ease-in-out -0.5s infinite alternate;
          }

          span:nth-child(3) {
            background: #fa1919;
            animation: down-up 1s ease-in-out -0.25s infinite alternate;
          }
        }

        .text {
          font-weight: bold;
          font-size: 36rpx;
          line-height: 50rpx;
          padding: 0 60rpx;
        }
      }
    }

    .refuel-panel {
      flex: 1;
      padding: 36rpx 32rpx 0;
      border-radius: 32rpx 32rpx 0rpx 0rpx;
      background: linear-gradient(180deg, #f5f5f5 0%, #f7f7fb 100%);
      z-index: 99;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      padding-bottom: 32rpx;
      box-sizing: border-box;

      .panel-content {
        .station-info {
          width: 100%;
          display: flex;
          align-items: flex-start;
          padding: 0 24rpx 24rpx;
          box-sizing: border-box;

          .info {
            flex: 1;
            overflow: hidden;

            .name-row {
              display: flex;
              align-items: center;

              .name {
                font-weight: 600;
                font-size: 32rpx;
                color: #333333;
                line-height: 45rpx;
              }

              .tag {
                flex-shrink: 0;
                margin-left: 8rpx;
                height: 32rpx;
                line-height: 32rpx;
                padding: 4rpx 12rpx;
                font-size: 20rpx;
                color: #ffffff;
                margin-right: 10rpx;
                border-radius: 8rpx;

                &.color-6A6A6A {
                  color: #6a6a6a;
                }

                &.color-118920 {
                  color: #118920;
                  background: #f3f9f4;
                }
              }
            }

            .address-row {
              margin-top: 12rpx;
              font-size: 24rpx;
              color: #333333;
              line-height: 33rpx;
            }

            .no-location {
              display: flex;
              align-items: center;
              justify-content: left;
              color: #999999;
            }

            .no-address {
              text-align: left;
              margin-top: 12rpx;
              font-size: 28rpx;
              color: #999999;
            }
          }

          .navigation {
            border-radius: 12rpx;
            margin-left: 40rpx;
            flex-shrink: 0;

            image {
              width: 54rpx;
              height: 54rpx;
            }

            div {
              text-align: center;
              font-size: 20rpx;
              color: #333333;
              line-height: 28rpx;
            }
          }

          .more-btn {
            border: 2rpx solid #666;
            border-radius: 12rpx;
            margin-left: 40rpx;
            flex-shrink: 0;
            height: auto;
            text-align: center;
            font-size: 24rpx;
            padding: 4rpx 12rpx;
            line-height: 36rpx;
          }
        }

        .oil-box {
          margin-top: 20rpx;
          background: #ffffff;
          border-radius: 16rpx;
        }

        .bd-info {
          background: #fff;
          border-radius: 16rpx;

          &-image {
            width: 278rpx;
            height: 278rpx;
            display: block;
            margin: 40rpx auto 0;
          }

          &-title {
            margin-top: 18rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            line-height: 33rpx;
            text-align: center;
          }

          &-subTitle {
            margin-top: 8rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 28rpx;
            text-align: center;
          }

          &-content {
            padding: 0 24rpx;
          }

          &-header {
            padding: 24rpx 0 32rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1rpx dashed #dddddd;
          }

          &-licencePlate {
            flex: 1;
            font-size: 32rpx;
          }

          &-trans {
            font-size: 28rpx;
            color: #999;
          }

          &-value {
            display: flex;
            align-items: center;
            margin-top: 24rpx;
          }

          &-amount,
          &-discount,
          &-integral {
            flex: 1;
            display: flex;
            align-items: center;
            height: 68rpx;
            margin-bottom: 8rpx;

            .title {
              font-weight: 500;
              font-size: 28rpx;
              color: #666666;
              line-height: 33rpx;
              margin-right: 20rpx;
            }

            .value {
              font-weight: 500;
              font-size: 40rpx;
              color: #333333;
              line-height: 48rpx;

              .amount-icon {
                font-size: 28rpx;
              }
            }
          }

          &-integral {
            margin-bottom: 16rpx;
          }
        }
      }

      .icon-right {
        width: 28rpx;
        height: 28rpx;
      }

      .car-info {
        padding: 20rpx 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        .bd-info-header {
          width: 100%;
        }

        //&::before {
        //  content: '';
        //  position: absolute;
        //  left: 10rpx;
        //  right: 10rpx;
        //  bottom: 0;
        //  height: 1rpx;
        //  border-bottom: 1rpx dashed #dddddd;
        //}

        .license {
          display: flex;
          align-items: center;
          font-weight: bold;
          font-size: 36rpx;
          color: #333333;
          line-height: 50rpx;

          > span:not(:first-child) {
            margin-left: 30rpx;
            position: relative;

            &::before {
              content: '';
              width: 2rpx;
              height: 32rpx;
              background: #9d9d9d;
              position: absolute;
              left: -15rpx;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }

        .change-btn {
          display: flex;
          align-items: center;
          font-size: 30rpx;
          color: #ff6b2c;
        }
      }

      .oil-info {
        padding: 34rpx 24rpx 24rpx;

        .select-row {
          display: flex;
          align-items: center;
          justify-content: space-between;

          > div {
            display: flex;
            align-items: center;

            .title {
              font-size: 26rpx;
              color: #666666;
              line-height: 37rpx;
              margin-right: 16rpx;
            }

            .input-wrap {
              padding: 0 10rpx;
              width: 176rpx;
              background: #f5f6f7;
              border-radius: 16rpx;
              height: 70rpx;
              line-height: 70rpx;
              font-size: 36rpx;
              color: #333333;
              text-align: center;
              box-sizing: border-box;

              .text {
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                > div {
                  line-height: 64rpx;
                }

                .amount-icon {
                  width: 30rpx;
                  height: 64rpx;
                  position: relative;

                  &::before {
                    content: '￥';
                    color: #333333;
                    font-size: 28rpx;
                    position: absolute;
                    left: 0;
                    bottom: -3rpx;
                    width: 100%;
                    height: 100%;
                  }
                }
              }
            }
          }

          .oil-num {
            flex: 1;
            margin-right: 18rpx;

            .input-wrap {
              flex: 1;
            }
          }

          .no-location {
            margin-top: 20rpx;
            width: 100%;
            display: flex;
            justify-content: center;
            font-size: 28rpx;
            color: #999999;
          }

          .no-service {
            margin-top: 20rpx;
            display: block;
            width: 100%;

            div:nth-child(1) {
              color: #333333;
              font-weight: bold;
              text-align: center;
            }

            div:nth-child(2) {
              margin-top: 12rpx;
              color: #999999;
              text-align: center;
            }
          }
        }

        .wallet-row {
          margin-top: 38rpx;
          display: flex;
          align-items: center;

          .title {
            font-size: 26rpx;
            color: #666666;
            line-height: 37rpx;
            margin-right: 16rpx;
          }

          .amount {
            font-weight: bold;
            font-size: 36rpx;
            color: #333333;
            line-height: 48rpx;

            .amount-icon {
              font-size: 28rpx;
            }
          }
        }
      }

      .tips-info {
        width: 100%;
        height: 65rpx;
        line-height: 65rpx;
        background: #fff7dc;
        border-radius: 0rpx 0rpx 16rpx 16rpx;
        font-size: 24rpx;
        color: #333333;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        .icon-right {
          margin-left: 10rpx;
        }
      }

      .order-info {
        padding: 37rpx 37rpx;
        text-align: center;

        .hint {
          height: 45rpx;
          font-size: 32rpx;
          color: #666666;
          line-height: 45rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            margin-left: 14rpx;

            &.eye-fill {
              width: 34rpx;
              height: 22rpx;
            }

            &.eye-off {
              width: 34rpx;
              height: 16rpx;
            }
          }
        }

        .code-row {
          margin-top: 24rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          > div {
            height: 80rpx;
            font-size: 80rpx;
            // line-height: 80rpx;
            letter-spacing: 24rpx;
          }

          .code {
            color: #fa1919;
          }

          .no-code {
            color: #999999;
          }

          image {
            // margin-top: 12rpx;
            margin-left: 14rpx;
            width: 34rpx;
            height: 34rpx;

            &:active {
              opacity: 0.8;
            }
          }
        }

        .detail {
          margin-top: 38rpx;
          font-size: 30rpx;
          color: #999999;

          > span:not(:first-child) {
            margin-left: 60rpx;
            position: relative;

            &::before {
              content: '';
              width: 2rpx;
              height: 30rpx;
              background: #999999;
              position: absolute;
              left: -29rpx;
              top: 50%;
              transform: translateY(-50%);
            }
          }
        }
      }

      .btn-box {
        padding: 0 16rpx;
        margin-top: 40rpx;

        .btn {
          &.disabled {
            opacity: 0.5;
            pointer-events: none;
          }
        }
      }
    }
  }
}

.oil-modal {
  width: 560rpx;
  min-height: 207rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .oil-pop {
    padding: 38rpx 0 0;
  }

  .no-fuedata {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    padding: 0 20rpx;
    margin-bottom: 34rpx;
    width: 100%;
    min-height: 68rpx;
    box-sizing: border-box;
  }

  .oil-num-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 40rpx;
    margin-bottom: 34rpx;
    width: 100%;
    min-height: 68rpx;
    box-sizing: border-box;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .num-list {
      margin-top: 28rpx;
      height: 100%;
      width: 100%;

      .num-item {
        height: 40rpx;
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        justify-content: space-between;
      }
    }
  }

  .money-num-content {
    display: flex;
    flex-direction: column;
    text-align: center;
    font-size: 28rpx;
    color: #333333;
    font-weight: bold;
    padding: 0 40rpx;
    margin-bottom: 34rpx;
    width: 100%;
    min-height: 68rpx;
    box-sizing: border-box;

    .content-title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }

    .warning-text {
      font-size: 26rpx;
      color: #333;
      padding: 40rpx 0 34rpx;
    }

    .tips-text {
      padding: 40rpx 0 34rpx;
      font-size: 26rpx;
      color: #333;
    }

    .price-input-area {
      border-radius: 20rpx;
      height: 88rpx;
      box-sizing: border-box;
      overflow: hidden;

      &.card-input-border {
        background: #f7f7fb;
        border-radius: 16px;
        border: 1rpx solid #ff0a0a;
      }

      .price-input {
        width: 100%;
        height: 100% !important;
        background: #f7f7fb;
        border-radius: 16rpx;
        text-align: center;
        box-sizing: border-box;
      }
    }
  }

  .slot-btn-box {
    display: flex;
    width: 100%;
    border-top: 2rpx solid #efeff4;

    .btn {
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      font-weight: 400;
      line-height: 90rpx;

      &:active {
        background-color: #f7f7f7;
      }
    }

    .cancel_btn {
      border-right: 2rpx solid #efeff4;
    }

    .confirm {
      font-weight: bold;
    }
  }
}

@keyframes up-down {
  from {
    transform: translateY(-10px);
  }
  to {
    transform: translateY(5px);
  }
}

@keyframes down-up {
  from {
    transform: translateY(5px);
  }
  to {
    transform: translateY(-10px);
  }
}
</style>
