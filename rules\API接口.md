---
description: UniApp + uni.$petro.http API 接口开发规范
globs: .vue,.js,services/http/*.js
alwaysApply: true
---

### UniApp API 接口规范

#### 总体描述

该文档主要对基于UniApp项目中使用uni.$petro.http进行API接口开发的相关规则进行了说明，包括API定义、Mock数据配置、响应格式定义、错误处理以及请求拦截等内容，有助于规范前端与后端的接口交互。

#### 应用范围
本规范适用于所有UniApp项目中与后端进行接口交互的开发场景，确保前端开发人员能够正确地定义、使用和处理API接口。

#### 使用要求
开发人员在进行API接口开发时，需要严格按照本规范中的API定义、Mock数据配置、响应格式定义以及错误处理等方面的要求进行开发。统一使用uni.$petro.http方法发起请求，确保跨平台兼容性和统一的错误处理机制。

## 规则1 API定义规范

### API文件组织结构
基于项目实际情况，API请求方法按业务模块组织在`src/services/http/`目录下。

#### 应用范围
适用于所有API接口的定义和管理。

#### 使用要求
严格按照模块化结构组织API代码，每个业务模块对应一个API文件。

#### 目录结构

```text
src/services/http/
├── index.js                 # HTTP服务入口
├── api.js                   # 通用API接口
├── account.js               # 账户相关API
├── car.js                   # 车辆相关API
├── station.js               # 加油站相关API
├── order.js                 # 订单相关API
└── notices.js               # 通知相关API
```

### API函数标准结构
所有API函数必须遵循统一的结构模式，包含内联Mock数据配置。

#### 标准API函数示例

```javascript
// src/services/http/api.js - 通用API示例
// 获取企业列表
export function getCompanyListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      onEntpList: [
        {
          enterpriseName: '企业名称1',
          entpNickName: '企业别名1',
          staffRole: 1,
        },
        {
          enterpriseName: '企业名称2',
          entpNickName: '企业别名2',
          staffRole: 1,
        },
      ],
      offEntpList: [
        {
          enterpriseName: '企业名称3',
          entpNickName: '企业别名3',
          staffRole: 1,
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('user.driver.queryCompanyByDriver.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 企业切换
export function companySwitchApi(data, config) {
  const mockData = {
    message: '成功',
    errorCode: '0',
    success: true,
    data: {},
  };

  return uni.$petro.http('user.companySwitch.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
```

```javascript
// src/services/http/car.js - 车辆相关API示例
// 获取司机绑定车辆列表
export function getCarListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 7,
      totalPage: 4,
      rows: [
        {
          driver: '张三',
          carNumber: '京A 88K09',
          companyName: '北京运输公司',
          refuelType: '柴油',
          refuelModel: '#-10',
          vehicleType: '货车',
          drivingLicenseUrl: 'https://example.com/license.jpg',
        },
        {
          driver: '李四',
          carNumber: '京A 88K08',
          companyName: '北京运输公司',
          refuelType: '汽油',
          refuelModel: '#92',
          vehicleType: '客车',
          drivingLicenseUrl: 'https://example.com/license2.jpg',
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('user.driver.bindCarList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
```

### API命名规范

#### 接口路径命名
- **格式**: `模块.动作.平台`
- **示例**:
  - `user.driver.queryCompanyByDriver.h5`
  - `oilstation.station.list.h5`
  - `account.driver.queryWalletList.h5`

#### 函数命名规范
- **格式**: `动作 + 业务对象 + Api`
- **示例**:
  - `getCompanyListApi` - 获取企业列表
  - `companySwitchApi` - 企业切换
  - `getCarListApi` - 获取车辆列表
  - `submitOrderApi` - 提交订单

## 规则2 Mock数据配置规范

### 内联Mock数据
所有API函数必须包含内联Mock数据，用于开发和测试环境。

#### 应用范围
适用于所有API接口的Mock数据配置。

#### 使用要求
Mock数据必须与真实接口返回结构保持一致，包含完整的业务数据。

#### Mock数据结构标准

```javascript
// 标准Mock数据结构
const mockData = {
  success: true,           // 请求成功标识
  data: {                  // 业务数据
    // 具体业务数据结构
  },
  message: '请求成功',      // 响应消息
  errorCode: null,         // 错误码，成功时为null
};

// 错误情况的Mock数据
const errorMockData = {
  success: false,
  data: null,
  message: '请求失败',
  errorCode: 'E001',
};
```

#### 复杂业务场景Mock示例

```javascript
// src/services/http/station.js - 加油站列表API
export function getStationListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 10,
      totalPage: 5,
      totalCount: 50,
      list: [
        {
          orgCode: '1-A4301-C001-S001',
          orgName: '湖南长沙岳麓区加油站',
          distance: '2.3',
          tagList: ['24小时营业', '便民服务'],
          stationStatus: '20', // 营业状态
          phone: '0731-88888888',
          address: '湖南省长沙市岳麓区麓山南路123号',
          longitude: '112.938814',
          latitude: '28.228209',
          businessStartTime: '00:00:01',
          businessEndTime: '23:59:59',
          hosCode: '1A2B',
          stationType: 'self', // 自营/加盟
          stationId: '001',
          bookingRefueling: true, // 是否支持预约加油
          activityList: [
            {
              activityId: 'act001',
              activityName: '新用户立减10元',
              activityType: 'discount',
              startTime: '2024-01-01 00:00:00',
              endTime: '2024-12-31 23:59:59'
            }
          ],
          fuelList: [
            {
              fuelNo: '300667',
              fuelName: '95号 车用汽油(Ⅴ)',
              fuelType: 12,
              price: '7.85',
              discount: '0.5',
              finalPrice: '7.35'
            },
            {
              fuelNo: '300668',
              fuelName: '92号 车用汽油(Ⅴ)',
              fuelType: 11,
              price: '7.45',
              discount: '0.3',
              finalPrice: '7.15'
            }
          ]
        }
      ]
    },
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('oilstation.station.list.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
```

## 规则3 请求配置规范

### uni.$petro.http 配置选项
uni.$petro.http方法支持多种配置选项，用于控制请求行为。

#### 应用范围
适用于所有使用uni.$petro.http的API调用。

#### 使用要求
根据业务需求合理配置请求选项，确保用户体验和错误处理。

#### 配置选项说明

```javascript
// 完整的请求配置示例
export function exampleApi(data, config) {
  const mockData = {
    success: true,
    data: {},
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('api.example.h5', data, {
    // 基础配置
    method: 'POST',              // 请求方法，默认POST
    timeout: 10000,              // 超时时间，毫秒

    // UI控制
    showLoading: true,           // 是否显示加载提示，默认true
    loadingText: '加载中...',    // 加载提示文字
    showToast: true,             // 是否显示错误提示，默认true

    // 错误处理
    ignoreErrs: false,           // 是否忽略错误，默认false
    silentMode: false,           // 静默模式，不显示任何提示

    // Mock配置
    mockResponse: mockData,      // Mock数据

    // 自定义配置
    ...config                    // 外部传入的配置
  });
}
```

### 常见使用场景配置

#### 静默请求（轮询、后台同步）
```javascript
// 轮询请求，不显示加载和错误提示
export function pollingStatusApi(data, config) {
  const mockData = {
    success: true,
    data: { status: 'processing' },
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('order.status.polling.h5', data, {
    showLoading: false,          // 不显示加载提示
    showToast: false,            // 不显示错误提示
    ignoreErrs: true,            // 忽略错误
    timeout: 5000,               // 较短的超时时间
    mockResponse: mockData,
    ...config
  });
}
```

#### 用户主动操作（提交表单）
```javascript
// 用户提交操作，需要明确的反馈
export function submitFormApi(data, config) {
  const mockData = {
    success: true,
    data: { orderId: 'ORDER123456' },
    message: '提交成功',
    errorCode: null,
  };

  return uni.$petro.http('form.submit.h5', data, {
    showLoading: true,           // 显示加载提示
    loadingText: '提交中...',    // 自定义加载文字
    showToast: true,             // 显示错误提示
    timeout: 30000,              // 较长的超时时间
    mockResponse: mockData,
    ...config
  });
}
```

#### 数据预加载（页面初始化）
```javascript
// 页面初始化数据加载
export function initPageDataApi(data, config) {
  const mockData = {
    success: true,
    data: {
      userInfo: {},
      settings: {},
      notifications: []
    },
    message: '请求成功',
    errorCode: null,
  };

  return uni.$petro.http('page.init.data.h5', data, {
    showLoading: false,          // 页面级loading，不使用接口loading
    showToast: false,            // 初始化失败通过页面状态显示
    ignoreErrs: true,            // 不中断页面渲染
    mockResponse: mockData,
    ...config
  });
}
```

## 规则4 错误处理规范

### 统一错误处理机制
uni.$petro.http内置了统一的错误处理机制，开发者需要了解并正确使用。

#### 应用范围
适用于所有API调用的错误处理。

#### 使用要求
合理处理不同类型的错误，提供良好的用户体验。

#### 错误处理示例

```javascript
// 在组件中处理API错误
export default {
  methods: {
    async handleApiCall() {
      try {
        // 1. 基础错误处理 - 依赖uni.$petro.http内置处理
        const { success, data, message } = await getCompanyListApi({
          userId: this.userId
        });

        if (success) {
          this.companyList = data.onEntpList || [];
        } else {
          // 业务逻辑错误
          console.warn('获取企业列表失败:', message);
        }

      } catch (error) {
        // 网络错误、超时等异常
        console.error('API调用异常:', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    },

    async handleSilentApiCall() {
      try {
        // 2. 静默处理 - 不显示错误提示
        const result = await pollingStatusApi({
          orderId: this.orderId
        }, {
          showToast: false,
          ignoreErrs: true
        });

        if (result.success) {
          this.orderStatus = result.data.status;
        }
        // 静默失败，不做特殊处理

      } catch (error) {
        // 即使是静默模式，也要记录错误日志
        console.error('轮询状态失败:', error);
      }
    },

    async handleCriticalApiCall() {
      try {
        // 3. 关键操作 - 需要特殊错误处理
        const result = await submitFormApi({
          formData: this.formData
        });

        if (result.success) {
          uni.showToast({
            title: '提交成功',
            icon: 'success'
          });

          // 跳转到结果页
          uni.navigateTo({
            url: `/pages/result/result?orderId=${result.data.orderId}`
          });

        } else {
          // 业务错误需要特殊处理
          this.handleBusinessError(result.errorCode, result.message);
        }

      } catch (error) {
        // 网络错误等异常情况
        uni.showModal({
          title: '提交失败',
          content: '网络异常，请检查网络连接后重试',
          showCancel: false
        });
      }
    },

    handleBusinessError(errorCode, message) {
      // 根据错误码进行特殊处理
      switch (errorCode) {
        case 'B_C15_000005':
        case 'B_C15_000008':
          // Token过期，跳转登录
          this.$store.dispatch('roles/logout');
          uni.reLaunch({
            url: '/pages/login/login'
          });
          break;

        case 'INSUFFICIENT_BALANCE':
          // 余额不足，跳转充值
          uni.showModal({
            title: '余额不足',
            content: '当前余额不足，是否前往充值？',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({
                  url: '/pages/wallet/wallet'
                });
              }
            }
          });
          break;

        default:
          // 其他业务错误
          uni.showToast({
            title: message || '操作失败',
            icon: 'none'
          });
      }
    }
  }
};
```

### 网络状态处理
```javascript
// 网络状态检测和处理
export default {
  methods: {
    async checkNetworkAndCall() {
      // 检查网络状态
      const networkInfo = await uni.getNetworkType();

      if (networkInfo.networkType === 'none') {
        uni.showModal({
          title: '网络异常',
          content: '请检查网络连接',
          showCancel: false
        });
        return;
      }

      // 网络正常，执行API调用
      try {
        const result = await getDataApi();
        this.handleSuccess(result);
      } catch (error) {
        this.handleNetworkError(error);
      }
    },

    handleNetworkError(error) {
      // 根据错误类型提供不同的处理方案
      if (error.code === 'TIMEOUT') {
        uni.showModal({
          title: '请求超时',
          content: '网络较慢，是否重试？',
          success: (res) => {
            if (res.confirm) {
              this.checkNetworkAndCall();
            }
          }
        });
      } else {
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    }
  }
};
```

## 规则5 响应数据处理规范

### 标准响应格式
所有API响应都遵循统一的数据格式。

#### 响应数据结构
```javascript
// 成功响应
{
  success: true,           // 请求成功标识
  data: {                  // 业务数据
    // 具体业务数据
  },
  message: '请求成功',      // 响应消息
  errorCode: null          // 错误码，成功时为null
}

// 失败响应
{
  success: false,          // 请求失败标识
  data: null,              // 失败时数据为null
  message: '错误描述',      // 错误消息
  errorCode: 'ERROR_CODE'  // 具体错误码
}
```

### 数据处理最佳实践
```javascript
// 组件中的数据处理示例
export default {
  data() {
    return {
      loading: false,
      dataList: [],
      error: null
    };
  },

  methods: {
    async loadData() {
      try {
        this.loading = true;
        this.error = null;

        const { success, data, message, errorCode } = await getDataListApi({
          pageNum: 1,
          pageSize: 20
        });

        if (success && data) {
          // 数据预处理
          this.dataList = this.processDataList(data.list || []);

          // 处理分页信息
          this.pagination = {
            current: data.pageNum || 1,
            total: data.totalCount || 0,
            pageSize: data.pageSize || 20
          };

        } else {
          // 业务错误处理
          this.error = message || '获取数据失败';
          this.dataList = [];
        }

      } catch (error) {
        // 异常错误处理
        console.error('加载数据异常:', error);
        this.error = '网络异常，请重试';
        this.dataList = [];

      } finally {
        this.loading = false;
      }
    },

    processDataList(list) {
      // 数据预处理：格式化、计算字段等
      return list.map(item => ({
        ...item,
        // 格式化时间
        formattedTime: this.formatTime(item.createTime),
        // 计算状态文本
        statusText: this.getStatusText(item.status),
        // 处理金额显示
        displayAmount: this.formatAmount(item.amount)
      }));
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
      };
      return statusMap[status] || '未知状态';
    },

    formatAmount(amount) {
      if (typeof amount !== 'number') return '0.00';
      return amount.toFixed(2);
    }
  }
};
```
```
```

#### 规则3 状态码定义

- 2xx: 成功
- 4xx: 客户端错误（比如401未授权、403无权限等）
- 5xx: 服务器错误

#### 规则4 请求拦截

- 统一添加 token
- 处理请求参数
- 添加时间戳防止缓存

#### 规则5 响应拦截

- 统一处理错误响应
- 处理 401、403 未授权等特殊逻辑
- 格式化响应数据
