import { getCarListApi } from '@/services/http';

export default {
  state: {
    carList: [],
    carInfo: {
      fillOilType: NaN,
      fillType: NaN,
      id: NaN,
      licensePlate: '',
      vehicleType: NaN,
    },
  },
  mutations: {
    setCarList(state, value) {
      Object.assign(state.carList, value);
    },
    setCarInfo(state, value) {
      Object.assign(state.carInfo, value);
    },
  },
  actions: {
    // 获取车辆列表
    async getCarList({ commit, dispatch, state, rootState }, setDefault = true) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      try {
        const params = {
          businessNo: rootState?.company?.companyInfo?.businessNo, // 业务编号
          staffNo: rootState?.company?.companyInfo?.staffNo, // 司机员工编号
        };
        const { success, data } = await getCarListApi(params);
        if (success && data?.length) {
          commit('setCarList', data);
          if (setDefault) {
            commit('setCarInfo', data[0]);
          }
          return data;
        } else {
          commit('setCarList', []);
          commit('setCarInfo', {
            fillOilType: NaN,
            fillType: NaN,
            id: NaN,
            licensePlate: '',
            vehicleType: NaN,
          });
        }
        
      } catch (error) {
        console.log(error);
      }
      return [];
    },
  },
  getters: {},
};
