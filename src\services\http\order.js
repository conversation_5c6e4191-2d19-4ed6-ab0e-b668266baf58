// [预约加油预授权下单/order/preAuth/generateOrder]
export function generateOrderApi(data, config) {
  const mockData = {
    success: true,
    data: {
      stationCode: '1-A4301-C001-R04-S011',
      stationName: '湖南长沙警院加油站',
      preAuthOrderNo: '2302151717350000246767237',
      fuelCode: '8396',
      productNo: '300667',
      productName: '95号 车用汽油(Ⅴ)',
      preAuthNum: 12.987,
      preAuthAmount: 100,
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.generateOrder.h5', data, {
    ...config,
    mockResponse: mockData,
    isCustomErr: true,
  });
}

// [取消预约加油/order/preAuth/cancel]
export function preAuthCancelApi(data, config) {
  const mockData = {
    success: true,
    data: null,
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.cancel.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [获取加油码/order/preAuth/getRefuelCode]
export function getRefuelCodeApi(data, config) {
  const mockData = {
    success: true,
    data: {
      refuelCode: '1234',
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.getRefuelCode.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [查询预授权订单 /order/preAuth/getPreAuthOrder]
export function getPreAuthOrderApi(data, config) {
  const mockData = {
    success: true,
    data: {
      preAuthOrderNo: '2303091054340000246709036',
      orderNo: null,
      orderChannel: '26',
      stationCode: '1-A4301-C001-R04-S011',
      stationName: '湖南长沙警院加油站',
      gunNo: null,
      memberNo: '**************',
      preAuthAccount: null,
      fuelCardAccountNo: null,
      licensePlate: '川A88888',
      productNo: '300667',
      productName: '95号 车用汽油(Ⅴ)',
      productUnit: null,
      preAuthNum: 1.1364,
      preAuthAmount: 100,
      receivableAmount: null,
      preOrderStatus: 1,
      usedInterestsSccount: 1,
      fuelCode: '3273',
      fuelCodeStatus: 0,
      createTime: '2024-07-17 01:54:34',
      completeTime: null,
      actualPayAmount: 1,
      orderType: 2,
      orderSubType: 14,
      businessDay: '2023-03-11',
      businessNo: '999',
      enterpriseNo: '**********',
      memberType: 2,
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.getPreAuthOrder.h5', data, {
    ...config,
    mockResponse: mockData,
    showLoading: false,
  });
}

// [查询未完成预授权订单 /order/preAuth/getUnPayPreAuthOrder]
export function getUnPayPreAuthOrderApi(data, config) {
  const mockData = {
    success: true,
    data: {
      preAuthOrderNo: '2303091054340000246709036',
      orderNo: null,
      orderChannel: '26',
      stationCode: '1-A4301-C001-R04-S011',
      stationName: '湖南长沙警院加油站',
      gunNo: null,
      memberNo: '**************',
      preAuthAccount: null,
      fuelCardAccountNo: null,
      licensePlate: '川A88888',
      productNo: '300667',
      productName: '95号 车用汽油(Ⅴ)',
      productUnit: null,
      preAuthNum: 1.1364,
      preAuthAmount: 100,
      receivableAmount: null,
      preOrderStatus: 1,
      usedInterestsSccount: 1,
      fuelCode: '3273',
      fuelCodeStatus: 0,
      createTime: '2024-07-17 01:54:34',
      completeTime: null,
      actualPayAmount: 1,
      orderType: 2,
      orderSubType: 14,
      businessDay: '2023-03-11',
      businessNo: '999',
      enterpriseNo: '**********',
      memberType: 2,
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.getUnPayPreAuthOrder.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取订单列表(v410)
export function getOrderListApi_v410(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 20,
      pageSum: 1,
      totalRows: 1,
      rows: [
        {
          orderNo: '2401251539040007038473013',
          stationCode: '1-A1201-C011-S026',
          stationName: '天津销售武清分公司高王路加油站',
          hosCode: 'xxxx',
          orderSubType: 11,
          orderStatus: 4,
          createTime: '2024-07-10 15:30:34',
          tradeEndTime: '2024-07-10 15:34:34',
          actualPayTotalAmount: 22.0,
          licensePate: '川A88888',
          itemList: [
            {
              productNo: '2401251539040007038473013',
              productName: '利群 (软红长嘴)香烟 20支',
              productType: '2',
              unitPrice: '22.0',
              productQty: '1.0',
              productUnit: '包',
            },
          ],
        },
        {
          orderNo: '2312011553170005175386820',
          stationCode: '1-A4301-C001-S006',
          stationName: '湖南长沙金霞加油站',
          hosCode: 'xxxx',
          orderSubType: 11,
          orderStatus: 5,
          createTime: '2024-08-10 12:00:01',
          tradeEndTime: '2024-08-10 12:01:01',
          actualPayTotalAmount: 28.98,
          licensePate: '川A777777',
          itemList: [
            {
              productNo: '2312011553170005175386820',
              productName: '95号 车用汽油(ⅥA)',
              productType: '1',
              unitPrice: '6.22/L',
              productQty: '4.82',
              productUnit: 'L',
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('order.preAuth.getMemberTradeList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取订单列表(v420)
export function getOrderListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      totalRows: 11,
      pageSize: 10,
      pageSum: 2,
      rows: [
        {
          orderNo: '2408221445030007562562952',
          stationCode: '1-A4301-C002-S045',
          stationName: '(简)湖南益阳卫龙加油站',
          businessDay: '2024-08-22',
          orderChannel: 42,
          orderType: 2,
          orderSubType: 11,
          orderStatus: 4,
          licensePlate: '川A77777',
          orderTotalAmount: 50.0,
          discountTotalAmount: 0.0,
          payDiscountTotalAmount: 0.0,
          receivedTotalAmount: 50.0,
          actualPayTotalAmount: 50.0,
          createTime: '2024-08-23 14:45:03',
          updateTime: '2024-08-23 14:45:04',
          invoiceFlag: 0,
          // productName: 'M100车用甲醇燃料',
          // productNo: '301032',
          // productType: '10010101',
          // gunNo: 2,
          // productQty: 4.55,
          // productUnit: 'L',
          // unitPrice: 11.0,
          createByName: '张康',
          orderItemList: [
            {
              orderNo: '111111',
              productName: 'M100车用甲醇燃料',
              productNo: '301032',
              productType: '1',
              gunNo: 2,
              productQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              discountAmount: 0.0,
              receivableAmount: 0.0,
              payDiscountAmount: 0.0,
              imgUrl: '',
              productCategory: '',
              orderItemNo: '',
            },
            {
              orderNo: '2222222',
              productName: 'M101车用甲醇燃料',
              productNo: '301032',
              productType: '2',
              gunNo: 2,
              productQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              discountAmount: 0.0,
              receivableAmount: 0.0,
              payDiscountAmount: 0.0,
              imgUrl: 'http://picsum.photos/375/200?r=1',
              productCategory: '',
              orderItemNo: '',
            },
          ],
        },
        {
          orderNo: '2408221445030007562562953',
          stationCode: '1-A4301-C002-S045',
          stationName: '(简)湖南益阳卫龙加油站',
          businessDay: '2024-08-22',
          orderChannel: 42,
          orderType: 2,
          orderSubType: 14,
          orderStatus: 5,
          licensePlate: '川A77777',
          orderTotalAmount: 50.0,
          discountTotalAmount: 0.0,
          payDiscountTotalAmount: 0.0,
          receivedTotalAmount: 50.0,
          actualPayTotalAmount: 50.0,
          createTime: '2024-08-23 14:45:03',
          updateTime: '2024-08-23 14:45:04',
          invoiceFlag: 0,
          // productName: 'M100车用甲醇燃料',
          // productNo: '301032',
          // productType: '10010101',
          // gunNo: 2,
          // productQty: 4.55,
          // productUnit: 'L',
          // unitPrice: 11.0,
          createByName: '张康',
          orderItemList: [
            {
              orderNo: '111111',
              productName: 'M100车用甲醇燃料2',
              productNo: '301032',
              productType: '1',
              gunNo: 2,
              productQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              discountAmount: 0.0,
              receivableAmount: 0.0,
              payDiscountAmount: 0.0,
              imgUrl: '',
              productCategory: '',
              orderItemNo: '',
            },
            {
              orderNo: '2222222',
              productName: 'M101车用甲醇燃料2',
              productNo: '301032',
              productType: '2',
              gunNo: 2,
              productQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              discountAmount: 0.0,
              receivableAmount: 0.0,
              payDiscountAmount: 0.0,
              imgUrl: 'http://picsum.photos/375/200?r=1',
              productCategory: '',
              orderItemNo: '',
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500);
  // });

  return uni.$petro.http('chart.trade.consume.list.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取订单详情
export function getOrderDetailApi(data, config) {
  const mockData = {
    success: true,
    data: {
      orderNo: '2410231605450006775499493',
      payChannel: ['中油智行APP'],
      orderTotalAmount: 10.0,
      actualPayTotalAmount: 10.0,
      discountTotalAmount: 0,
      payDiscountAmount: null,
      orderItemList: [
        {
          orderNo: '2410231605450006775499493',
          productName: '92号 车用汽油(京Ⅵ)',
          productNo: '300775',
          productType: '1',
          gunNo: 6,
          productQty: 20.0,
          productUnit: 'L',
          unitPrice: '0.5/L',
          discountAmount: 0.0,
          receivableAmount: 10.0,
          payDiscountAmount: 0.0,
          imgUrl: null,
          productCategory: '3100',
          orderItemNo: null,
        },
      ],
      stationName: '(简)西藏销售拉萨分公司中卫加油站测试中卫',
      stationCode: '1-A5401-C001-S001',
      payItemList: [
        { payMethod: 5, payAmount: 10.0, payConfirmationTime: '2024-10-24 16:05:45', payMethodName: '昆仑e享卡' },
        { payMethod: 5, payAmount: 10.0, payConfirmationTime: '2024-10-24 16:05:45', payMethodName: '昆仑e享卡2' },
      ],
      activityDiscountList: null,
      orderType: 2,
      orderSubType: 11,
      discountList: [
        { payAmount: '12', payMethodName: '优惠1' },
        { payAmount: '15', payMethodName: '优惠2' },
      ],
      payDetailList: [{ payAmount: 10.0, payMethod: null, payChannel: null, payMethodName: '昆仑e享卡' }],
      payConfirmationTime: '2024-10-24 16:05:45',
      businessDay: '2024-10-23',
    },
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500); // 延迟2秒
  // });
  return uni.$petro.http('order.consume.detail.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取室内支付订单详情
export function getIndoorOrderDetailApi(data, config) {
  const mockData = {
    success: true,
    data: {
      orderNo: '2312011548300005175386819',
      payConfirmationTime: '2023-12-04 15:48:41',
      payChannel: ['中油智行APP'],
      payDiscountTotal: '0.00',
      orderItemList: [
        {
          orderNo: '2312011548300005175386819',
          productName: '95号 车用汽油(ⅥA)',
          productNo: '300867',
          productType: '1',
          gunNo: '1',
          productQty: '4.33',
          productUnit: 'L',
          unitPrice: '6.22',
          discountAmount: 0.0,
          receivableAmount: 26.93,
          payDiscountAmount: 0.0,
          imgUrl: null,
          productCategory: '3100',
          productDescription: null,
          orderItemNo: 116039,
        },
        {
          orderNo: '2312011548300005175386819',
          productName: '98号 车用汽油(ⅥA)',
          productNo: '300867',
          productType: '2',
          gunNo: '1',
          productQty: '4.44',
          productUnit: 'L',
          unitPrice: '6.33',
          discountAmount: 0.0,
          receivableAmount: 36.93,
          payDiscountAmount: 0.0,
          imgUrl: 'http://picsum.photos/375/200?r=1',
          productCategory: '3100',
          productDescription: null,
          orderItemNo: 116040,
        },
      ],
      payItemList: [
        {
          payAmount: '26.93',
          payMethod: '昆仑e享卡',
          payChannel: null,
          payMethodName: '昆仑e享卡',
        },
      ],
      invoiceFlag: '1',
      stationCode: '1-A4301-C001-S006',
      stationName: '湖南长沙金霞加油站',
      commentFlag: 0,
      createTime: '2023-12-04 15:48:30',
      businessDay: '2023-12-01',
      orderType: 2,
      orderSubType: 14,
      actualPayTotalAmount: 26.93,
      orderTotalAmount: 26.93,
      pickUpCode: null,
      consignee: null,
      consigneePhone: null,
      licensePlate: null,
      expectPickUpTime: null,
      discountTotalAmount: 0,
      discountList: [
        {
          payAmount: 1.2,
          payMethod: '',
          payChannel: 0,
          payMethodName: '支付折扣',
        },
      ],
      payDetailList: [
        {
          payAmount: '26.93',
          payMethod: null,
          payChannel: null,
          payMethodName: '昆仑e享卡',
        },
      ],
      consigneeAddress: null,
      performanceNo: null,
      hosCode: '1A07',
      receivedTime: null,
      pickUpTime: null,
      mileage: '1000',
      equipmentCardNo: '123123r354234',
    },
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500);
  // });
  return uni.$petro.http('order.indoor.orderDetails.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取已退款订单列表(v440)
export function getRuturnOrderListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      totalRows: 1,
      pageSize: 10,
      pageSum: 1,
      rows: [
        {
          returnOrderNo: '2408221445030007562562952',
          // stationCode: '1-A4301-C002-S045',
          stationName: '(简)湖南益阳卫龙加油站',
          actualTotalRefundAmount: 50.0,
          refundStatus: 0,
          createTime: '2024-08-23 14:45:03',
          orderItems: [
            {
              returnOrderNo: '111111',
              productName: 'M100车用甲醇燃料',
              productNo: '301032',
              productQty: 4.5,
              returnQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              actualRefundAmount: 50.0,
              imgUrl: '',
              createTime: '',
            },
            {
              returnOrderNo: '22222',
              productName: 'M100车用甲醇燃料',
              productNo: '301032',
              productQty: 4.5,
              returnQty: 4.5,
              productUnit: 'L',
              unitPrice: 11.0,
              actualRefundAmount: 50.0,
              imgUrl: '',
              createTime: '',
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500);
  // });

  return uni.$petro.http('order.consume.returnOrderList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取已退款订单详情(v440)
export function getReturnOrderDetailApi(data, config) {
  const mockData = {
    success: true,
    data: {
      returnOrderNo: '2312011548300005175386819',
      returnOrderTime: '2023-12-04 15:48:41',
      returnOrderTotalAmount: '999.00',
      orderItems: [
        {
          returnOrderNo: '111111',
          productName: 'M100车用甲醇燃料',
          productNo: '301032',
          productQty: 4.5,
          returnQty: 4.5,
          productUnit: 'L',
          unitPrice: 11.0,
          actualRefundAmount: 50.0,
          imgUrl: '',
          createTime: '',
        },
        {
          returnOrderNo: '111111',
          productName: 'M100车用甲醇燃料',
          productNo: '301032',
          productQty: 4.5,
          returnQty: 4.5,
          productUnit: 'L',
          unitPrice: 11.0,
          actualRefundAmount: 50.0,
          imgUrl: '',
          createTime: '',
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500);
  // });
  return uni.$petro.http('order.consume.returnOrderDetail.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 室内支付待支付订单绑定BD卡信息接口(v440)
export function saveIndoorUnPayOrder(data, config) {
  const mockData = {
    success: true,
    data: {},
    message: '请求成功',
    errorCode: null,
  };
  // return new Promise(resolve => {
  //   setTimeout(() => {
  //     resolve(mockData);
  //   }, 500);
  // });
  return uni.$petro.http('fuelfilling.indoor.saveIndoorUnPayOrder.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
