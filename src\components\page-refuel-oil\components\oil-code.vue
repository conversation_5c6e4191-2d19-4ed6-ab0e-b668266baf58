<template>
  <div class="page-gift-code">
    <view class="container">
      <div class="title">会员码</div>
      <!-- 未开启定位权限 -->
      <div class="code-placeholder location" v-if="!isLocalPre">
        <image src="@/static/images/icon-qrcode-placeholder.png"></image>
        <p>付款码在距离中石油加油站 500米 内使用，现在还不知道您在哪里，请在设置中开启位置权限</p>
        <button size="default" @click="toSysSetting()">去设置</button>
      </div>
      <!-- 距离超过500米 -->
      <div class="code-placeholder distance" v-else-if="isLocalPre && distanceInM > distanceLimit">
        <image src="@/static/images/icon-qrcode-placeholder.png"></image>
        <p>距离加油站过远</p>
        <p>请在有效范围内使用会员码</p>
        <button size="default" @click="refreshDistance()">刷新</button>
      </div>
      <!-- 正常展示码 -->
      <div class="code-box" v-else>
        <div class="value">{{ qrCode }}</div>
        <view class="code" @click="refreshCode()">
          <view class="code-value">
            <canvas canvas-id="qrcode" id="qrcode" style="width: 490rpx; height: 490rpx" />
          </view>
          <div class="code-loading" v-if="loadingCode">
            <u-loading-icon></u-loading-icon>
          </div>
        </view>
        <div class="refresh-row">
          <span>(点击二维码更新)</span>
          <div @click="refreshCode()">
            刷新
            <image src="@/static/images/oil-icon-code-refresh.png"></image>
          </div>
        </div>
      </div>
      <div class="footer">
        <div class="line-row">
          <div class="icon-left"></div>
          <div class="icon-right"></div>
        </div>
        <div class="account-row">
          <div class="label">昆仑e享卡</div>
          <div class="account-info" @click="onAccountPopupShow" v-if="[1].includes(tokenInfo.fleetType)">
            <span class="text-overflow" v-if="walletInfo.accountType">
              {{ walletInfo.accountType == 2 ? walletInfo.userName : walletInfo.licencePlate }}-{{ walletInfo.unitAlias }}
            </span>
            <image src="@/static/images/icon-arrow-right.png"></image>
          </div>
          <div class="account-info" @click="onAccountPopupShow" v-if="[2].includes(tokenInfo.fleetType)">
            <span class="text-overflow">
              {{ walletInfo.licencePlate }} &nbsp;余额：￥{{ accountAmount(walletInfo.walletAccountList, 'availableAmount') }}
            </span>
          </div>
          <div class="account-box" @click="onAccountPopupShow" v-if="[3].includes(tokenInfo.fleetType)">
            <div class="account-info">
              <span class="text-overflow" v-if="walletInfo.accountType">
                {{ walletInfo.licencePlate }}
              </span>
              <image src="@/static/images/icon-arrow-right.png"></image>
            </div>
            <div class="amount">余额：￥{{ accountAmount(walletInfo.walletAccountList, 'availableAmount') }}</div>
          </div>
        </div>
        <div class="tips">
          <div>温馨提示:</div>
          <p>请确认{{ [1].includes(tokenInfo.fleetType) ? '昆仑e享卡' : '' }}信息再扫码付款</p>
        </div>
      </div>
    </view>
    <root-portal :enable="true">
      <account-popup ref="accountPopup" @onSelect="refreshCode()"></account-popup>
    </root-portal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { QRCode } from '@uni-ui/code-plugs';
import AccountPopup from './account-popup.vue';

export default {
  name: 'oil-code',
  components: {
    AccountPopup,
  },
  props: {
    pageShow: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      osName: uni.$petro.store.systemInfo.osName,
      qrCode: '', // 二维码字符串
      qrCodePollingTaskId: null, // 二维码轮询刷新id
      orderPollingTaskId: null, // 支付订单轮询任务id
      loadingCode: false, // 加载中
      qrCodeInfo: {
        id: 'qrcode',
        // ctx: this,
        code: '', //必传
        level: 4, //纠错等级 0~4 默认4 非必传
        type: 'none', // 码点 目前只支持 none 其它暂不支持 非必传
        // src: '/static/35.png', //画布背景 非必传
        padding: 10, //二维码margin Number 单位rpx 默认0 非必传
        // border: {
        //   //非必传
        //   color: ['#F27121', '#8A2387', '#1b82d2'], //边框颜色支持渐变色 最多10种颜色 如果默认黑色此属性不需要传
        //   opacity: 0.6, //边框透明度 默认为1不透明 0~1
        //   lineWidth: 6, //边框宽度
        //   degree: 15, //边框圆角度数 默认5
        // },
        // text: {
        //   //二维码绘制文字 非必传
        //   opacity: 1, //文字透明度 默认不透明1  0~1 非必传
        //   font: 'bold 20px system-ui', //文字是否加粗 默认normal 20px system-ui 非必传
        //   color: ['#000000'], // 文字颜色 多个颜色支持渐变色 默认黑色 非必传
        //   content: '这是一个测试', //文字内容
        // },
        // img: {
        //   // 二维码log配置 非必传
        //   // src: '/static/logo.png', // 图片地址
        //   size: 40, // 图片大小
        //   degree: 15, // 圆角大小 如果type为round生效
        //   type: 'round', //图片展示类型 默认none 可选值  round圆角  circle圆 如果为round 可以传入degree设置圆角大小 默认 5
        //   color: '#ffffff', //图片周围的白色边框
        //   width: 8, //图片周围白色边框的宽度 默认5
        // },
        // color: ['#11998e', '#38ef7d', '#F27121', '#8A2387', '#1b82d2'], //二维码颜色支持渐变 最多10种颜色 默认黑色 非必传
        // bgColor: '#FFFFFF', //二维码背景色 默认白色 非必传
        size: 488, // 二维码大小 Number 单位rpx 必传
      },
      shouldRefreshCode: false,
      // 账户信息
      accountList: [],
      // 是否显示
      appShow: true,
      // 加油距离限制 TODO
      // distanceLimit: 500,
      distanceInM: 0,
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      walletInfo: state => state?.accountDriver?.driverWalletInfo,
      tabBarValue: state => state?.tabBar?.tabBarValue,
      oilTabsValue: state => state?.tabBar?.oilTabsValue,
      accountInfo: state => state.company?.accountInfo,
      userInfo: state => state?.roles?.userInfo,
      stationList: state => state.station?.stationList,
      stationInfo: state => state.station?.stationInfo,
      pagePre: state => state.tabBar?.tabBarPermissions.oil,
      indexPageShow: state => state?.tabBar?.indexPageShow,
      tokenInfo: state => state?.roles?.tokenInfo,
      companyInfo: state => state.company?.companyInfo,
    }),
    isLocalPre() {
      if (this.pagePre && !this.pagePre.localPre) {
        return false;
      }
      return true;
    },
    distanceLimit() {
      return this.stationInfo?.payDistance * 1000 || 500;
    },
  },
  watch: {
    pageShow: {
      handler(newValue, oldValue) {
        console.log('oil-code---show', newValue, oldValue);
        if (newValue > 1) {
          this.getWalletInfo();
          if (this.pagePre?.localPre) {
            this.clearPollingTasks();
            this.refreshDistance();
          }
        }
      },
      deep: true, // 深度监听对象内部属性的变化
    },
    tabBarValue: {
      handler(newValue, oldValue) {
        if (newValue === 'refuel-oil' && this.oilTabsValue === 'code') {
          this.getWalletInfo();
          if (this.pagePre?.localPre) {
            this.refreshDistance();
          }
        } else {
          this.clearPollingTasks();
        }
      },
    },
    pagePre: {
      handler(newValue, oldValue) {
        if (!oldValue?.localPre && newValue?.localPre) {
          this.refreshDistance();
        }
      },
      deep: true, // 深度监听对象内部属性的变化
    },
    indexPageShow: {
      handler(newValue, oldValue) {
        console.log('indexPageShow----', newValue, oldValue);
        if (newValue && this.tabBarValue === 'refuel-oil' && this.oilTabsValue === 'code') {
          this.getWalletInfo();
          if (this.pagePre?.localPre) {
            this.clearPollingTasks();
            this.refreshDistance();
          }
        }
      },
    },
  },
  async mounted() {
    uni.onAppShow(res => {
      this.appShow = true;
      console.log('onAppShow');
    });
    uni.onAppHide(res => {
      this.appShow = false;
      console.log('onAppHide');
    });
    this.getWalletInfo();
    if (this.pagePre?.localPre) {
      this.refreshDistance();
    }
  },
  methods: {
    /**
     * 根据账户类型获取账户金额
     *
     * @param item 账户数据数组
     * @param value 需要获取的金额类型，如'availableAmount'
     * @param key 账户类型，默认为5
     * @returns 返回指定账户类型的金额，如果未找到则返回0
     */
    accountAmount(item = [], value, key = 5) {
      const data = (item || []).find(it => key == it.accountType);
      if (data) {
        return data[value] || '0';
      } else {
        return 0;
      }
    },
    // 刷新距离判断是否获取二维码
    async refreshDistance() {
      this.distanceInM = await this.getDistance();
      if (this.distanceInM <= this.distanceLimit) {
        this.checkPollingTasks();
      }
    },
    // 获取最近油站距离距离
    async getDistance() {
      let defaultGPS = null;
      if (this.pagePre?.isDefaultGPS) {
        defaultGPS = uni.$petro.config?.defaultGPS;
      }
      const data = await this.$store.dispatch('getStationList', defaultGPS);
      if (!data.length) return;
      const stationInfo = await this.$store.dispatch('getDistanceByStationCode', {
        stationCode: data[0].orgCode,
        location: defaultGPS || {},
        // TODO
        // location: uni.$petro.config?.defaultGPS || {},
      });
      if (!stationInfo?.stationCode) return;
      return stationInfo?.distanceInM;
    },
    async getWalletInfo() {
      try {
        // 获取司机账户钱包列表
        const params = {
          enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
          refresh: true,
          isBd: [2].includes(this.tokenInfo.fleetType),
        };
        await this.$store.dispatch('getWalletList', { ...params });
      } catch (err) {
        console.error(err);
      }
    },
    checkPollingTasks() {
      if (
        (this.appShow === true && this.tabBarValue === 'refuel-oil' && this.oilTabsValue === 'code') ||
        [2, 3].includes(this.tokenInfo.fleetType)
      ) {
        this.createPollingTasks();
      }
    },
    // 创建轮询任务
    async createPollingTasks() {
      if (!this.role?.businessNo) {
        return;
      }
      if (!this.walletInfo?.mainAccountNo) {
        await this.getWalletInfo();
      }
      this.createOrderInfoPollingTask();
      this.createQrCodePollingTask();
    },
    // 清除轮询任务
    clearPollingTasks() {
      clearInterval(this.qrCodePollingTaskId);
      clearInterval(this.orderPollingTaskId);
      this.qrCodePollingTaskId = null;
      this.orderPollingTaskId = null;
      this.loadingCode = false;
    },
    async refreshCode() {
      if (!this.walletInfo?.mainAccountNo) {
        await this.getWalletInfo();
      }
      await this.getQrCode();
    },
    // 获取二维码
    async getQrCode() {
      console.log('二维码--getQrCode--', JSON.stringify(this.walletInfo));
      try {
        if (this.loadingCode) return;
        this.loadingCode = true;
        let { success, data } = await uni.$petro.AccountPlugin.caller({
          bizType: 'generatePayCode', // 生成支付码
          data: {
            businessIdx: this.walletInfo?.serialNo,
            // charType: 4, // 角色类型 4:司机
            mainAccountType: Number(this.walletInfo?.accountType),
          },
        });
        if (!success) return;
        this.qrCode = data?.qrcode;
        this.qrCodeInfo.code = this.qrCode;
        QRCode(this.qrCodeInfo, res => {
          console.log('QRCode', res);
          this.loadingCode = false;
          this.shouldRefreshCode = false;
        });
      } catch (err) {
        console.error(err);
        this.loadingCode = false;
      }
    },
    // 创建每隔60秒刷新二维码
    async createQrCodePollingTask() {
      console.log('createQrCodePollingTask', this.qrCodePollingTaskId);
      if (this.qrCodePollingTaskId) return;
      this.qrCodePollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
        await this.getQrCode();
        return false;
        // TODO
      }, 60 * 1000);
    },
    // 轮询获取待支付订单
    async createOrderInfoPollingTask() {
      console.log('createOrderInfoPollingTask', this.qrCodePollingTaskId);
      if (this.qrCodePollingTaskId) return;
      if (this.orderPollingTaskId) return;
      this.orderPollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
        if (!this.qrCode) return false;
        try {
          console.log('this.walletInfo----', this.walletInfo);
          const { success, data } = await uni.$petro.PayPlugin.caller(
            {
              bizType: 'getBusinessNoList',
              data: {
                parameters: JSON.stringify({
                  mainAccountNo: this.walletInfo?.mainAccountNo, // 主账户编号
                  unitMainAccountNo: this.walletInfo?.enterpriseAccountNo, // 单位主账户编号
                  enterpriseStaffNo: this.userInfo?.staffNo, // 员工编号
                  petrolChinaNo: this.userInfo?.petroChinaNo, // 员工档案编号
                  businessNo: this.walletInfo?.businessNo, // 业务编号
                  extendField: '', // 风控
                }),
              },
            },
            { ignoreErrs: true },
          );
          console.log(`轮询码-${this.qrCode}--`, data);
          if (!!success) {
            uni.$petro.route({ url: '/pages/code-pay/code-pay', params: data, type: 'navigateTo' }, true);
            return true;
          }
        } catch (err) {
          console.error(err);
        }
      }, 5000);
    },
    // 打开选择账户弹窗
    onAccountPopupShow() {
      if ([2].includes(this.tokenInfo.fleetType)) return;
      this.$refs.accountPopup.show();
    },
    // 选择账户
    onAccountSelect(item) {
      this.refreshCode();
    },
    // 权限-跳转系统设置页面
    async toSysSetting(url = 'setting') {
      const res = await uni.$petro.Bridge.zyzx.launchUrl({
        url: url,
      });
    },
    testToPay() {
      uni.$petro.route({ url: '/pages/code-pay/code-pay', params: {}, type: 'navigateTo' }, true);
    },
  },
  onUnload() {
    console.log('onUnload');
    this.clearPollingTasks();
  },
  destroyed() {
    console.log('destroyed');
    this.clearPollingTasks();
  },
};
</script>

<style scoped lang="scss">
.code-placeholder {
  margin-top: 152rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  image {
    width: 114rpx;
    height: 114rpx;
    margin-bottom: 28rpx;
  }

  p {
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
    line-height: 40rpx;
    text-align: center;
  }

  button {
    margin-top: 48rpx;
    width: 218rpx;
    height: 80rpx;
    background: #fa1919;
    border-radius: 41rpx;
    color: #ffffff;
    font-weight: bold;
    font-size: 30rpx;
    line-height: 80rpx;
    text-align: center;
  }

  &.distance {
    padding-bottom: 196rpx;
  }

  &.location {
    padding-bottom: 154rpx;
  }
}

.page-gift-code {
}

.container {
  margin: 0 32rpx;
  padding: 56rpx 48rpx;
  background: #f4f6f9;
  border-radius: 16rpx;

  .title {
    color: #000;
    text-align: center;
    height: 44rpx;
    line-height: 44rpx;
    font-weight: bold;
    font-size: 36rpx;
    color: #333333;
  }
}

// 二维码
.code-box {
  padding-bottom: 30rpx;

  .value {
    margin-top: 44rpx;
    color: #000;
    text-align: center;
    height: 44rpx;
    line-height: 44rpx;
    font-weight: bold;
    font-size: 36rpx;
    color: #333333;
  }

  .refresh-row {
    margin-top: 32rpx;
    display: flex;
    justify-content: center;

    > span {
      font-size: 24rpx;
      color: #333333;
      line-height: 44rpx;
    }

    div {
      margin-left: 10rpx;
      height: 44rpx;
      font-size: 24rpx;
      color: #ff6b2c;
      line-height: 44rpx;
      display: flex;
      align-items: center;

      image {
        margin-left: 4rpx;
        width: 16rpx;
        height: 16rpx;
      }
    }
  }

  .code {
    margin: 20rpx auto 0;
    width: 488rpx;
    // height: 488rpx;
    box-sizing: border-box;
    position: relative;

    &-value {
      width: 488rpx;
      height: 488rpx;
      margin: 0 auto;
      background: #f5f5f5;
      border-radius: 32rpx;
      overflow: hidden;
    }

    &-loading {
      position: absolute;
      margin: auto;
      z-index: 1;
      height: 50px;
      inset: 0;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
  }
}

.footer {
  .line-row {
    height: 42rpx;
    position: relative;

    div {
      width: 42rpx;
      height: 42rpx;
      background: #5671b4;
      border-radius: 50%;
      position: absolute;

      &.icon-left {
        top: 0;
        left: calc(-48rpx - 21rpx);
      }

      &.icon-right {
        top: 0;
        right: calc(-48rpx - 21rpx);
      }
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 100%;
      height: 0rpx;
      // border: 1rpx solid #e6e6e6;
      border-top: 1rpx dashed #e6e6e6;
    }
  }

  .account-row {
    margin-top: 52rpx;
    display: flex;
    justify-content: space-between;

    .label {
      flex-shrink: 0;
      height: 44rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #666666;
      line-height: 44rpx;
    }

    .amount {
      font-size: 24rpx;
      text-align: right;
      padding-right: 40rpx;
    }

    .account-info {
      padding-left: 20rpx;
      overflow: hidden;
      display: flex;
      align-items: center;
      height: 44rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #333333;
      line-height: 44rpx;
      justify-content: flex-end;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }

  .tips {
    margin-top: 60rpx;

    div {
      font-weight: bold;
      font-size: 28rpx;
      color: #333333;
      line-height: 33rpx;
    }

    p {
      margin-top: 12rpx;
      height: 40rpx;
      font-size: 28rpx;
      color: #666666;
      line-height: 33rpx;
    }
  }
}
</style>
