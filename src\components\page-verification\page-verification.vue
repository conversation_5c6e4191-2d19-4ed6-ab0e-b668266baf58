<template>
  <div class="page-verification">
    <div class="content">
      <div class="text-p">请识别本人人脸，保持正对手机，确保光线充足</div>
      <div class="text-p">王*强 110304********1219</div>
      <div class="demo-img">
        <image src="https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/login/logo.png"></image>
      </div>
      <div class="tips"
        >通过人脸识别对您的身份进行确认，以确保企业和个人财产安全，您的个人信息将保存于中国石油企业端，且用于加油卡认证、授权、管理等相关工作，认证后中国石油企业端不会对外显示您的个人信息，如您所在企业有核对需求，也仅会显示您的脱敏信息（如张*亮11************25）。</div
      >
      <div class="condition-list">
        <div class="condition-item" v-for="(item, index) in conditionList" :key="index">
          <div class="label">{{ item.label }}</div>
          <u-icon name="checkmark-circle-fill" color="#45d59d" size="16"></u-icon>
        </div>
      </div>

      <div class="btn-row">
        <button class="custom-btn-block orange circle" type="default" plain="true">开始验证</button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';

export default {
  name: 'page-verification',
  components: {},
  props: {},
  data() {
    return {
      conditionList: [
        {
          label: '正对手机',
        },
        {
          label: '光线充足',
        },
        {
          label: '放慢动作',
        },
      ],
    };
  },
  computed: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.page-verification {
  width: 100%;
  height: 100%;
  .content {
    padding: 80rpx 40rpx 0;
    .text-p {
      margin-top: 40rpx;
      font-size: 24rpx;
      color: #333333;
      text-align: center;
    }
    .demo-img {
      padding: 80rpx 0;
      display: flex;
      justify-content: center;
      image {
        width: 400rpx;
        height: 400rpx;
      }
    }
    .tips {
      padding: 0 40rpx;
      font-size: 24rpx;
      color: #999999;
    }
    .condition-list {
      margin-top: 40rpx;
      padding: 20rpx 40rpx;
      background: #ffffff;
      display: flex;
      justify-content:  space-between;
      .condition-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .label {
          font-size: 28rpx;
          color: #999999;
          margin-bottom: 16rpx;
        }
      }
    }
    .btn-row {
      margin-top: 40rpx;
    }
  }
}
</style>
