<template>
  <div class="page-container">
    <u-navbar :title="'订单详情'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <view class="page-order-detail">
          <div class="system-time">
            <div class="countdown-bg1">{{ hour }}</div>
            <span>:</span> <div class="countdown-bg2">{{ minutes }} </div><span>:</span><div class="countdown-bg3">{{ seconds }}</div
            ><span>:</span>
            <div class="countdown-bg4">{{ count }}</div>
          </div>

          <div class="content-view">
            <div class="commodity-order-box">
              <div class="gas-station">
                <div class="gas-station-text">{{ stationName }}</div>
              </div>
              <div class="commodity-order" v-for="(item, index) in orderInfo.orderItems" :key="index">
                <div class="commodity">
                  <image class="commodity-img" v-if="item.imgUrl" :src="item.imgUrl" />
                  <div class="commodity-img placeholder" v-else></div>

                  <div class="commodity-title">{{ item.productName || '' }}</div>
                  <div class="commodity-price">
                    <div class="unit-price">&yen;{{ item.unitPrice || '' }}</div>
                    <div class="num">{{ 'x ' + (item.productQty || '') + (item.productUnit || '') }}</div>
                  </div>
                </div>
                <!-- <div class="payment-row">
                  <div></div>
                  <div class="amount">
                    <div>订单金额：</div>
                    <div>&yen;</div>
                    <div>{{ orderInfo.orderTotalAmount || 0 }}</div>
                  </div>
                </div> -->
                <div class="information">
                  <div class="information-data">
                    <div class="information-data-left">商品编号</div>
                    <div class="information-data-right">
                      {{ item.productNo || '' }}
                      <div class="copy" @click="copyProductNo(item.productNo)">复制</div>
                    </div>
                  </div>
                </div>
                <div class="information">
                  <div class="information-data">
                    <div class="information-data-left">原销售数量</div>
                    <div class="information-data-right">
                      {{ item.productQty || '' }}
                    </div>
                  </div>
                </div>
                <div class="information">
                  <div class="information-data">
                    <div class="information-data-left">退货数量</div>
                    <div class="information-data-right">
                      {{ item.returnQty || '' }}
                    </div>
                  </div>
                </div>
                <div class="payment-row">
                  <div>实退金额</div>
                  <div class="amount">
                    <div>&yen;</div>
                    <div>{{ item.actualRefundAmount }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="details-price">
              <div class="details-price-item">
                <div class="item-left">退款时间</div>
                <div class="item-right">{{ orderInfo.returnOrderTime || '' }}</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">订单编号</div>
                <div class="item-right">
                  <div class="item-right-text">{{ orderInfo.returnOrderNo || '' }}</div>
                  <div class="copy" @click="copyProductNo(orderInfo.returnOrderNo)">复制</div>
                </div>
              </div>
              <div class="details-price-item">
                <div class="item-left">退款总金额</div>
                <div class="item-right red amount">
                  <div>&yen;</div>
                  <div>{{ orderInfo.returnOrderTotalAmount || 0 }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="bottom-btn">
            <button class="custom-btn-block red circle" type="default" @click="back()">完成</button>
          </div>
        </view>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { ORDER_SUBTYPE } from '@/services/enum';
import { getReturnOrderDetailApi } from '@/services/http';

export default {
  components: {},
  data() {
    return {
      ORDER_SUBTYPE: ORDER_SUBTYPE,
      query: {},
      timer: null,
      // 倒计时展示绑定值
      hour: '00',
      minutes: '00',
      seconds: '00',
      count: '00',
      // 订单详情
      orderInfo: null,
    };
  },
  computed: {
    ...mapState({
      companyInfo: state => state?.company?.companyInfo,
    }),
    stationName() {
      return this.orderInfo?.stationName || this.orderInfo?.orderItems?.[0]?.stationName || '中国石油加油站';
    },
  },
  onLoad(query) {
    this.query = query;
    clearInterval(this.timer);
    this.goto();
    this.getData();
  },
  methods: {
    // 获取订单详情
    async getData() {
      try {
        const params = {
          returnOrderNo: this.query?.orderNo,
          returnOrderDetailStatus: true, // 是否需要退款商品详情信息 默认true
        };
        const res = await getReturnOrderDetailApi(params);

        if (res && !res?.success) return;
        this.orderInfo = res?.data;
      } catch (error) {
        console.log(error);
      }
    },
    // 时钟
    goto() {
      this.timer = setInterval(() => {
        this.count = Number(this.count);
        if (this.count < 99) {
          this.count = this.count + 1;
        } else {
          this.count = 0;
        }
        if (this.count < 10) {
          this.count = '0' + this.count;
        }
        var date = new Date();
        this.hour = date.getHours(); // 时
        if (this.hour >= 0 && this.hour <= 9) {
          this.hour = '0' + this.hour;
        }
        this.minutes = date.getMinutes(); // 分
        if (this.minutes >= 0 && this.minutes <= 9) {
          this.minutes = '0' + this.minutes;
        }
        this.seconds = date.getSeconds(); //秒
        if (this.seconds >= 0 && this.seconds <= 9) {
          this.seconds = '0' + this.seconds;
        }
      }, 10);
    },
    // 复制
    copyProductNo(value) {
      uni.setClipboardData({
        data: value,
        success: () => {
          uni.showToast({
            title: '复制成功',
          });
        },
      });
    },
    // 返回
    back() {
      uni.$petro.route({
        type: 'back',
      });
    },
  },
  // 销毁计时器
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
    overflow: hidden;
    .page-order-detail {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .system-time {
        width: 100%;
        height: 160rpx;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #e64f22;
        font-size: 32rpx;
        font-weight: bold;
        span {
          padding: 0 16rpx;
        }
        div {
          width: 88rpx;
          height: 88rpx;
          color: #ffffff;
          border-radius: 20rpx;
          text-align: center;
          line-height: 88rpx;
          font-size: 36rpx;
          box-shadow: 0 2rpx 20rpx 0 rgba(0, 0, 0, 0.07);
        }
        .countdown-bg1 {
          background-image: linear-gradient(288deg, #f9773c 0%, #ff7c34 100%);
        }
        .countdown-bg2 {
          background-image: linear-gradient(288deg, #ff6728 0%, #ff702d 100%);
        }
        .countdown-bg3 {
          background-image: linear-gradient(288deg, #ff5c22 0%, #ff6426 100%);
        }
        .countdown-bg4 {
          background-image: linear-gradient(288deg, #ff501c 0%, #ff5921 100%);
        }
      }

      .content-view {
        width: 100%;
        flex: 1;
        padding: 32rpx;
        overflow-y: auto;
        box-sizing: border-box;

        .commodity-order-box {
          background: #ffffff;
          border-radius: 16rpx;
          padding: 20rpx 28rpx;

          .gas-station {
            display: flex;
            align-items: center;
            overflow: hidden;

            .gas-station-img {
              width: 33rpx;
              height: 32rpx;
              flex-shrink: 0;
            }

            .gas-station-text {
              margin-left: 10rpx;
              font-size: 28rpx;
              font-weight: bold;
              color: #333333;
              line-height: 40rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .commodity-order {
            margin-top: 15rpx;
            &:not(:first-child) {
              margin-top: 30rpx;
            }
            .commodity {
              display: flex;

              .commodity-img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 10rpx;
                overflow: hidden;
                margin-right: 20rpx;
                &.placeholder {
                  background: #f0f0f0;
                }
              }

              .detail-left-text {
                width: 78rpx;
                height: 78rpx;
                background: #f0f0f0;
                border-radius: 16rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                span:nth-child(1) {
                  color: #333333;
                  font-size: 32rpx;
                  font-weight: bold;
                }
                span:nth-child(2) {
                  color: #333333;
                  font-size: 20rpx;
                  line-height: 20rpx;
                  margin-top: -20rpx;
                }
              }

              .detail-left-img {
                width: 100rpx;
                height: 100rpx;
                border-radius: 10rpx;
                margin-right: 20rpx;
              }

              .commodity-title {
                margin-left: 24rpx;
                flex: 1;
                color: #333;
                font-size: 28rpx;
              }

              .commodity-price {
                text-align: right;

                .unit-price {
                  margin-top: 4rpx;
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #333333;
                  line-height: 40rpx;
                }

                .num {
                  font-size: 20rpx;
                  font-weight: 400;
                  color: #666666;
                  line-height: 40rpx;
                }
              }
            }
            .information {
              margin-top: 8rpx;
              .information-data {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 15rpx;
                flex-wrap: wrap;

                &:nth-of-type(1) {
                  margin-top: 0;
                }

                .information-data-left {
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #333333;
                  line-height: 48rpx;
                }

                .information-data-right {
                  text-align: right;
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #666666;
                  display: flex;
                  align-items: center;
                  flex: 1;
                  justify-content: flex-end;
                  line-height: 48rpx;

                  .copy {
                    margin-left: 10rpx;
                    width: 64rpx;
                    height: 32rpx;
                    background: #ebf6ff;
                    border-radius: 4rpx;
                    font-size: 20rpx;
                    color: #47b2ff;
                    line-height: 32rpx;
                    text-align: center;
                  }
                }
              }
            }
          }

          .payment-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8rpx;
            div:nth-child(1) {
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 48rpx;
            }
            .amount {
              display: flex;
              flex-direction: row;

              div {
                font-size: 28rpx;
                line-height: 44rpx;
              }

              div:nth-child(1) {
                color: #ff7033;
                font-size: 24rpx;
                margin-top: 2rpx;
              }

              div:nth-child(2) {
                color: #ff7033;
                font-weight: bold;
                font-size: 32rpx;
              }
            }
          }
        }

        .details-price {
          margin-top: 24rpx;
          background: #ffffff;
          border-radius: 16rpx;
          padding: 20rpx 28rpx;

          .details-price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .item-left {
              font-size: 28rpx;
              font-weight: 400;
              color: #666666;
              line-height: 67rpx;
            }

            .item-right {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 67rpx;

              .item-right-text {
                line-height: 50rpx;
                font-size: 26rpx;
                font-weight: 400;
                color: #333333;
              }

              .copy {
                margin-left: 10rpx;
                width: 64rpx;
                height: 32rpx;
                background: #ebf6ff;
                border-radius: 4rpx;
                font-size: 20rpx;
                color: #47b2ff;
                line-height: 32rpx;
                text-align: center;
              }

              .red {
                color: #ff5500;
              }

              &.amount {
                display: flex;
                flex-direction: row;

                div {
                  font-size: 28rpx;
                  line-height: 44rpx;
                }

                div:nth-child(1) {
                  color: #ff7033;
                  font-size: 24rpx;
                  margin-top: 6rpx;
                }

                div:nth-child(2) {
                  color: #ff7033;
                  font-weight: bold;
                  font-size: 32rpx;
                }
              }
            }

            .item-right-button {
              padding: 0 20rpx;
              height: 48rpx;
              border-radius: 4rpx;
              border: 1rpx solid #333333;
              line-height: 48rpx;
              font-size: 26rpx;
              font-weight: 400;
              color: #333333;
              text-align: center;
            }

            .color {
              color: #e64f22;
              border: 1rpx solid #e64f22;
            }
          }
        }

        .pay-list {
          padding: 0 32rpx;
        }

        .details-price-item-panel {
          background: #f5f8fa;
          border-radius: 8rpx;
        }
      }

      .bottom-btn {
        padding: 36rpx 48rpx 0;
        background: #ffffff;
        padding-bottom: env(safe-area-inset-bottom);
      }
    }
  }
}
</style>
