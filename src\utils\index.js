/**
 * 格式化时间
 * @param time
 * @param {*} format
 * @returns
 */
export function dateFormat(time, format = 'YYYY-MM-DD HH:mm:ss') {
  const date = new Date(time);
  let o = {
    'M+': date.getMonth() + 1, //月份
    'D+': date.getDate(), //日
    'H+': date.getHours(), //小时
    'm+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
    'q+': Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  if (/(Y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}

/**
 * 开始轮询
 *
 * @param callback 回调函数，当查询结束后被调用，并将停止轮询的函数作为参数传递
 * @param delayMs 轮询间隔（毫秒）
 * @returns 返回一个函数，用于停止轮询
 */
export function startPolling(callback, delayMs) {
  let isPolling = true; // 添加一个标志来控制是否继续轮询

  async function poll() {
    console.log('正在查询...');
    // 模拟异步操作，假设查询结束后再次轮询
    await new Promise(resolve => {
      setTimeout(async function () {
        await callback(stopPolling); // 调用回调函数，并将 stopPolling 函数作为参数传递
        resolve();
      }, delayMs);
    });

    // 如果 isPolling 为 true，则继续下一次轮询
    if (isPolling) {
      poll();
    }
  }

  // 第一次启动轮询
  poll();

  // 返回一个函数来停止轮询
  function stopPolling() {
    isPolling = false;
  }
}

/**
 * 距离转换保留两位小数
 * 
 * @param num 距离
 */
export function formatDistanceToTwoDecimal(num) {
  num = Number(num);
  // 判断 num 是否是有效的数字
  if (typeof num !== 'number' || isNaN(num)) {
    return '0.00';
  }
  // 将数值乘以1000进行四舍五入
  let roundedNum = Math.round(num * 1000);
  // 获取整数部分和小数部分
  let integerPart = Math.floor(roundedNum / 1000);
  let decimalPart = Math.floor((roundedNum % 1000) / 10); // 取前两位小数
  // 检查第三位小数
  let thirdDecimal = roundedNum % 10;
  // 如果第三位小数大于0，则第二位小数加1
  if (thirdDecimal > 0) {
      decimalPart += 1;
      // 如果 decimalPart 达到 100，进行进位
      if (decimalPart === 100) {
          integerPart += 1;
          decimalPart = 0;
      }
  }
  // 格式化输出，确保返回的结果有两位小数
  return `${integerPart}.${decimalPart.toString().padStart(2, '0')}`;
}