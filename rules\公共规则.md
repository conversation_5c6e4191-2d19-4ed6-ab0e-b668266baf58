---
description: UniApp + Vue 2 司机小程序项目公共开发规则
globs: .vue,.js,.scss,.json
alwaysApply: true
---

### 总体描述
该文档定义了基于UniApp + Vue 2的司机小程序项目的公共开发规则，涵盖技术栈规范、工程结构、编码规范、命名规范等核心内容，为项目开发提供统一的标准和指引。

#### 应用范围
本文档适用于petro-soti-zyzx-miniapp-driver项目的所有开发人员，确保团队成员遵循统一的开发规范，提高代码质量和项目可维护性。

#### 使用要求
开发人员在进行UniApp小程序项目开发时，必须严格遵循本文档中的技术栈规范、工程结构和编码规范进行开发，确保项目的一致性和可维护性。

## 规则1 技术栈规范

### 核心技术栈
基于项目实际情况，明确以下技术栈的使用规范：

#### 应用范围
适用于项目的所有开发场景，包括页面开发、组件开发、状态管理、网络请求等。

#### 使用要求
严格按照以下技术栈进行开发，禁止引入未经批准的第三方库。

#### 技术栈清单

**框架层**
- **UniApp**: 2.0.2-3090820231124001 - 跨平台应用开发框架
- **Vue**: 2.6.14 - 渐进式JavaScript框架，使用Options API
- **Vuex**: 3.6.2 - Vue.js的状态管理模式

**构建工具**
- **Vue CLI**: 5.0 - Vue.js开发的标准工具
- **Webpack**: 通过Vue CLI集成
- **Babel**: ES6+语法转换，支持UniApp平台适配

**UI组件库**
- **uView UI**: 2.0.36 - UniApp生态UI框架，使用u-前缀
- **@petro-soti/foudation-zyzx**: 1.2.0-1090 - 中石油自研组件库，使用petro-/zyzx-前缀

**样式处理**
- **SCSS/Sass**: 1.70.0 - CSS预处理器
- **sass-loader**: 10 - Webpack的Sass加载器
- **PostCSS**: CSS后处理器，支持autoprefixer

**网络请求**
- **flyio**: 0.6.2 - 支持请求转发的HTTP请求库
- **uni.$petro.http**: 基于flyio封装的统一请求方法

**开发工具**
- **vconsole**: 3.15.1 - 移动端调试工具
- **commitizen**: 4.3.0 - Git提交规范工具

#### 示例代码

```javascript
// Vue 2 Options API 组件示例
export default {
  name: 'ComponentName',
  components: {},
  props: {
    data: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false
    };
  },
  computed: {
    // 使用Vuex辅助函数
    ...mapState(['userInfo']),
    ...mapGetters(['isLogin'])
  },
  methods: {
    // 使用uni.$petro.http进行网络请求
    async fetchData() {
      const { success, data } = await uni.$petro.http('api.getData.h5', {}, {
        mockResponse: { success: true, data: [] }
      });
      if (success) {
        this.handleData(data);
      }
    }
  },
  mounted() {
    this.fetchData();
  }
};
```

## 规则2 工程结构规范

### 目录结构标准
基于UniApp项目特点，定义标准的目录结构和文件组织方式。

#### 应用范围
适用于项目的所有文件和目录的组织，包括页面、组件、资源、配置等。

#### 使用要求
严格按照以下目录结构进行文件组织，禁止随意创建目录或放置文件。

#### 标准目录结构

```text
petro-soti-zyzx-miniapp-driver/     # 项目根目录
├── build/                          # 构建配置
│   ├── manifest/                   # manifest构建工具
│   ├── tools/                      # 构建工具
│   └── env/                        # 环境配置脚本
├── dist/                           # 构建输出目录
├── public/                         # 静态资源
│   └── index.html                  # H5入口文件
├── src/                            # 源码目录
│   ├── api/                        # API数据管理
│   │   └── data.js                 # 数据API
│   ├── assets/                     # 静态资源文件
│   │   └── *.png                   # 图片资源
│   ├── components/                 # 公共组件
│   │   ├── p-*/                    # 功能组件 (p-前缀)
│   │   └── page-*/                 # 页面级组件
│   ├── mycomponents/               # 自定义组件
│   │   ├── keyboard/               # 键盘组件
│   │   └── security-plugin/        # 安全插件
│   ├── pages/                      # 页面文件
│   │   ├── index/                  # 首页
│   │   ├── station-list/           # 加油站列表
│   │   └── */                      # 其他页面
│   ├── services/                   # 业务服务
│   │   ├── http/                   # HTTP请求模块
│   │   ├── store/                  # Vuex状态管理
│   │   └── enum/                   # 枚举定义
│   ├── static/                     # 静态资源
│   │   ├── images/                 # 图片资源
│   │   └── *.png                   # 其他静态文件
│   ├── utils/                      # 工具函数
│   │   ├── index.js                # 通用工具
│   │   └── sdk.js                  # SDK工具
│   ├── wxcomponents/               # 微信原生组件
│   ├── App.vue                     # 应用入口组件
│   ├── main.js                     # 应用入口文件
│   ├── pages.json                  # 页面路由配置
│   ├── manifest.json               # 应用配置文件
│   ├── uni.scss                    # 全局样式变量
│   └── config.index.local.js       # 环境配置文件
├── rules/                          # 开发规范文档
├── package.json                    # 项目依赖配置
├── vue.config.js                   # Vue CLI配置
├── babel.config.js                 # Babel配置
└── postcss.config.js               # PostCSS配置
```

## 规则3 编码规范

### Vue组件编码规范
定义Vue组件的编写标准，确保代码的一致性和可维护性。

#### 应用范围
适用于所有Vue组件的开发，包括页面组件和公共组件。

#### 使用要求
严格按照Vue 2 Options API规范编写组件，禁止使用Vue 3 Composition API。

#### 组件结构规范

```vue
<template>
  <view class="component-name">
    <!-- 使用uView UI组件 -->
    <u-button type="primary" @click="handleClick">
      {{ buttonText }}
    </u-button>

    <!-- 使用自研组件 -->
    <petro-layout :loading="loading">
      <view class="content">
        {{ content }}
      </view>
    </petro-layout>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  name: 'ComponentName', // 必须定义name属性
  components: {
    // 局部注册组件
  },
  props: {
    // Props定义必须包含类型和默认值
    title: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      content: ''
    };
  },
  computed: {
    // 使用Vuex辅助函数
    ...mapState('moduleName', ['stateProperty']),

    // 计算属性
    buttonText() {
      return this.loading ? '加载中...' : '确定';
    }
  },
  watch: {
    // 监听器
    title: {
      handler(newVal) {
        this.handleTitleChange(newVal);
      },
      immediate: true
    }
  },
  methods: {
    // 使用Vuex辅助函数
    ...mapActions('moduleName', ['actionName']),

    // 组件方法
    handleClick() {
      this.actionName();
    },

    handleTitleChange(title) {
      this.content = `标题：${title}`;
    }
  },
  // 生命周期钩子
  created() {
    // 组件创建时的逻辑
  },
  mounted() {
    // 组件挂载后的逻辑
  },
  beforeDestroy() {
    // 组件销毁前的清理逻辑
  }
};
</script>

<style scoped lang="scss">
// 必须使用scoped和scss
.component-name {
  padding: 20rpx;

  .content {
    margin-top: 20rpx;
    color: #333;
  }
}
</style>
```

### 命名规范

#### 文件命名
- **页面文件**: kebab-case，如 `station-list.vue`
- **组件文件**: kebab-case，如 `p-test.vue`、`page-home.vue`
- **JavaScript文件**: kebab-case，如 `api-service.js`
- **样式文件**: kebab-case，如 `common-styles.scss`

#### 组件命名
- **功能组件**: p-前缀 + 功能名称，如 `p-map`、`p-test`
- **页面组件**: page-前缀 + 页面名称，如 `page-home`、`page-order`
- **自定义组件**: 功能描述，如 `keyboard`、`security-plugin`

#### 变量命名
- **data属性**: camelCase，如 `userInfo`、`isLoading`
- **方法名**: camelCase，如 `handleClick`、`fetchData`
- **常量**: UPPER_SNAKE_CASE，如 `API_BASE_URL`

#### CSS类名
- **组件根类**: 组件名，如 `.p-test`、`.page-home`
- **子元素类**: BEM规范，如 `.p-test__content`、`.p-test--active`

### 代码质量规范

#### 代码注释
```javascript
/**
 * 获取用户信息
 * @param {string} userId - 用户ID
 * @param {Object} options - 请求配置
 * @returns {Promise} 返回用户信息
 */
async fetchUserInfo(userId, options = {}) {
  const { success, data } = await uni.$petro.http(
    'user.getUserInfo.h5',
    { userId },
    {
      ...options,
      mockResponse: {
        success: true,
        data: { name: '测试用户', mobile: '138****8888' }
      }
    }
  );
  return success ? data : null;
}
```

#### 错误处理
```javascript
methods: {
  async handleSubmit() {
    try {
      this.loading = true;
      const result = await this.submitData();
      if (result.success) {
        uni.showToast({ title: '提交成功', icon: 'success' });
      } else {
        uni.showToast({ title: result.message || '提交失败', icon: 'none' });
      }
    } catch (error) {
      console.error('提交失败:', error);
      uni.showToast({ title: '网络错误，请重试', icon: 'none' });
    } finally {
      this.loading = false;
    }
  }
}
```

#### 性能优化
```javascript
// 使用防抖处理频繁操作
methods: {
  handleSearch: uni.$petro.Utils.debounce(function(keyword) {
    this.searchData(keyword);
  }, 500),

  // 使用轮询任务
  startPolling() {
    this.pollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
      const result = await this.checkStatus();
      return result.completed; // 返回true时停止轮询
    }, 3000);
  }
}
```

#### 规则3.命名规范

##### 3.1 组件命名

- 单文件组件（SFC）采用大驼峰命名法，例如`UserProfile.vue`。
- 目录名采用短横线命名法，例如`user-profile/`

##### 3.2 JS 文件命名

- 工具函数文件用小写字母加下划线，例如`date_utils.js`。
- 路由文件使用`routes.js`。
- 状态管理文件使用`store.js`。

##### 3.3 CSS/SCSS 文件命名

- 全局样式文件用`global.scss`。
- 组件样式文件用`component-name.scss`。

#### 规则4.代码风格规范

##### 4.1 HTML 模板规范

- 属性采用短横线命名法，例如`v-bind:is-loading="isLoading"`。
- 指令缩写统一使用`:`代替`v-bind:`，`@`代替`v-on:`。
- 组件嵌套层级不超过 4 层。

##### 4.2 JavaScript 规范

- 使用 ES6 + 语法，如箭头函数、解构赋值、扩展运算符。

- 每行代码长度不超过 120 个字符。

- 注释遵循 JSDoc 规范

  ```typescript
  /**
   * 获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise<UserInfo>} 用户信息
   */
  async function getUserInfo(userId) {
    // ...
  }
  ```

##### 4.3 CSS 规范

- 使用 SCSS 或 CSS Modules。
- 类名采用 BEM 命名法，例如`.user-profile__avatar`。
- 避免使用内联样式。