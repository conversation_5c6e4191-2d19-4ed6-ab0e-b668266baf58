---
description: UniApp + Vue 2 司机小程序项目公共开发规则
globs: .vue,.js,.scss,.json
alwaysApply: true
---

# UniApp + Vue 2 司机小程序项目公共开发规则

## 总体描述

该文档定义了基于UniApp + Vue 2的司机小程序项目的公共开发规则，涵盖技术栈规范、工程结构、编码规范、命名规范等核心内容，为项目开发提供统一的标准和指引。

### 应用范围

本文档适用于petro-soti-zyzx-miniapp-driver项目的所有开发人员，确保团队成员遵循统一的开发规范，提高代码质量和项目可维护性。

### 使用要求

开发人员在进行UniApp小程序项目开发时，必须严格遵循本文档中的技术栈规范、工程结构和编码规范进行开发，确保项目的一致性和可维护性。

## 规则1 技术栈规范

### 强制性技术约束

基于项目实际情况，以下技术栈为强制性要求，**严禁使用其他替代方案**：

#### 核心框架约束

**UniApp + Vue 2 技术栈**

- **UniApp**: 2.0.2-3090820231124001 - 跨平台应用开发框架
  - 支持平台：微信小程序、支付宝小程序、mPaaS小程序、H5、App Plus
  - 支持插件模式：微信小程序插件、支付宝小程序插件、mPaaS小程序插件
- **Vue**: 2.6.14 - 渐进式JavaScript框架
  - **强制使用Options API**，严禁使用Vue 3或Composition API
  - 版本范围：>= 2.6.14 < 2.7
- **Vuex**: 3.6.2 - Vue.js的状态管理模式
  - **严禁使用Pinia或其他状态管理库**

#### 构建工具链

**Vue CLI + Webpack 构建体系**

- **Vue CLI**: 5.0 - Vue.js开发的标准工具
- **Webpack**: 通过Vue CLI集成，支持自定义配置
- **Babel**: ES6+语法转换，支持UniApp平台适配
  - 核心版本：@babel/core ^7.24.0
  - 运行时转换：@babel/plugin-transform-runtime ^7.24.0
  - 预设：@babel/preset-env ^7.24.0

#### UI组件库规范

**双组件库架构**

- **uView UI**: 2.0.36 - UniApp生态UI框架
  - 组件前缀：u-
  - 优先级：第一选择
  - 自动引入：通过easycom配置
- **@petro-soti/foudation-zyzx**: 2.0.0-1010 - 中石油自研组件库
  - 组件前缀：petro-、zyzx-
  - 优先级：第二选择
  - 自动引入：通过easycom配置

#### 样式处理规范

**SCSS预处理器**

- **SCSS/Sass**: 1.70.0 - CSS预处理器
  - **严禁使用CSS-in-JS或styled-components**
- **sass-loader**: 10 - Webpack的Sass加载器
- **PostCSS**: CSS后处理器，支持autoprefixer

#### 网络请求规范

**统一HTTP请求方案**

- **flyio**: 0.6.2 - 支持请求转发的HTTP请求库
- **uni.$petro.http**: 基于flyio封装的统一请求方法
  - **强制使用**，严禁使用axios、fetch或其他HTTP库
  - 支持内联Mock数据配置
  - 统一错误处理和响应格式

#### 开发工具

**调试和规范工具**

- **vconsole**: 3.15.1 - 移动端调试工具（仅H5环境）
- **commitizen**: 4.3.0 - Git提交规范工具

### 技术栈使用示例

#### Vue 2 Options API 标准组件

```javascript
// Vue 2 Options API 标准组件示例
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: 'StandardComponent',
  components: {
    // 局部注册组件
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      list: []
    };
  },
  computed: {
    // 使用Vuex辅助函数
    ...mapState('moduleName', ['userInfo']),
    ...mapGetters('moduleName', ['isLogin']),

    // 计算属性
    displayTitle() {
      return this.title || '默认标题';
    }
  },
  watch: {
    title: {
      handler(newVal) {
        console.log('标题变化:', newVal);
      },
      immediate: true
    }
  },
  methods: {
    // 使用Vuex辅助函数
    ...mapActions('moduleName', ['updateUserInfo']),

    // 使用uni.$petro.http进行网络请求
    async fetchData() {
      try {
        this.loading = true;
        const { success, data, message } = await uni.$petro.http(
          'api.getData.h5',
          { pageNum: 1, pageSize: 20 },
          {
            mockResponse: {
              success: true,
              data: { list: [], total: 0 },
              message: '请求成功'
            }
          }
        );

        if (success) {
          this.list = data.list || [];
        } else {
          uni.showToast({ title: message || '获取数据失败', icon: 'none' });
        }
      } catch (error) {
        console.error('请求异常:', error);
        uni.showToast({ title: '网络异常，请重试', icon: 'none' });
      } finally {
        this.loading = false;
      }
    }
  },
  // 生命周期钩子
  created() {
    // 组件创建时的逻辑
  },
  mounted() {
    this.fetchData();
  },
  beforeDestroy() {
    // 组件销毁前的清理逻辑
  }
};
```

#### uView UI 组件使用示例

```vue
<template>
  <view class="page-container">
    <!-- uView UI 组件使用 -->
    <u-navbar title="页面标题" :border-bottom="false" />

    <u-form :model="form" ref="form" label-width="120">
      <u-form-item label="用户名" prop="username">
        <u-input v-model="form.username" placeholder="请输入用户名" />
      </u-form-item>

      <u-form-item label="密码" prop="password">
        <u-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
        />
      </u-form-item>
    </u-form>

    <u-button
      type="primary"
      :loading="loading"
      @click="handleSubmit"
    >
      提交
    </u-button>
  </view>
</template>
```

#### 自研组件使用示例

```vue
<template>
  <view class="page-container">
    <!-- 中石油自研组件使用 -->
    <petro-layout :loading="pageLoading">
      <zyzx-company-bar
        :company-info="companyInfo"
        @switch="handleCompanySwitch"
      />

      <zyzx-page-car-wallet
        :wallet-data="walletData"
        @refresh="handleRefresh"
      />
    </petro-layout>
  </view>
</template>
```

## 规则2 工程结构规范

### 标准目录结构

基于UniApp项目特点和实际业务需求，定义标准的目录结构和文件组织方式。

#### 核心目录说明

**src/ 源码目录结构**

```text
src/
├── pages/                          # 页面目录（按功能模块组织）
│   ├── index/                      # 首页模块
│   │   └── index.vue               # 首页组件
│   ├── station-list/               # 油站列表模块
│   │   └── station-list.vue        # 油站列表组件
│   ├── car/                        # 车辆管理模块
│   │   ├── car.vue                 # 车辆主页
│   │   └── pages/                  # 车辆子页面
│   │       ├── car-details-page.vue    # 车辆详情
│   │       └── car-type.vue            # 车辆类型
│   ├── wallet/                     # 钱包模块
│   │   └── pages/                  # 钱包子页面
│   │       ├── bill-list-page.vue      # 账单列表
│   │       └── car-wallet-list.vue     # 车辆钱包列表
│   └── mine/                       # 个人中心模块
│       └── pages/                  # 个人中心子页面
│           ├── about-page.vue          # 关于页面
│           └── edit-password-page.vue  # 修改密码
├── components/                     # 公共组件目录
│   ├── p-test/                     # 功能组件（p-前缀）
│   │   └── p-test.vue              # 测试组件
│   ├── p-map/                      # 地图组件
│   │   └── p-map.vue               # 地图功能组件
│   ├── page-home/                  # 页面级组件（page-前缀）
│   │   └── page-home.vue           # 首页页面组件
│   └── zyzx-company-bar/           # 自研组件（zyzx-前缀）
│       └── zyzx-company-bar.vue    # 企业栏组件
├── services/                       # 业务服务层
│   ├── http/                       # HTTP请求模块
│   │   ├── index.js                # HTTP服务入口
│   │   ├── api.js                  # 通用API
│   │   ├── account.js              # 账户相关API
│   │   ├── car.js                  # 车辆相关API
│   │   ├── station.js              # 加油站相关API
│   │   └── order.js                # 订单相关API
│   ├── store/                      # Vuex状态管理
│   │   ├── index.js                # store入口文件
│   │   ├── roles.js                # 用户角色状态
│   │   ├── account.js              # 账户状态
│   │   ├── car.js                  # 车辆状态
│   │   └── station.js              # 加油站状态
│   └── enum/                       # 枚举定义
│       └── index.js                # 枚举常量
├── static/                         # 静态资源
│   ├── images/                     # 图片资源
│   ├── xml/                        # XML配置文件
│   ├── gwcli1-0-2.wasm.br         # WASM文件
│   └── *.png                       # 图标文件
├── utils/                          # 工具函数
│   ├── index.js                    # 通用工具函数
│   └── sdk.js                      # SDK工具
├── api/                            # API数据
│   └── data.js                     # 静态数据
├── assets/                         # 资源文件
│   └── lg.png                      # Logo等资源
├── App.vue                         # 应用入口组件
├── main.js                         # 应用入口文件
├── pages.json                      # 页面路由配置
├── uni.scss                        # 全局样式变量
├── uni.promisify.adaptor.js        # Promise适配器
├── config.index.local.js           # 环境配置文件
├── mini.project.json               # 小程序项目配置
├── plugin.js                       # 插件入口
├── plugin.json                     # 插件配置
└── .manifest.json                  # 应用清单
```

#### 目录组织原则

**页面目录组织**

1. **按功能模块分组**：相关页面放在同一目录下
2. **支持子页面嵌套**：复杂模块可创建pages子目录
3. **命名规范**：使用kebab-case命名，语义化明确

**组件目录组织**

1. **按组件类型分类**：
   - `p-*`：功能组件，可复用的业务组件
   - `page-*`：页面级组件，特定页面的大型组件
   - `zyzx-*`：自研组件，企业级组件

2. **组件结构**：每个组件一个目录，包含.vue文件和相关资源

**服务层组织**

1. **HTTP服务**：按业务模块拆分API文件
2. **状态管理**：按业务领域拆分Vuex模块
3. **枚举定义**：统一管理常量和枚举值

## 规则3 编码规范

### Vue组件编码规范

定义Vue组件的编写标准，确保代码的一致性和可维护性。

#### 组件结构标准

**标准Vue组件结构**

```vue
<template>
  <view class="component-name">
    <!-- 使用uView UI组件 -->
    <u-navbar title="页面标题" :border-bottom="false" />

    <!-- 条件渲染 -->
    <view v-if="loading" class="loading-container">
      <u-loading-icon mode="flower" />
    </view>

    <!-- 列表渲染 -->
    <view v-else class="content-container">
      <view
        v-for="item in dataList"
        :key="item.id"
        class="list-item"
        @click="handleItemClick(item)"
      >
        {{ item.name }}
      </view>
    </view>

    <!-- 使用自研组件 -->
    <zyzx-company-bar
      :company-info="companyInfo"
      @switch="handleCompanySwitch"
    />
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  name: 'ComponentName', // 必须定义name属性
  components: {
    // 局部注册组件（如果需要）
  },
  props: {
    // Props定义必须包含类型和默认值
    title: {
      type: String,
      default: '',
      required: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      content: ''
    };
  },
  computed: {
    // 使用Vuex辅助函数
    ...mapState('moduleName', ['stateProperty']),

    // 计算属性
    buttonText() {
      return this.loading ? '加载中...' : '确定';
    }
  },
  watch: {
    // 监听器
    title: {
      handler(newVal) {
        this.handleTitleChange(newVal);
      },
      immediate: true
    }
  },
  methods: {
    // 使用Vuex辅助函数
    ...mapActions('moduleName', ['actionName']),

    // 组件方法
    handleClick() {
      this.actionName();
    },

    handleTitleChange(title) {
      this.content = `标题：${title}`;
    }
  },
  // 生命周期钩子
  created() {
    // 组件创建时的逻辑
  },
  mounted() {
    // 组件挂载后的逻辑
  },
  beforeDestroy() {
    // 组件销毁前的清理逻辑
  }
};
</script>

<style scoped lang="scss">
// 必须使用scoped和scss
.component-name {
  padding: 20rpx;

  .content {
    margin-top: 20rpx;
    color: #333;
  }
}
</style>
```

### 命名规范

#### 文件命名
- **页面文件**: kebab-case，如 `station-list.vue`
- **组件文件**: kebab-case，如 `p-test.vue`、`page-home.vue`
- **JavaScript文件**: kebab-case，如 `api-service.js`
- **样式文件**: kebab-case，如 `common-styles.scss`

#### 组件命名
- **功能组件**: p-前缀 + 功能名称，如 `p-map`、`p-test`
- **页面组件**: page-前缀 + 页面名称，如 `page-home`、`page-order`
- **自定义组件**: 功能描述，如 `keyboard`、`security-plugin`

#### 变量命名
- **data属性**: camelCase，如 `userInfo`、`isLoading`
- **方法名**: camelCase，如 `handleClick`、`fetchData`
- **常量**: UPPER_SNAKE_CASE，如 `API_BASE_URL`

#### CSS类名
- **组件根类**: 组件名，如 `.p-test`、`.page-home`
- **子元素类**: BEM规范，如 `.p-test__content`、`.p-test--active`

### 代码质量规范

#### 代码注释
```javascript
/**
 * 获取用户信息
 * @param {string} userId - 用户ID
 * @param {Object} options - 请求配置
 * @returns {Promise} 返回用户信息
 */
async fetchUserInfo(userId, options = {}) {
  const { success, data } = await uni.$petro.http(
    'user.getUserInfo.h5',
    { userId },
    {
      ...options,
      mockResponse: {
        success: true,
        data: { name: '测试用户', mobile: '138****8888' }
      }
    }
  );
  return success ? data : null;
}
```

#### 错误处理
```javascript
methods: {
  async handleSubmit() {
    try {
      this.loading = true;
      const result = await this.submitData();
      if (result.success) {
        uni.showToast({ title: '提交成功', icon: 'success' });
      } else {
        uni.showToast({ title: result.message || '提交失败', icon: 'none' });
      }
    } catch (error) {
      console.error('提交失败:', error);
      uni.showToast({ title: '网络错误，请重试', icon: 'none' });
    } finally {
      this.loading = false;
    }
  }
}
```

#### 性能优化
```javascript
// 使用防抖处理频繁操作
methods: {
  handleSearch: uni.$petro.Utils.debounce(function(keyword) {
    this.searchData(keyword);
  }, 500),

  // 使用轮询任务
  startPolling() {
    this.pollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
      const result = await this.checkStatus();
      return result.completed; // 返回true时停止轮询
    }, 3000);
  }
}
```

## 规则4 代码质量规范

### 代码注释规范

**JSDoc注释标准**

```javascript
/**
 * 获取用户信息
 * @param {string} userId - 用户ID
 * @param {Object} options - 请求配置选项
 * @param {boolean} options.showLoading - 是否显示加载提示
 * @returns {Promise<Object>} 返回用户信息对象
 * @example
 * const userInfo = await getUserInfo('123', { showLoading: true });
 */
async function getUserInfo(userId, options = {}) {
  // 实现逻辑
}
```

**Vue组件注释**

```vue
<template>
  <!-- 用户信息展示区域 -->
  <view class="user-info">
    <!-- 头像区域 -->
    <view class="avatar-section">
      <image :src="userInfo.avatar" class="avatar" />
    </view>
  </view>
</template>

<script>
/**
 * 用户信息组件
 * @description 展示用户基本信息，支持编辑功能
 * <AUTHOR>
 * @date 2024-01-01
 */
export default {
  name: 'UserInfo',
  // ...
};
</script>
```

### 错误处理规范

**统一错误处理模式**

```javascript
// 1. API调用错误处理
methods: {
  async fetchData() {
    try {
      this.loading = true;
      const result = await this.apiCall();

      if (result.success) {
        this.handleSuccess(result.data);
      } else {
        this.handleBusinessError(result.errorCode, result.message);
      }
    } catch (error) {
      this.handleNetworkError(error);
    } finally {
      this.loading = false;
    }
  },

  handleBusinessError(errorCode, message) {
    // 业务错误处理
    switch (errorCode) {
      case 'B_C15_000005':
        // Token过期处理
        this.$store.dispatch('user/logout');
        break;
      default:
        uni.showToast({ title: message || '操作失败', icon: 'none' });
    }
  },

  handleNetworkError(error) {
    console.error('网络请求失败:', error);
    uni.showToast({ title: '网络异常，请重试', icon: 'none' });
  }
}
```

### 性能优化规范

**防抖和节流**

```javascript
// 使用防抖处理搜索
methods: {
  handleSearch: uni.$petro.Utils.debounce(function(keyword) {
    this.searchData(keyword);
  }, 500),

  // 使用节流处理滚动
  handleScroll: uni.$petro.Utils.throttle(function(e) {
    this.updateScrollPosition(e.detail.scrollTop);
  }, 100)
}
```

**列表优化**

```vue
<template>
  <!-- 使用虚拟列表优化长列表 -->
  <scroll-view
    scroll-y
    class="scroll-container"
    @scrolltolower="loadMore"
  >
    <view
      v-for="item in displayList"
      :key="item.id"
      class="list-item"
    >
      {{ item.name }}
    </view>
  </scroll-view>
</template>
```

### 代码风格规范

**HTML模板规范**

1. **属性命名**：使用kebab-case
2. **指令缩写**：统一使用`:`代替`v-bind:`，`@`代替`v-on:`
3. **组件嵌套**：不超过4层
4. **条件渲染**：优先使用`v-if`而非`v-show`（除非需要频繁切换）

**JavaScript规范**

1. **ES6+语法**：使用箭头函数、解构赋值、扩展运算符
2. **代码长度**：每行不超过120个字符
3. **变量声明**：优先使用`const`，需要重新赋值时使用`let`
4. **函数定义**：使用具有描述性的函数名

**SCSS规范**

1. **BEM命名法**：`.block__element--modifier`
2. **嵌套层级**：不超过3层
3. **变量使用**：统一使用uni.scss中定义的变量
4. **避免内联样式**：所有样式都应在`<style>`标签中定义