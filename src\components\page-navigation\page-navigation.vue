<template>
  <div class="page-container">
    <u-navbar :title="'网点导航'" :autoBack="false" :leftIconColor="'transparent'" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div class="page-navigation">
          <p-map :mapId="'map-navigation'" pageChannel="navigation" :showMap="showMap" height="350rpx"></p-map>
          <div class="station-box">
            <station-list :pageChannel="'navigation'"></station-list>
          </div>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import pMap from '../p-map/p-map.vue';
import stationList from './components/station-list.vue';

export default {
  name: 'page-navigation',
  components: {
    pMap,
    stationList,
  },
  props: {
    showMap: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      osName: uni.$petro.store.systemInfo.osName,
      // 权限
      permissions: null,
      // 为了记录用户第一次进来点的是去开启还是取消,再切换回来app后需要操作(zk要求)
      sysModalStatus: '',
    };
  },
  computed: {},
  async mounted() {
    uni.onAppShow(async res => {
      this.permissions = await this.getPermissions();
      // 系统和app都有权限, 并且当前油站列表没有数据, 获取油站列表
      if (this.permissions?.gps == 1 && !this.stationList?.length) {
        this.$store.commit('updateLocalPre', {
          page: 'navigation',
          localPre: true,
          isDefaultGPS: false,
        });
      }

      // zk要求: 安卓流程,用户打开app后无权限，弹出系统权限弹窗点击去设置后再回来app,需要再次判断弹出app权限弹窗,只弹一次
      if (this.osName == 'android' && this.sysModalStatus === 'confirm' && [2, 4].includes(this.permissions?.gps)) {
        this.sysModalStatus = '';
        this.showAppPermissionModal();
      }
    });

    // 权限判断
    // 1 系统开,app开
    // 2 系统开,app关
    // 3 系统关,app开
    // 4 系统关,app关
    this.permissions = await this.getPermissions();
    if ([3, 4].includes(this.permissions?.gps)) {
      // 系统权限未开启
      this.showSysPermissionModal();
    } else if ([2].includes(this.permissions?.gps)) {
      // app权限未开启
      this.showAppPermissionModal();
    } else {
      this.$store.commit('updateLocalPre', {
        page: 'navigation',
        localPre: true,
        isDefaultGPS: false,
      });
    }
  },
  methods: {
    // 权限-显示系统定位权限弹窗
    async showSysPermissionModal() {
      uni.showModal({
        title: '定位服务未开启,无法根据您的位置信息为您服务,如不开启则为您选择默认城市服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            this.sysModalStatus = 'confirm';
            // 跳去系统设置界面
            if (this.osName == 'ios') {
              this.toSysSetting();
            } else {
              this.toSysSetting('locsyssetting');
            }
          } else {
            this.sysModalStatus = 'cancel';
            // ios无法检测系统关,app开的情况,不做处理
            if (this.osName == 'android') {
              if (this.permissions?.gps == 3) {
                this.$store.commit('updateLocalPre', {
                  page: 'navigation',
                  localPre: true,
                  isDefaultGPS: true,
                });
              } else if (this.permissions?.gps == 4) {
                // 系统关,app关 显示app设置提示弹窗
                this.showAppPermissionModal();
              }
            }
          }
        },
      });
    },
    // 权限-显示app定位权限弹窗
    async showAppPermissionModal() {
      uni.showModal({
        title: '位置权限未开启,无法根据您的位置信息获取您附近的加油站网点信息为您服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            // 跳去app设置界面
            this.toSysSetting();
          }
          // 界面显示无位置信息状态
        },
      });
    },
    // 权限-跳转系统设置页面
    async toSysSetting(url = 'setting') {
      const res = await uni.$petro.Bridge.zyzx.launchUrl({
        url: url,
      });
    },
    // 权限-获取权限
    getPermissions() {
      if (my.isIDE) {
        return {
          gps: 1,
        };
      }
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera', 'gps'],
        });
        resolve(res);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
    overflow: hidden;
  }
}
.page-navigation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .station-box {
    flex: 1;
    overflow: hidden;
    border-radius: 32rpx 32rpx 0rpx 0rpx;
  }
}
</style>
