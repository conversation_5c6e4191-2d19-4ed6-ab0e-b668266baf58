// [司机已绑定车辆查询（我的车辆） /user/driver/bindCarList]
export function getCarListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 7,
      totalPage: 4,
      rows: [
        {
          driver: 'aaaa',
          carNumber: '京A 88K09',
          companyName: 'bbbbl',
          refuelType: '柴油',
          refuelModel: '#-10',
          vehicleType: '货车',
          drivingLicenseUrl: 'aaaa.url',
        },
        {
          driver: 'bbbb',
          carNumber: '京A 88K08',
          companyName: 'bbbbl',
          refuelType: '柴油',
          refuelModel: '#-10',
          vehicleType: '客车',
          drivingLicenseUrl: 'aaaa.url',
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('user.driver.bindCarList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}