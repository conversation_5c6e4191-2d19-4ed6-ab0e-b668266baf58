<script>
export default {
  name: 'zyzx-page-unit-setting',
  components: {},
  props: {
    // 是否是会员小程序进入
    isInit: {
      type: Boolean,
      default: false,
    },
    // 是否是企业开通/关联
    isRelate: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      isLoading: true,
      userInfo: {},
      tabs: [
        [
          // { id: 'firstList', value: '单位卡' },
          { id: 'secondList', value: '司机卡' },
        ],
        [
          { id: 'firstList', value: '单位卡开通' },
          { id: 'secondList', value: '单位卡关联' },
        ],
      ],
      tabIndex: 0, // tab索引
      currentTab: 'secondList', // 当前tab
      firstList: [], // 第一个列表
      secondList: [], // 第二个列表
      setItem: null, // 当前设置项
      pickerVisable: false, // 开票类型选择框是否显示
      // 开票类型选择列表
      pickerList: [
        { id: 1, label: '消费地消费开普通发票' },
        // { id: 2, label: '充值后消费地开普通发票' },
        { id: 3, label: '开户地消费开增值税专用发票' },
        { id: 4, label: '开户地充值开普通发票' },
        { id: 5, label: '开户地消费开普通发票' },
        // { id: 6, label: '开户地开增值税专用发票' },
        // { id: 7, label: '消费地开普通发票' },
        { id: 8, label: '消费地开增值税专用发票' },
      ],
      pickerSelect: [0], // 选择项
      selectList: [], // 选中的待激活列表
      authenticationInfo: null, // 初始化认证结果
      saveInfo: {}, // 登录信息
      queryType: 4, // 查询类型:0-企业账户已激活 1-企业账户待开通 2-司机账户已激活 3-企业账户关联 4-所有待激活账户
    };
  },
  async mounted() {
    try {
      this.getUserInfo();
      this.saveInfo = await uni.$petro.getTokenInfo();

      console.log(this.isInit, 'isRelate', this.isRelate, 'saveInfo', this.saveInfo);
      if (this.isRelate) {
        this.tabIndex = 1;
        this.queryType = 1;
        this.getUserEnterprise(this.queryType);
        this.getUserEnterprise(3);
      } else {
        this.tabIndex = 0;
        this.queryType = 4;
        this.getUserEnterprise(this.queryType);
      }
      if (this.isInit) {
        // 账户SDK注册
        await uni.$petro.setTokenInfo({
          memberNo: this.saveInfo?.memberNo,
          gsmsToken: this.saveInfo?.gsmsToken,
        });
      }
    } catch (e) {
      console.log('mounted', e);
      this.isLoading = false;
    } finally {
      this.isLoading = false;
    }
  },
  methods: {
    // 获取企业列表
    async getUserEnterprise(queryType = 4, isReget = false) {
      try {
        const res = await uni.$petro.http(
          'user.business.queryEnterpriseBusinessList.h5',
          {
            queryType,
            ...this.data,
          },
          { showLoading: false },
        );
        console.log('getUserEnterprise', res);
        this.$refs.dataList.loadStatus = 'nomore';

        let resultList = res?.data || [];
        let firstList = [];
        let secondList = [];
        if (queryType === 1) {
          // 企业账户待开通
          firstList = resultList.reduce((list, item) => {
            item.invoiceName = this.pickerList.find(el => el.id == item.invoiceType)?.label || '';
            item.key = `${item.businessNo}_${item.role}_${item.businessType}`;
            list.push(item);
            return list;
          }, []);
          if (!isReget) {
            this.firstList = firstList;
          } else {
            this.firstList.forEach(item => {
              firstList.forEach(el => {
                if (el.key === item.key) {
                  item.mainAccountNo = el.mainAccountNo;
                  item.enterpriseAccountNo = el.enterpriseAccountNo;
                }
              });
            });
            this.selectList = this.firstList.filter(item => item.isSelected);
          }
        } else if (queryType === 3) {
          // 企业账户关联
          this.secondList = resultList.reduce((list, item) => {
            item.key = `${item.businessNo}_${item.role}_${item.businessType}`;
            list.push(item);
            return list;
          }, []);
        } else {
          // 所有待激活账户
          firstList = resultList.reduce((list, item) => {
            if (item.role === 2) {
              item.key = `${item.businessNo}_${item.role}_${item.businessType}`;
              list.push(item);
            }
            return list;
          }, []);
          secondList = resultList.reduce((list, item) => {
            if (item.role === 4) {
              item.key = `${item.businessNo}_${item.role}_${item.businessType}`;
              list.push(item);
            }
            return list;
          }, []);

          if (!isReget) {
            this.firstList = firstList;
            this.secondList = secondList;
          } else {
            if (this.currentTab === 'firstList') {
              this.firstList.forEach(item => {
                firstList.forEach(el => {
                  if (el.key === item.key) {
                    item.mainAccountNo = el.mainAccountNo;
                    item.enterpriseAccountNo = el.enterpriseAccountNo;
                  }
                });
              });
              this.selectList = this.firstList.filter(item => item.isSelected);
            } else {
              this.secondList.forEach(item => {
                secondList.forEach(el => {
                  if (el.key === item.key) {
                    item.mainAccountNo = el.mainAccountNo;
                    item.enterpriseAccountNo = el.enterpriseAccountNo;
                  }
                });
              });
              this.selectList = this.secondList.filter(item => item.isSelected);
            }
          }
        }
      } catch (e) {
        this.isLoading = false;
      }
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await uni.$petro.http(
          'user.getBasicInfo.h5',
          {
            type: 1, // 1-不脱敏信息 2-脱敏信息
          },
          { showLoading: false },
        );
        console.log('getUserInfo', res);
        this.userInfo = res?.data || {};
      } catch (e) {
        console.log(e);
      }
    },
    // tab切换
    handleTabChange(id) {
      this.currentTab = id;
      if (this.isRelate) {
        this.queryType = this.currentTab === 'firstList' ? 1 : 3;
      }
    },
    // 选择待激活数据
    onSelected(item) {
      if (this.isRelate && this.currentTab === 'secondList') {
        this.secondList = this.secondList.map(el => {
          el.isSelected = item.key === el.key;
          return el;
        });
      } else {
        let currentList = this[this.currentTab];
        let index = currentList.findIndex(el => el.key === item.key);

        let newData = JSON.parse(JSON.stringify(item));
        newData.isSelected = !newData.isSelected;
        this.$set(this[this.currentTab], index, newData);
      }
    },
    // 选择开票类型
    onSelectInvoiceType(item) {
      let index = this.pickerList.findIndex(el => el.id === item.invoiceType);

      this.setItem = item;
      this.pickerSelect = [index === -1 ? 0 : index];
      this.pickerVisable = true;
    },
    // 选择器值改变
    onPickerChange(e) {
      this.pickerSelect = e.detail.value;
    },
    // 确认选择
    onPickerConfirm() {
      let currentList = this[this.currentTab];
      let index = currentList.findIndex(el => el.key === this.setItem.key);

      let newData = JSON.parse(JSON.stringify(currentList[index]));
      newData.invoiceType = this.pickerList[this.pickerSelect].id;
      newData.invoiceName = this.pickerList[this.pickerSelect].label;

      this.$set(this[this.currentTab], index, newData);
      this.pickerVisable = false;
    },
    // 确认
    async onConfirm() {
      let currentList = this[this.currentTab];
      let selectList = currentList.filter(item => item.isSelected);
      if (!selectList.length)
        return uni.showToast({ title: this.isRelate && this.currentTab === 'secondList' ? '请选择要关联的单位' : '请选择要开通的单位' });

      // 管理员账户必须选择开票类型
      if (this.currentTab === 'firstList') {
        let index = selectList.filter(item => [10, 6].includes(item.businessType)).findIndex(item => !item.invoiceType);
        if (index !== -1) return uni.showToast({ title: '请选择单位' + selectList[index].enterpriseName + '的开票类型' });
      }

      this.selectList = selectList;
      if (this.isRelate && this.currentTab === 'secondList') {
        uni.$petro.showModal({
          title: '关联确认',
          content: '关联后，多级车队管理人间可进行资金及权益的协同管理，是否确认关联？',
          showCancel: true,
          confirmText: '确认关联',
          cancelText: '暂不关联',
          success: async () => {
            // 关联账户
            await this.onRelateAccount();
          },
          cancel: () => {
            console.log('用户点击取消');
          },
        });
      } else {
        // 开通
        await this.onGetMetaInfo();
      }
    },
    // 关联账户
    async onRelateAccount() {
      try {
        const res = await uni.$petro.http('user.business.enterpriseAffiliation.h5', {
          id: this.selectList[0].id,
        });
        if (!res?.success) return;
        uni.showToast({ content: '关联成功' });
        await this.getUserEnterprise(this.queryType);
        this.$emit('success', this.selectList);
      } catch (error) {
        console.log(error);
      }
    },
    // 获取人脸识别元信息
    async onGetMetaInfo() {
      try {
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'getMetaInfo',
          data: {},
        });
        console.log('onGetMetaInfo', res);

        this.initAuthentication(res.data.metaInfo);
      } catch (e) {}
    },
    // 初始化人脸认证
    async initAuthentication(metaInfo) {
      /**
       * metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
       * areaCode 认证地市编码 例如：320100
       * roleBusinessList 业务角色列表
       *  - orgCode 单位编号
       *  - staffNo 员工编号
       *  - businessNo 业务编号
       *  - userRole 角色编码:1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
       *  - enterpriseAccountNo 单位账户编号 (司机人脸的时候必传)
       */
      let params = {
        metaInfo: metaInfo,
        areaCode: '510100',
        roleBusinessList: [],
      };

      this.selectList.forEach(item => {
        params.roleBusinessList.push({
          orgCode: item.enterpriseNo,
          staffNo: item.staffNo,
          businessNo: item.businessNo,
          userRole: this.currentTab === 'firstList' ? 2 : 4, // 车队业务管理员-2 司机-4
          enterpriseAccountNo: item.enterpriseAccountNo || item.parentMainAccountNo,
        });
      });
      try {
        const res = await uni.$petro.http('user.memberInfo.initRealPersonIdentify.h5', params);
        console.log('res', res);

        if (!res?.data) return uni.showToast({ content: res.message || '初始化失败' });

        this.authenticationInfo = res.data;
        this.faceVerify();
      } catch (e) {}
    },
    // 人脸识别
    async faceVerify() {
      try {
        // 获取人脸识别凭证
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'verify',
          data: {
            certifyId: this.authenticationInfo.certifyId,
          },
        });
        console.log('faceVerify', res);

        this.realPersonAuthentication();
      } catch (e) {}
    },
    // 实人认证
    async realPersonAuthentication() {
      try {
        /**
         * type 实人认证场景： 1—业务资料审核实人; 2—管理员激活企业; 3—司机首次注册; 4—扫一扫实人;
         * verifyNo 身份认证唯一标识(初始化实人认证接口返回)；
         * certifyId 实人认证三方系统的标识(初始化实人认证接口返回)。
         * roleBusinessList 业务角色列表
         *  - orgCode 单位编号
         *  - staffNo 员工编号
         *  - businessNo 业务编号
         *  - userRole 角色编码: 1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
         */
        const res = await uni.$petro.http('user.memberInfo.realPersonIdentify.h5', {
          type: '1',
          verifyNo: this.authenticationInfo.verifyNo,
          certifyId: this.authenticationInfo.certifyId,
          roleBusinessList: this.authenticationInfo.roleBusinessList,
        });
        console.log('res', res);
        if (!res?.success || !res?.data?.authInfo) return uni.showToast({ content: res.message || '实人认证失败' });

        if (!this.userInfo?.identityNo) {
          // 无身份证号码
          await this.getUserInfo();
        }

        // 判断是否有没开户的数据
        let index = this.selectList.findIndex(item => !item.mainAccountNo || !item.enterpriseAccountNo);
        if (index !== -1) {
          if (this.isRelate && this.currentTab === 'firstList') {
            if (!(await this.openSubEnterprise())) return uni.showToast({ content: '开户失败' });
          }
          await this.getUserEnterprise(this.queryType, true);
        }

        this.onActive();
      } catch (e) {}
    },
    // 开通下级企业
    async openSubEnterprise() {
      return new Promise(async resolve => {
        try {
          let params = {
            openSubEnterpriseList: [],
          };
          this.selectList.forEach(item => {
            params.openSubEnterpriseList.push({
              id: item.id,
              inviterUnitMainAccountNo: item.parentMainAccountNo,
              unitAlias: item.unitAlias,
              enterpriseNo: item.enterpriseNo,
            });
          });
          const res = await uni.$petro.http('account.manager.openSubEnterprise.h5', params);
          resolve(res?.success);
        } catch (e) {
          resolve(false);
        }
      });
    },
    // 激活
    async onActive() {
      uni.$petro.showLoading();
      try {
        let activation = [];
        this.selectList.forEach(item => {
          activation.push({
            invitationRecordNo: item.id || '',
            unitMemberNo: item.enterpriseNo || '',
            businessNo: item.businessNo || '',
            staffNo: item.staffNo,
            invoiceType: item.invoiceType,
            mainAccountNo: item.mainAccountNo,
            unitMainAccountNo: item.enterpriseAccountNo,
          });
        });

        await uni.$petro.AccountPlugin?.securityPluginInstance
          ?.activateUser(
            '4', // 'staffRole',
            this.userInfo.identityNo,
            this.authenticationInfo.verifyNo,
            activation,
            uni.$petro.AccountPlugin?.ref,
            pwd => {
              uni.$petro.hideLoading();
              console.log('>>>>>>>>on keyboard:', pwd);
            },
          )
          .then(res => {
            uni.$petro.hideLoading();
            console.log('activate user:', res);
            if (res?.code === '0') return this.activeSuccess();
            // 不抛错-键盘取消:P_SDK07_200001
            if (res?.code.indexOf('P_SDK07_200001') === -1) {
              uni.showModal({
                title: res?.msg || '异常',
                confirmText: '确认',
                showCancel: false,
              });
            }
          })
          .catch(error => {
            uni.$petro.hideLoading();
            console.error('activate user:', error);
            // 不抛错-键盘取消:P_SDK07_200001
            if (error?.code.indexOf('P_SDK07_200001') === -1) {
              uni.showModal({
                title: error?.msg || '异常',
                confirmText: '确认',
                showCancel: false,
              });
            }
          });
      } catch (e) {
        console.log(e);
      } finally {
        uni.$petro.hideLoading();
      }
    },
    // 激活成功
    async activeSuccess() {
      uni.showToast({ content: '激活成功' });
      this.$emit('success', this.selectList);
    },
    // 切换企业和角色
    async switchCompanyAndRole() {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: this.selectList[0].enterpriseNo,
          type: 1,
          roleType: this.selectList[0].role || '4',
          roleBusinessType: this.selectList[0].businessType || '11',
          businessNo: this.selectList[0].businessNo,
        });
        console.log('switchCompanyAndRole', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
      } catch (e) {
        console.log(e);
        uni.$petro.hideLoading();
      }
    },
    // 页面跳转
    turnPage() {
      setTimeout(async () => {
        uni.$petro.hideLoading();
        if (this.currentTab === 'firstList') {
          if (this.selectList[0].businessType === 6) {
            // 非油直销业务
            this.toDirectsale();
          } else if (this.selectList[0].businessType === 11) {
            // 礼品卡业务
            uni.$petro.route({ url: '/pages/gift-code/gift-code', type: 'reLaunch' });
          } else {
            // 车队卡业务
            this.toManager();
          }
        } else {
          // 司机
          this.toDriver();
        }
      }, 500);
    },
    // 获取车队业务类型
    async getFleetType() {
      try {
        const res = await uni.$petro.http('user.business.getFleetType.h5', {
          businessNo: this.selectList[0].businessNo,
        });
        console.log('getFleetType', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = Object.assign(this.saveInfo, res.data);
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转非油直销
    toDirectsale() {
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-6' });
      }, 500);
    },
    // 跳转管理端
    async toManager() {
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-10' });
      }, 500);
    },
    // 跳转司机端
    async toDriver() {
      await this.getFleetType();
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '4-10' });
      }, 500);
    },
    // 下拉刷新触发
    async refreshPullDown() {
      console.log('下拉刷新触发....');
      this.$refs.dataList.loadStatus = 'loading';
      await this.getUserEnterprise(this.queryType);
      this.$refs.dataList.stopRefresh();
    },
  },
};
</script>

<template>
  <div class="zyzx-page-unit-setting">
    <view class="loading" v-show="isLoading">
      <img class="loading-img" src="./images/icon-loading.gif" />
      <view class="loading-value">正在努力加载中...</view>
    </view>
    <img class="bg" src="./images/bg-header.png" />
    <view class="content">
      <view class="title">请设置你的昆仑e享卡</view>
      <view class="subTitle">
        {{ !!userInfo.phone ? userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '' }}
        {{
          isRelate
            ? '存在新的多级单位关系，请激活你的昆仑e享卡，激活后可进入对应的单位进行管理。'
            : '已在以下单位开通了业务，请激活你的昆仑e享卡，激活后可进入对应的单位进行管理。'
        }}
      </view>
      <view class="tab">
        <view
          class="tab-item"
          :class="{ active: currentTab === item.id }"
          v-for="item in tabs[tabIndex]"
          :key="item.id"
          @click="handleTabChange(item.id)"
        >
          {{ item.value }}
        </view>
      </view>
      <zyzx-data-list ref="dataList" @refreshPullDown="refreshPullDown">
        <div class="list">
          <block v-if="currentTab === 'firstList'">
            <div v-if="firstList && firstList.length">
              <div v-for="item in firstList" :key="item.key">
                <div class="list-header" v-if="isRelate">
                  <div class="list-header-title">{{ item.parentEnterpriseName }}的{{ item.parentContactName }}</div>
                  <div class="list-header-subTitle">邀请你成为下级车队管理人</div>
                </div>
                <div class="list-item" :class="{ active: item.isSelected }" @click="onSelected(item)">
                  <div class="list-item-content">
                    <u-avatar
                      :text="item.enterpriseName ? item.enterpriseName.slice(0, 1) : ''"
                      size="40"
                      fontSize="18"
                      randomBgColor
                    ></u-avatar>
                    <view class="list-item-content-value">
                      <div class="list-item-content-value-name">{{ item.enterpriseName }} </div>
                      <div class="list-item-content-value-alias">{{ item.unitAlias }}</div>
                      <div class="list-item-content-value-role">{{
                        item.businessType === 10 ? '车队管理人' : item.businessType === 6 ? '采购业务管理人' : '礼品卡券管理人'
                      }}</div>
                    </view>
                  </div>
                  <div
                    class="list-setting"
                    @click.stop="onSelectInvoiceType(item)"
                    v-show="item.isSelected && [10, 6].includes(item.businessType) && !isRelate"
                  >
                    <div class="list-setting-title">开票类型</div>
                    <div class="list-setting-value">{{ item.invoiceName || '请选择开票类型' }}</div>
                    <div class="list-setting-right"></div>
                  </div>
                  <div class="list-setting" v-show="isRelate">
                    <div class="list-setting-title">开票类型</div>
                    <div class="list-setting-value">{{ item.invoiceName }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="noData" v-else>
              <img class="noData-img" src="./images/icon-naData.png" />
              <div class="noData-value">{{ isRelate ? '暂无待开通的单位卡' : '暂无待激活的单位卡' }}</div>
            </div>
          </block>
          <block v-else>
            <div v-if="secondList && secondList.length">
              <div v-for="item in secondList" :key="item.key">
                <div class="list-header" v-if="isRelate">
                  <div class="list-header-title">{{ item.parentEnterpriseName }}的{{ item.parentContactName }}</div>
                  <div class="list-header-subTitle">关联你为下级单位联系人</div>
                </div>
                <div class="list-item" :class="{ active: item.isSelected }" @click="onSelected(item)">
                  <div class="list-item-content">
                    <u-avatar
                      :text="item.enterpriseName ? item.enterpriseName.slice(0, 1) : ''"
                      size="40"
                      fontSize="18"
                      randomBgColor
                    ></u-avatar>
                    <view class="list-item-content-value">
                      <div class="list-item-content-value-name">{{ item.enterpriseName }}</div>
                      <div class="list-item-content-value-alias">{{ item.unitAlias }}</div>
                      <div class="list-item-content-value-role">{{ isRelate ? '车队管理人' : '车队司机' }}</div>
                    </view>
                  </div>
                </div>
              </div>
            </div>
            <div class="noData" v-else>
              <img class="noData-img" src="./images/icon-naData.png" />
              <div class="noData-value">{{ isRelate ? '暂无待关联的单位卡' : '暂无待激活司机卡' }}</div>
            </div>
          </block>
        </div>
      </zyzx-data-list>
    </view>
    <view class="footer">
      <button @click="onConfirm()">确定</button>
    </view>

    <u-popup :show="pickerVisable" mode="bottom" :round="10" :closeOnClickOverlay="false">
      <view class="picker">
        <view class="picker-header">
          <view class="picker-header-value">选择开票类型</view>
          <img src="./images/icon-close.png" class="picker-header-icon" @click="pickerVisable = false" />
        </view>
        <view class="picker-content">
          <picker-view :value="pickerSelect" @change="onPickerChange">
            <picker-view-column>
              <view class="picker-item" v-for="item in pickerList" :key="item.id">
                {{ item.label }}
              </view>
            </picker-view-column>
          </picker-view>
        </view>
        <view class="picker-footer">
          <button class="plain-btn" @click="onPickerConfirm()">确定</button>
        </view>
      </view>
    </u-popup>

    <petro-layout ref="layout" :petroKeyboard="true"></petro-layout>
  </div>
</template>

<style scoped lang="scss">
.zyzx-page-unit-setting {
  width: 100%;
  min-height: 100vh;
  padding: 252rpx 0 36rpx;
  box-sizing: border-box;

  .loading {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background: #ffffff;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 998;

    &-img {
      width: 560rpx;
      height: 560rpx;
    }

    &-value {
      margin-top: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 44rpx;
    }
  }

  .bg {
    width: 100%;
    height: 372rpx;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
  }

  .content {
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;
    position: relative;
    z-index: 2;
  }

  .title {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 44rpx;
  }

  .subTitle {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #666666;
    line-height: 44rpx;
    margin-top: 10rpx;
  }

  .tab {
    display: flex;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 30rpx;
    color: #666;
    line-height: 44rpx;

    &-item {
      padding: 20rpx;
    }

    .active {
      color: #000000;
      position: relative;

      &::after {
        position: absolute;
        content: '';
        width: 50%;
        height: 4rpx;
        background: #fa1919;
        bottom: 0;
        left: 25%;
      }
    }
  }

  .list {
    min-height: 528rpx;

    &-header {
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 44rpx;
      margin-top: 32rpx;

      &-subTitle {
        color: #666666;
      }
    }

    &-item {
      min-height: 138rpx;
      padding: 0 32rpx;
      border-radius: 16rpx;
      border: 1rpx solid #eeeeee;
      margin-top: 24rpx;

      &-content {
        display: flex;
        align-items: center;
        padding: 28rpx 0;

        &-icon {
          width: 80rpx;
          height: 80rpx;
          background-color: #09f;
          border-radius: 50%;
          text-align: center;
          line-height: 80rpx;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 36rpx;
          color: #ffffff;
        }

        &-value {
          flex: 1;
          margin-left: 24rpx;
          overflow: hidden;

          &-name {
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
            line-height: 38rpx;
            overflow: hidden;
            display: flex;
            align-items: center;

            &-value {
              flex: 1;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            &-icon {
              width: 32rpx;
              height: 32rpx;
              margin-left: 8rpx;
            }
          }

          &-alias {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 20rpx;
            color: #999999;
            line-height: 23rpx;
            margin-top: 4rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }

          &-role {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 28rpx;
            margin-top: 4rpx;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .active {
      border: 2rpx solid #fa1919;
    }

    &-setting {
      padding: 16rpx 0 28rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0px -1rpx 0px 0px #eeeeee;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 33rpx;
      overflow: hidden;

      &-value {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-left: 10rpx;
        text-align: right;
      }

      &-right {
        width: 12rpx;
        height: 12rpx;
        margin: 0 10rpx;
        border-top: 3rpx solid #999;
        border-right: 3rpx solid #999;
        transform: rotate(45deg);
      }
    }

    .noData {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      &-img {
        width: 560rpx;
        height: 436rpx;
      }

      &-value {
        margin-top: 48rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 44rpx;
      }
    }
  }

  // 页面底部
  .footer {
    width: 100%;
    height: 180rpx;
    padding: 28rpx 48rpx 60rpx;
    background: #ffffff;
    box-sizing: border-box;
    position: fixed;
    bottom: 0;
  }

  // 按钮
  button {
    box-sizing: border-box;
    height: 92rpx;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    line-height: 92rpx;
    border-radius: 46rpx;
    border: none;
    background: #fa1919;
    color: #ffffff;
    flex: 1;

    &:active {
      opacity: 0.8;
    }
  }

  // 镂空按钮
  .plain-btn {
    border: 1rpx solid #eeeeee;
    background: #ffffff;
    color: #333333;
  }

  // 选择器
  .picker {
    width: 100%;
    max-height: 76vh;
    padding: 48rpx 40rpx 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    &-header {
      height: 50rpx;
      margin-bottom: 40rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 50rpx;
      position: relative;
      text-align: center;

      &-icon {
        width: 40rpx;
        height: 40rpx;
        position: absolute;
        top: 5rpx;
        right: 0;
      }
    }

    &-content {
      flex: 1;
    }

    &-item {
      height: 96rpx !important;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 32rpx;
      color: #333333 !important;
      line-height: 96rpx;
    }

    &-footer {
      width: 100%;
      height: 148rpx;
      padding: 28rpx 6rpx;
      box-sizing: border-box;
    }
  }
}
</style>
