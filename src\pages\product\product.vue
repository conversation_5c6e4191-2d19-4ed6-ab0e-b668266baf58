<template>
  <view class="page-product">
    <u-navbar :title="' '" :autoBack="true" :placeholder="false" :bgColor="'transparent'"></u-navbar>
    <petro-layout ref="layout">
      <zyzx-page-product @onSelected="onOpenRole" @onScan="onScan"></zyzx-page-product>
    </petro-layout>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      selected: null,
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
  },
  methods: {
    // 开通业务
    onOpenRole(v) {
      console.log(v);
      uni.$petro.route({ url: '/pages/roles/roles', params: { isInit: 1 }, type: 'navigateTo' });
    },
    // 扫码
    onScan(res) {
      if (res?.success && res?.type === 'other') {
        console.log('onScan', res?.data?.authInfo);
        uni.showModal({
          title: '温馨提示',
          content: '实人认证成功',
          showCancel: false,
          success: res => {
            if (res.confirm) {
              this.getUserEnterprise();
            }
          },
        });
      }
    },
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const tokenInfo = await uni.$petro.getTokenInfo();
        if (tokenInfo.orgCode !== '9999') return;
        const res = await uni.$petro.http('user.business.queryEnterpriseBusinessList.h5', {
          queryType: 0,
        });
        console.log('res', res);

        let managerList = res?.data || []; // 企业列表

        if (managerList?.length) {
          await this.switchCompany(managerList[0].enterpriseNo);
        }
      } catch (e) {}
    },
    // 切换企业
    async switchCompany() {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: this.selectList[0].enterpriseNo,
        });
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
      } catch (e) {
        uni.$petro.hideLoading();
      } finally {
        uni.$petro.hideLoading();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-product {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
}

.test {
  margin-top: 20rpx;
  border: 1rpx solid;
  padding: 20rpx 0;
}
</style>
