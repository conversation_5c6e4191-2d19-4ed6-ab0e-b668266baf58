<template>
  <view class="face-recognition-page">
    <div class="header-layout" :class="{ 'is-ide': isIDE }">
      <u-navbar title="身份校验" :fixed="!isIDE" :autoBack="true" :placeholder="true">
        <view slot="right">
          <view class="btn-help" @click="onToHelp()">帮助</view>
        </view>
      </u-navbar>
    </div>

    <!-- 账单列表 -->
    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-face-recognition />
      </petro-layout>
    </div>
  </view>
</template>

<script>
import ZyzxPageFaceRecognition from '@/components/zyzx-page-face-recognition/zyzx-page-face-recognition.vue';

export default {
  name: 'face-recognition-page',
  components: { ZyzxPageFaceRecognition },
  data() {
    return {};
  },
  computed: {
    isIDE() {
      return my.isIDE;
    },
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * 跳转到帮助页面
     */
    onToHelp() {
      uni.$petro.route({ url: '/pages/help/help', type: 'navigateTo' });
    },
  },
};
</script>

<style scoped lang="scss">
.face-recognition-page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;

  .header-layout {
    &.is-ide {
      padding-right: calc(240rpx + 24rpx);
    }
  }
}

.container {
  flex: 1;
  overflow: scroll;
}
</style>
