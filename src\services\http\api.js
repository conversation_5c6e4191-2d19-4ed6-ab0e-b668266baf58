// 获取待开通已开通/企业列表
export function getCompanyListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      onEntpList: [
        {
          enterpriseName: '企业名称1',
          entpNickName: '企业别名1',
          staffRole: 1,
        },
        {
          enterpriseName: '企业名称2',
          entpNickName: '企业别名2',
          staffRole: 1,
        },
      ],
      offEntpList: [
        {
          enterpriseName: '企业名称1',
          entpNickName: '企业别名2',
          staffRole: 1,
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('user.driver.queryCompanyByDriver.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [用户企业切换/user/companySwitch]
export function companySwitchApi(data, config) {
  const mockData = {
    message: '成功',
    errorCode: '0',
    success: true,
    data: {},
  };
  return uni.$petro.http('user.companySwitch.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 开通企业账户
export function openCompanyApi(data, config) {
  const mockData = {
    success: true,
    data: null,
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('account.driver.activateCompany.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
