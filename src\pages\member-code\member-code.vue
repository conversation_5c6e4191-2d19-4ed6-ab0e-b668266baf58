<script>
import { mapState } from 'vuex';
import oilCode from '@/components/page-refuel-oil/components/oil-code.vue';

export default {
  name: 'member-code',
  components: {
    oilCode,
  },
  data() {
    return {
      query: {},
      systemInfo: uni.$petro.store?.systemInfo,
      pageShow: 0,
    };
  },
  onLoad(query) {
    this.query = query;
    try {
      if (this.query?.data) this.orderInfo = JSON.parse(this.query?.data);
    } catch (err) {
      console.error(err);
    }
  },
  onShow() {
    this.pageShow++;
    console.log('member-code---show', this.pageShow);
  },
  async mounted() {
    uni.onAppShow(async res => {
      this.permissions = await this.getPermissions();
      // 系统和app都有权限, 并且当前油站列表没有数据, 获取油站列表
      if (this.permissions?.gps == 1 && !this.stationList?.length) {
        this.$store.commit('updateLocalPre', {
          page: 'oil',
          localPre: true,
          isDefaultGPS: false,
        });
      }

      // zk要求: 安卓流程,用户打开app后无权限，弹出系统权限弹窗点击去设置后再回来app,需要再次判断弹出app权限弹窗,只弹一次
      if (this.osName == 'android' && this.sysModalStatus === 'confirm' && [2, 4].includes(this.permissions?.gps)) {
        this.sysModalStatus = '';
        this.showAppPermissionModal();
      }
    });

    // 权限判断
    // 1 系统开,app开
    // 2 系统开,app关
    // 3 系统关,app开
    // 4 系统关,app关
    this.permissions = await this.getPermissions();
    if ([3, 4].includes(this.permissions?.gps)) {
      // 系统权限未开启
      this.showSysPermissionModal();
    } else if ([2].includes(this.permissions?.gps)) {
      // app权限未开启
      this.showAppPermissionModal();
    } else {
      this.$store.commit('updateLocalPre', {
        page: 'oil',
        localPre: true,
        isDefaultGPS: false,
      });
    }
  },
  methods: {
    // 权限-显示系统定位权限弹窗
    async showSysPermissionModal() {
      uni.showModal({
        title: '定位服务未开启,无法根据您的位置信息为您服务,如不开启则为您选择默认城市服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            this.sysModalStatus = 'confirm';
            // 跳去系统设置界面
            if (this.osName == 'ios') {
              this.toSysSetting();
            } else {
              this.toSysSetting('locsyssetting');
            }
          } else {
            this.sysModalStatus = 'cancel';
            // ios无法检测系统关,app开的情况,不做处理
            if (this.osName == 'android') {
              if (this.permissions?.gps == 3) {
                this.$store.commit('updateLocalPre', {
                  page: 'oil',
                  localPre: true,
                  isDefaultGPS: true,
                });
              } else if (this.permissions?.gps == 4) {
                // 系统关,app关 显示app设置提示弹窗
                this.showAppPermissionModal();
              }
            }
          }
        },
      });
    },
    // 权限-显示app定位权限弹窗
    async showAppPermissionModal() {
      uni.showModal({
        title: '位置权限未开启,无法根据您的位置信息获取您附近的加油站网点信息为您服务',
        content: '是否前去开启',
        showCancel: true,
        success: async modalRes => {
          if (modalRes.confirm) {
            // 跳去app设置界面
            this.toSysSetting();
          }
          // 界面显示无位置信息状态
        },
      });
    },
    // 权限-跳转系统设置页面
    async toSysSetting(url = 'setting') {
      const res = await uni.$petro.Bridge.zyzx.launchUrl({
        url: url,
      });
    },
    // 权限-获取权限
    getPermissions() {
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera', 'gps'],
        });
        resolve(res);
      });
    },
  },
};
</script>

<template>
  <div class="page-member-code">
    <u-navbar :title="' '" :autoBack="true" :placeholder="false" :bgColor="'transparent'"></u-navbar>
    <div class="page-content">
      <div class="header-placeholder" :style="{ height: systemInfo.statusBarHeight + systemInfo.titleBarHeight }"></div>
      <div class="code-content">
        <oil-code :pageShow="pageShow"></oil-code>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-member-code {
  height: 100vh;
}
.page-content {
  background: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('@/static/images/code-oil-bg.png') no-repeat;
  background-size: 100% 100%;
}
.code-content {
  padding-top: 40rpx;
}
</style>
