// [我的钱包信息 /account/manager/getDetailAccountInfo]
export function getWalletInfoApi(data, config) {
  const mockData = {
    success: true,
    data: {
      organizationName: 'aaa',
      managerName: 'bbb',
      cardNumber: '1111111',
      location: 'beijing',
      consumptionType: '消费类型',
      totalAssets: '872222',
      coupon: '86',
      points: '85',
      oilCoins: '84',
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('account.manager.getDetailAccountInfo.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [单位司机查询账户钱包列表 /account/driver/queryWalletList] 暂未用到
export function getWalletListApi(data, config) {
  const mockData = {
    success: true,
    data: [
      {
        enterpriseAccountNo: '',
        mainAccountNo: '',
        enterpriseNo: '',
        userId: '123',
        userName: 'testName',
        licencePlate: '川A23324',
        accountType: '2',
        unitAlias: '单位别名',
        businessNo: '*********',
        businessType: 0,
        invoiceType: 0,
        accountPlace: '',
        accountPlaceName: '',
        serialNo: 0,
        walletAccount: {
          cardNo: '**************',
          accountNo: '1231312',
          frozenAmount: 0.0,
          availableAmount: 120.0,
          accountStatus: 0,
        },
      },
      {
        enterpriseAccountNo: '',
        mainAccountNo: '',
        enterpriseNo: '',
        userId: '1234',
        userName: 'testName2',
        licencePlate: '川A23325',
        accountType: '2',
        unitAlias: '单位别名2',
        businessNo: '*********',
        businessType: 0,
        invoiceType: 0,
        accountPlace: '',
        accountPlaceName: '',
        serialNo: 0,
        walletAccount: {
          cardNo: '**************',
          accountNo: '1231312',
          frozenAmount: 0.0,
          availableAmount: 120.0,
          accountStatus: 0,
        },
      },
    ],
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('account.driver.queryWalletList.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// [BD单位司机通过装备卡号查询主账户信息 /account/driver/queryMainAccountInfoByBDCardNo](v440)
export function getMainAccountInfoByBDCardNoApi(data, config) {
  const mockData = {
    success: true,
    data: [
      {
        bdCardNo: '**************',
        carLicense: '川A23324',
        unitAccRDto: {
          unitMainAccountNo: '**********',
          mainAccountNo: '*********',
          carLicense: '川A23324',
          unitAlias: '单位别名',
          mainAccountType: 2, // 账户类型 1:单位账户 2:司机账户 3:车牌账户 4:车牌司机账户
          accountStatus: 1, // 账户状态 1:正常 2:冻结 3:注销 6:待激活
          createTime: '2021-12-04',
          accountPlace: '常用地',
          accountPlaceName: '常用地名称',
          enterpriseStaffName: '管理员名称',
          availableAmount: '100.00',
          allocationAmount: '10.00',
          summaryAmount: '60.00',
          frozenAmount: '20.12',
        },
      },
    ],
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('account.driver.queryMainAccountInfoByBDCardNo.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
