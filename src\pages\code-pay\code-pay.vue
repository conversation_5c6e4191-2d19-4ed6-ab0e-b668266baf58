<script>
import { mapState } from 'vuex';

import { saveIndoorUnPayOrder } from '@/services/http';

export default {
  name: 'code-pay',
  computed: {
    ...mapState({
      walletInfo: state => state?.accountDriver?.driverWalletInfo,
      accountInfo: state => state.company?.accountInfo,
      userInfo: state => state?.roles?.userInfo,
      cardInfo: state => state?.accountDriver?.cardInfo,
      tokenInfo: state => state?.roles?.tokenInfo,
    }),
    realPayMoney() {
      if (!this.orderInfo?.payOrderNo || !this.orderInfo?.payTypeList?.length) return '0';
      const payInfo = this.orderInfo?.payTypeList.find(item => item.payOrderNo === this.orderInfo?.payOrderNo);
      if (!payInfo?.realPayMoney) return '0';
      // 将数字转换为字符串
      const numStr = payInfo.realPayMoney.toString();
      // 找到小数点的位置
      const decimalIndex = numStr.indexOf('.');
      if (decimalIndex === -1) {
        // 没有小数点，返回整数部分加上.00
        return numStr + '.00';
      } else {
        // 获取小数点后的部分
        const decimalPart = numStr.substring(decimalIndex + 1);
        // 如果小数点后不足两位，则补零；如果多于两位，则切割到两位
        const truncatedDecimalPart = decimalPart.length > 2 ? decimalPart.slice(0, 2) : decimalPart.padEnd(2, '0');
        // 拼接整数部分和小数部分，确保小数点后始终有两位
        return numStr.slice(0, decimalIndex + 1) + truncatedDecimalPart;
      }
    },
    // 支付类型
    payType() {
      if (!this.orderInfo?.payOrderNo || !this.orderInfo?.payTypeList?.length) return 0;
      const payInfo = this.orderInfo?.payTypeList.find(item => item.payOrderNo === this.orderInfo?.payOrderNo);
      return payInfo.payType;
    },
    // 积分
    accumulatedPoint() {
      if (!this.orderInfo?.payOrderNo || !this.orderInfo?.payTypeList?.length) return 0;
      const payInfo = this.orderInfo?.payTypeList.find(item => item.payOrderNo === this.orderInfo?.payOrderNo);
      if (!payInfo?.accumulatedPoint) return '0';
      // 将数字转换为字符串
      const numStr = payInfo.accumulatedPoint.toString();
      // 找到小数点的位置
      const decimalIndex = numStr.indexOf('.');
      if (decimalIndex === -1) {
        // 没有小数点，返回整数部分加上.00
        return numStr + '.00';
      } else {
        // 获取小数点后的部分
        const decimalPart = numStr.substring(decimalIndex + 1);
        // 如果小数点后不足两位，则补零；如果多于两位，则切割到两位
        const truncatedDecimalPart = decimalPart.length > 2 ? decimalPart.slice(0, 2) : decimalPart.padEnd(2, '0');
        // 拼接整数部分和小数部分，确保小数点后始终有两位
        return numStr.slice(0, decimalIndex + 1) + truncatedDecimalPart;
      }
    },
  },
  data() {
    return {
      query: {},
      orderInfo: {},
      loading: false,
      mileage: '',
    };
  },
  onLoad(query) {
    this.query = query;
    try {
      if (this.query?.data) this.orderInfo = JSON.parse(this.query?.data);
      console.log(this.orderInfo, 'orderInfo--');
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    // 订单支付
    async orderPay() {
      const orderInfo = this.orderInfo;
      if (!orderInfo?.orderNo) {
        uni.showModal({
          title: '温馨提示',
          content: '订单数据不存在，请重试！',
        });
        return;
      }
      try {
        this.loading = true;
        if ([2].includes(this.tokenInfo.fleetType) && this.payType != 15) {
          if (!this.mileage) return uni.showToast({ content: '请输入当前里程' });
          let bindResult = await this.saveIndoorUnPayOrder();
          if (!bindResult) return;
        }
        const res = await uni.$petro.PayPlugin.caller(
          {
            bizType: 'rposBusinessPay', // 企业确认支付扫码订单
            data: {
              businessInfo: {
                mainAccountNo: this.walletInfo?.mainAccountNo, // 主账户编号
                unitMainAccountNo: this.walletInfo?.enterpriseAccountNo, // 单位主账户编号
                businessNo: this.walletInfo?.businessNo, // 业务编码
                staffNo: this.userInfo?.staffNo, // 员工编号
                accountType: 5, // 昆仑e享卡账户
                capitalType: 1, // 金额类型 1--资金 2--信用
              },
              payOrderNo: orderInfo?.payOrderNo,
              orderNo: orderInfo?.orderNo,
              amount: orderInfo?.payMoney,
              rechargeType: orderInfo?.rechargeType,
              extInfo: {},
              headers: {},
            },
          },
          {
            ignoreErrs: true,
          },
        );
        if (res?.success) {
          uni.$petro.route(
            {
              url: '/pages/code-pay/pages/code-result',
              params: { ...res.data, payType: this.payType },
              type: 'redirectTo',
            },
            true,
          );
        } else {
          // 订单支付已超时:P_B07_708003
          if (res?.message?.indexOf('P_B07_708003') !== -1) {
            uni.showModal({
              title: '支付失败',
              content: '订单支付已超时，请重新选择支付',
              confirmText: '确认',
              showCancel: false,
              success: ss => {
                if (ss?.confirm) {
                  uni.$petro.route({
                    type: 'back',
                  });
                }
              },
            });
          } else if (res?.message?.indexOf('P_SDK07_007000') === -1) {
            // 不抛错-键盘取消:P_SDK07_007000
            uni.showModal({
              title: res?.message || '异常',
              confirmText: '确认',
              showCancel: false,
            });
          }
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    // test
    toResult() {
      // uni.$petro.route(
      //   {
      //     url: '/pages/code-pay/pages/code-result',
      //     params: {
      //       channel: '中油智行APP',
      //       payAmount: this.orderInfo?.payMoney,
      //       transTime: '2024-10-31',
      //     },
      //     type: 'redirectTo',
      //   },
      //   true,
      // );
    },
    // 室内支付待支付订单绑定BD卡
    async saveIndoorUnPayOrder() {
      return new Promise(async resolve => {
        try {
          const orderInfo = this.orderInfo;
          let { success } = await saveIndoorUnPayOrder({
            stationCode: orderInfo?.stationCode,
            mileage: this.mileage,
            orderNo: orderInfo?.orderNo,
            staffNo: this.userInfo?.staffNo,
            equipmentCardNo: this.cardInfo?.bdCardNo,
          });
          resolve(!!success);
        } catch (error) {
          console.log('saveIndoorUnPayOrder--', error);
          resolve(false);
        }
      });
    },
  },
};
</script>

<template>
  <div class="page-gift-pay">
    <u-navbar :title="'确认订单'" :autoBack="true" :placeholder="true" :bgColor="'transparent'"></u-navbar>
    <div class="page-container">
      <div class="content">
        <div class="title">待付{{ payType != 15 ? '金额' : '积分' }}</div>
        <div class="value">
          <span class="unit" v-if="payType != 15">￥</span>
          <span class="money">{{ payType != 15 ? realPayMoney : accumulatedPoint }}</span>
          <span class="unit" v-if="payType == 15">积分</span>
        </div>
      </div>
      <div class="mileage-info" v-if="[2, 3].includes(tokenInfo.fleetType)"> 当前里程（km） <input type="text" v-model="mileage" /> </div>
      <button class="btn-pay" :class="{ 'is-loading': loading }" :loading="loading" @click="orderPay()">确认支付 </button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page-gift-pay {
  height: 100vh;
  background: #f0f1f5;
  .page-container {
    padding: 20rpx 32rpx;
    text-align: center;

    .content {
      padding: 120rpx 0;
      background: #ffffff;
      border-radius: 16rpx;
      .title {
        font-size: 30rpx;
        color: #333;
        height: 42rpx;
        line-height: 35rpx;
      }

      .unit {
        font-size: 72rpx;
      }

      .money {
        margin-top: 24rpx;
        height: 120rpx;
        font-weight: bold;
        font-size: 100rpx;
        color: #333333;
        line-height: 84rpx;
      }
    }

    .mileage-info {
      display: flex;
      align-items: center;
      height: 80rpx;
      padding: 0 28rpx;
      border: 1rpx solid rgba(92, 110, 184, 0.62);
      border-radius: 16rpx;
      margin-top: 24rpx;

      input {
        flex: 1;
        text-align: right;
        box-sizing: border-box;
        background: #f0f1f5;
        height: 100%;
      }
    }

    .btn-pay {
      box-sizing: border-box;
      width: 100%;
      height: 92rpx;
      text-align: center;
      font-weight: 500;
      font-size: 32rpx;
      line-height: 92rpx;
      border-radius: 100rpx;
      background: #fa1919;
      color: #fff;
      padding: 0 120rpx;
      display: inline-block;
      margin-top: 60rpx;

      &:active {
        opacity: 0.8;
      }

      &.is-loading {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}
</style>
