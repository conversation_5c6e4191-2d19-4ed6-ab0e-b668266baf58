<template>
  <view class="page-mine">
    <u-navbar :title="'搜索'" :autoBack="false" :leftIconColor="'transparent'" :placeholder="true" :bgColor="'transparent'"></u-navbar>

    <div class="page-container">
      <petro-layout ref="layout">
        <zyzx-page-search @onSelected="onSelected" @onLogout="onLogout"></zyzx-page-search>
      </petro-layout>
    </div>
  </view>
</template>

<script>
export default {
  components: {},
  computed: {},
  data() {
    return {
      list: [],
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
  },
  onShow() {},
  methods: {
    onSelected(v) {
      console.log(v);
    },
    async onLogout(res) {
      if (!res?.confirm) {
        return;
      }
      this.$store.commit('logout');
      await uni.$petro.route(
        {
          appId: '3815480475716653', // TODO 小程序ID需要调整
          url: 'pages/index/index',
        },
        { test: 123 },
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.page-mine {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-container {
  flex: 1;
}
</style>
