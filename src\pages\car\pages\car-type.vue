<template>
  <div class="page-flex page-car-type">
    <div class="container">
      <div class="header">
        <u-navbar :title="'选择车型'" :autoBack="false" :border="true" :leftIconColor="'transparent'" :placeholder="true"
          :bgColor="'#fff'"></u-navbar>
      </div>
      <div class="scroll-view">
        <petro-scroll-view ref="scrollView" style="height: 100%" :scroll-y="true" :refresher-enabled="false"
          @refresherrefresh="onRefresh" :scrolltolower-enabled="false" :scrolltolower-bar-enabled="false"
          @scrolltolower="scrolltolower" :scroll-top-bar-enable="false">
          <u-tabs :list="typeList" lineColor="#FA1919" :activeStyle="{
            color: '#333',
            fontWeight: 'bold',
            fontSize: '32rpx',
            whiteSpace: 'nowrap',
            width: 'calc(50vw - 44rpx )',
            textAlign: 'center',
          }" :inactiveStyle="{
              color: '#333',
              fontSize: '32rpx',
              whiteSpace: 'nowrap',
              width: 'calc(50vw - 44rpx )',
              textAlign: 'center',
            }" @click="onTypeSelect" :current="1"></u-tabs>
          <div class="list">
            <div class="car-item" :class="{ active: selected.name === item.name }" v-for="(item, index) in dataList"
              :key="index" @click="onSelect(item)">
              <image class="img" :src="item.img"></image>
              <div class="name">{{ item.name }}</div>
            </div>
          </div>
          <div class="tip"> 温馨提示：错误信息可能<span class="point">导致绕路或出现违章</span>，建议选择实际车型，线路更合理 </div>
        </petro-scroll-view>
      </div>
      <div class="footer">
        <div class="btn">
          <button class="btn-nav" @click="onStartNav">开启导航</button>
        </div>
        <u-safe-bottom></u-safe-bottom>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'page-car-type',
  components: {},
  data() {
    return {
      typeList: [
        {
          name: '乘用车',
          val: 'passenger',
        },
        {
          name: '货车',
          val: 'truck',
        },
      ],
      typeVal: {
        name: '货车',
        val: 'truck',
      },
      list: {
        passenger: [
          {
            name: '轿车',
            img: '/static/images/car-type/icon-car-type_01.png',
            val: 'sedan',
          },
          {
            name: 'MPV',
            img: '/static/images/car-type/icon-car-type_02.png',
            val: 'mpv',
          },
          {
            name: 'SUV',
            img: '/static/images/car-type/icon-car-type_03.png',
            val: 'suv',
          },
          {
            name: '专用\n乘用车',
            img: '/static/images/car-type/icon-car-type_04.png',
            val: 'special',
          },
        ],
        truck: [
          {
            name: '微型',
            img: '/static/images/car-type/icon-car-type_05.png',
            val: 'mini',
          },
          {
            name: '轻型',
            img: '/static/images/car-type/icon-car-type_06.png',
            val: 'light',
          },
          {
            name: '中型',
            img: '/static/images/car-type/icon-car-type_07.png',
            val: 'medium',
          },
          {
            name: '重型',
            img: '/static/images/car-type/icon-car-type_08.png',
            val: 'heavy',
          },
        ],
      },
      selected: {},
      station: null,
    };
  },
  onLoad(query) {
    this.query = query;
    try {
      this.station = JSON.parse(query.data);
    } catch (err) {
      console.log(err);
    }
  },
  async mounted() { },
  onShow() { },
  computed: {
    dataList() {
      return this.list[this.typeVal.val];
    },
  },
  methods: {
    scroll(e) { },
    onPulling() {
      console.log('onPulling');
    },
    onAbout() {
      console.log('onAbout');
    },
    onRefresh() {
      console.log('onRefresh');
      if (this._freshing) return;
      this._freshing = true;
      setTimeout(() => {
        this._freshing = false;
        this.getList(true);
        this.$refs.scrollView.stopRefresh();
      }, 1500);
    },
    scrolltolower() {
      console.log('scrolltolower');
      this.getList();
    },
    getList(refresh) { },
    onTypeSelect(item) {
      this.typeVal = item;
      this.selected = {};
    },
    onSelect(item) {
      this.selected = item;
    },
    onStartNav() {
      if (!this.selected?.name) {
        return uni.showToast({
          title: '请选择车型',
          icon: 'none',
        });
      }
      this.openLocation();
    },
    async openLocation() {
      if (!this.station) {
        return uni.showToast({
          title: '油站异常，请尝试重新选择',
          icon: 'none',
        });
      }
      try {
        uni.$petro.openLocation({
          ...this.station,
          vehicle_type: this.typeVal.val,
          vehicle_subtype: this.selected.val,
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-car-type {
  height: 100%;
  background-color: #fff;

  >.container {
    height: inherit;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .scroll-view {
    flex: 1;
    height: inherit;
    overflow: auto;
  }

  .u-tabs__wrapper__nav {
    width: 100%;
  }

  .list {
    padding: 96rpx 32rpx 80rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
    border-top: 2rpx solid #eee;

    .car-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8rpx 34rpx 8rpx 24rpx;
      border-radius: 16rpx;
      border: 1px solid #eee;
      color: #666;

      &.active {
        border: 1px solid #fa1919;
        font-weight: bold;
        color: #333;
      }
    }

    .img {
      width: 160rpx;
      height: 160rpx;
    }

    .name {
      word-break: keep-all;
      text-align: center;
      flex: 1;
    }
  }

  .tip {
    padding: 32rpx;
    color: #666;
    font-size: 24rpx;

    .point {
      color: #fa1919;
      padding: 0 10rpx;
    }
  }

  .footer {
    padding: 28rpx 48rpx;

    .btn-nav {
      background-color: #fa1919;
      color: #fff;
      border-radius: 200rpx;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
