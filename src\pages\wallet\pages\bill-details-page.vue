<template>
  <div class="bill-details-page">
    <u-navbar :title="accountType == 8 ? '积分详情' : '账单详情'" :autoBack="true" :placeholder="true"></u-navbar>

    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-bill-details :data="detailsInfo" />
    </petro-layout>
  </div>
</template>

<script>
// import zyzxPageBillDetails from '@/components/zyzx-page-bill-details/zyzx-page-bill-details.vue';

export default {
  name: 'bill-details-page',
  // components: { zyzxPageBillDetails },
  data() {
    return {
      detailsInfo: {},
      accountType: '',
    };
  },
  onLoad(options) {
    console.log('🚀 ~ onLoad ~ options:', options);
    // 保存确认下单传递生成的订单信息
    let query = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
    this.detailsInfo = { ...query.queryInfo };
    this.accountType = query.accountType;
    console.log('🚀 ~ onLoad ~ this.detailsInfo:', this.detailsInfo);
  },
  onshow() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.bill-details-page {
  width: 100%;
  height: 100%;
  background: #fff;
}
</style>
