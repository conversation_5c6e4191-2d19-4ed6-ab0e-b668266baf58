<script>
export default {
  globalData: {},
  onLaunch(options) {
    console.log('App Launch', options);
    this.hookInit();
    // 其他地方单独重新初始化
    // const app = getApp();
    // app.hookInit();
  },
  onShow() {
    console.log('App Show');
  },
  onHide() {
    console.log('App Hide');
  },
  methods: {
    async hookInit() {
      console.log('hookInit');
      const res = await uni.$petro.getTokenInfo();
      if (!res?.accessToken) return;
      console.log('hookInit request');
      await (this.$store || this.$vm?.$store).dispatch('getRoles', true);
      let result = await (this.$store || this.$vm?.$store).dispatch('getRolesMap', true);
      console.log('------result', result);
      if (result === 'none') return;
      await (this.$store || this.$vm?.$store).dispatch('getAccountInfoList', {
        accountType: 2, // 1：业务专项账户 2：司机账户 3：车牌账户
        refresh: true,
      });
      await (this.$store || this.$vm?.$store).dispatch('getCompanyList', {
        queryType: 2,
        refresh: true,
      });
      await (this.$store || this.$vm?.$store).dispatch('getUserInfo', true);
    },
  },
};
</script>

<style lang="scss">
@import 'uview-ui/index.scss';

page,
.a-page {
  height: 100%;
  background: #fff;
}

.btn-petro-t {
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  border: 2rpx solid #e64f22;
  border-radius: 16rpx;
  background-color: #e64f22 !important;

  &:active {
    color: #fff;
    background-color: #e64f22;
    opacity: 0.8;
  }

  &.disabled {
    opacity: 0.2;
    pointer-events: none;
  }
}

// 按钮样式覆盖
.custom-btn-block {
  border-radius: 8rpx;
  height: 92rpx;
  line-height: 92rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-weight: bold;
  &.red {
    color: #ffffff;
    border-color: #fa1919;
    background: #fa1919;
  }
  &.red-plain {
    color: #fa1919;
    border-color: #fa1919;
    background: #ffffff;
  }
  &.orange {
    color: #ffffff;
    border-color: #f79901;
    background: #f79901;
  }
  &.orange-plain {
    color: #f79901;
    border-color: #f79901;
    background: #ffffff;
  }
  &.white {
    color: #333333;
    background: #ffffff;
  }
  &.circle {
    border-radius: 100rpx;
  }
  &.no-border {
    border: none !important;
  }
  &.disabled {
    opacity: 0.2;
  }
}

// 单行文本溢出显示省略号
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.zyzx-page-roles .u-text__value {
  max-width: 2em;
}
</style>
