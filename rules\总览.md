---
description: UniApp + Vue 2 司机小程序项目开发总览
globs: .md,.vue,.js,.ts,.scss,.less
alwaysApply: true
---

# UniApp + Vue 2 司机小程序项目开发总览

## 总体描述
该文档对基于UniApp + Vue 2的司机小程序项目进行了全面概述，涵盖技术栈、工程结构、开发规范等核心内容，为项目的整体架构和开发提供清晰的指引。

### 应用范围
本文档适用于petro-soti-zyzx-miniapp-driver项目的所有开发人员、项目经理和相关技术人员，帮助他们了解UniApp司机小程序项目的整体架构、技术栈和开发规范。

### 使用要求
开发人员在进行UniApp小程序项目开发时要严格遵循相关规则文档的要求进行开发，确保项目的一致性和可维护性。项目经理可以根据本文档对前端项目进行整体规划和管理。

## 项目基本信息

### 项目标识
- **项目名称**: petro-soti-zyzx-miniapp-driver
- **项目版本**: 1.1.4
- **发布版本**: 1.1.4
- **项目类型**: 司机端小程序
- **开发模式**: 私有项目

### 项目技术栈

#### 核心框架
- **UniApp**: 2.0.2-3090820231124001 - 跨平台应用开发框架
- **Vue**: 2.6.14 - 渐进式JavaScript框架 (严格使用Options API)
- **Vuex**: 3.6.2 - Vue.js的状态管理模式

#### 构建工具
- **Vue CLI**: 5.0 - Vue.js开发的标准工具
- **Webpack**: 通过Vue CLI集成，支持自定义配置
- **Babel**: ES6+语法转换，支持UniApp平台适配
- **PostCSS**: CSS后处理器，支持autoprefixer

#### UI组件库
- **uView UI**: 2.0.36 - UniApp生态UI框架，使用u-前缀
- **@petro-soti/foudation-zyzx**: 2.0.0-1010 - 中石油自研组件库，使用petro-/zyzx-前缀

#### 样式预处理
- **SCSS/Sass**: 1.70.0 - CSS预处理器
- **sass-loader**: 10 - Webpack的Sass加载器

#### 网络请求
- **flyio**: 0.6.2 - 支持请求转发的HTTP请求库
- **uni.$petro.http**: 基于flyio封装的统一请求方法

#### 开发工具
- **vconsole**: 3.15.1 - 移动端调试工具
- **commitizen**: 4.3.0 - Git提交规范工具

#### 支持平台
- **微信小程序** (mp-weixin) - 主要平台
- **支付宝小程序** (mp-alipay) - 支持平台
- **mPaaS小程序** (mp-mpaas) - 企业平台
- **H5** (h5) - Web端支持
- **App Plus** (app-plus) - 原生App支持

#### 插件模式支持
- **微信小程序插件** (mp-wx-plugin)
- **支付宝小程序插件** (mp-ali-plugin)
- **mPaaS小程序插件** (mp-mpaas-plugin)

## 前端规范文档总览

| 序号 | 规则名称 | 用途描述 |
|------|----------|----------|
| 1 | **公共规则.md** | **技术栈规范**：明确UniApp + Vue 2技术栈的使用规范<br/>**工程结构**：展示UniApp项目的标准目录组织<br/>**编码规范**：统一的代码风格和命名规范<br/>**开发约束**：强制性技术约束和最佳实践 |
| 2 | **路由.md** | **pages.json配置**：定义UniApp页面路由的配置方式<br/>**页面跳转**：规范uni.navigateTo等导航API的使用<br/>**路径命名**：明确页面路径和文件的命名规范<br/>**分包管理**：小程序分包配置和优化策略 |
| 3 | **状态管理.md** | **Vuex模块化**：说明Vuex store的模块化架构设计<br/>**状态管理规范**：规定Vuex在UniApp项目中的具体使用方式<br/>**数据流管理**：定义组件与store的交互模式<br/>**状态持久化**：数据持久化和同步策略 |
| 4 | **API接口.md** | **uni.$petro.http**：明确统一请求方法的使用规范<br/>**Mock数据**：规范内联Mock数据的配置方式<br/>**响应格式**：统一API响应数据的结构标准<br/>**错误处理**：说明请求错误的统一处理机制<br/>**网络优化**：请求优化和缓存策略 |
| 5 | **UI.md** | **uView UI**：规范uView UI组件库的使用方式<br/>**自研组件**：说明@petro-soti/foudation-zyzx组件库的使用<br/>**样式规范**：定义SCSS样式的编写规范<br/>**响应式设计**：明确跨平台UI适配规则<br/>**主题定制**：UI主题和样式定制规范 |
| 6 | **代码生成规则.md** | **AI工具约束**：明确AI工具使用的技术栈约束<br/>**代码模板**：提供标准的代码生成模板<br/>**最佳实践**：定义代码生成的最佳实践规范<br/>**质量保证**：代码生成的质量控制和验证 |

## 项目工程结构

### 完整目录结构

```text
petro-soti-zyzx-miniapp-driver/
├── build/                          # 构建配置
│   ├── manifest/                   # 清单构建工具
│   │   ├── index.js                # 清单构建入口
│   │   └── cli.js                  # 命令行工具
│   └── tools/                      # 构建工具
├── src/                            # 源代码目录
│   ├── pages/                      # 页面目录
│   │   ├── index/                  # 首页
│   │   ├── station-list/           # 油站列表
│   │   ├── car/                    # 车辆管理
│   │   │   └── pages/              # 车辆子页面
│   │   │       ├── car-details-page.vue    # 车辆详情
│   │   │       └── car-type.vue            # 车辆类型
│   │   ├── wallet/                 # 钱包相关
│   │   │   └── pages/              # 钱包子页面
│   │   │       ├── bill-list-page.vue      # 账单列表
│   │   │       ├── wallet-unitcar-page.vue # 单位车辆钱包
│   │   │       └── car-wallet-list.vue     # 车辆钱包列表
│   │   ├── mine/                   # 个人中心
│   │   │   └── pages/              # 个人中心子页面
│   │   │       ├── about-page.vue          # 关于页面
│   │   │       ├── equipment-page.vue      # 设备页面
│   │   │       └── edit-password-page.vue  # 修改密码
│   │   ├── login/                  # 登录页面
│   │   ├── verification/           # 实人认证
│   │   ├── business-info/          # 业务信息
│   │   ├── order-detail/           # 订单详情
│   │   ├── pay-result/             # 支付结果
│   │   ├── message/                # 站内信
│   │   ├── todo/                   # 待办事项
│   │   ├── todo-list/              # 待办列表
│   │   ├── help/                   # 帮助中心
│   │   ├── demo/                   # 演示页面
│   │   ├── webview/                # 内嵌网页
│   │   ├── product/                # 产品页面
│   │   ├── member-code/            # 会员码
│   │   ├── code-pay/               # 码支付
│   │   ├── search/                 # 搜索页面
│   │   ├── car-account/            # 车辆账户
│   │   └── none/                   # 空页面
│   ├── components/                 # 组件目录
│   │   ├── p-test/                 # 测试组件
│   │   ├── p-map/                  # 地图组件
│   │   ├── p-agreement/            # 协议组件
│   │   ├── p-data-list/            # 数据列表组件
│   │   ├── p-login-token/          # 登录令牌组件
│   │   ├── p-slider-verify/        # 滑块验证组件
│   │   ├── page-home/              # 首页组件
│   │   ├── page-navigation/        # 导航组件
│   │   ├── page-order/             # 订单组件
│   │   ├── page-refuel-oil/        # 加油组件
│   │   ├── page-station-list/      # 油站列表组件
│   │   ├── page-verification/      # 验证组件
│   │   ├── zyzx-company-bar/       # 企业栏组件
│   │   ├── zyzx-company-popup/     # 企业弹窗组件
│   │   ├── zyzx-page-car-wallet/   # 车辆钱包组件
│   │   ├── zyzx-page-face-recognition/ # 人脸识别组件
│   │   └── zyzx-page-unit-setting/ # 单位设置组件
│   ├── services/                   # 服务目录
│   │   ├── http/                   # HTTP请求服务
│   │   │   ├── index.js            # HTTP服务入口
│   │   │   ├── api.js              # 通用API
│   │   │   ├── account.js          # 账户API
│   │   │   ├── car.js              # 车辆API
│   │   │   ├── station.js          # 加油站API
│   │   │   └── order.js            # 订单API
│   │   ├── store/                  # Vuex状态管理
│   │   │   ├── index.js            # store入口
│   │   │   ├── roles.js            # 用户角色
│   │   │   ├── account.js          # 账户状态
│   │   │   ├── accountDriver.js    # 司机账户状态
│   │   │   ├── car.js              # 车辆状态
│   │   │   ├── station.js          # 加油站状态
│   │   │   ├── company.js          # 企业状态
│   │   │   ├── tabBar.js           # 底部导航状态
│   │   │   ├── test1.js            # 测试模块1
│   │   │   └── test2.js            # 测试模块2
│   │   └── enum/                   # 枚举定义
│   │       └── index.js            # 枚举入口
│   ├── static/                     # 静态资源
│   │   ├── images/                 # 图片资源
│   │   ├── xml/                    # XML文件
│   │   ├── gwcli1-0-2.wasm.br     # WASM文件
│   │   └── *.png                   # 图标文件
│   ├── utils/                      # 工具函数
│   │   ├── index.js                # 工具函数入口
│   │   └── sdk.js                  # SDK工具
│   ├── api/                        # API数据
│   │   └── data.js                 # 数据文件
│   ├── assets/                     # 资源文件
│   │   └── lg.png                  # Logo图片
│   ├── App.vue                     # 应用入口组件
│   ├── main.js                     # 应用入口文件
│   ├── pages.json                  # 页面配置
│   ├── uni.scss                    # 全局样式
│   ├── uni.promisify.adaptor.js    # Promise适配器
│   ├── config.index.local.js       # 环境配置文件
│   ├── mini.project.json           # 小程序项目配置
│   ├── plugin.js                   # 插件入口
│   ├── plugin.json                 # 插件配置
│   └── .manifest.json              # 应用清单
├── rules/                          # 开发规范文档
│   ├── 总览.md                     # 项目总览
│   ├── 公共规则.md                 # 公共开发规则
│   ├── 路由.md                     # 路由规范
│   ├── 状态管理.md                 # 状态管理规范
│   ├── API接口.md                  # API接口规范
│   ├── UI.md                       # UI设计规范
│   └── cust/                       # 自定义规则
│       └── 代码生成规则.md         # 代码生成规则
├── package.json                    # 项目依赖配置
├── vue.config.js                   # Vue CLI配置
├── babel.config.js                 # Babel配置
├── postcss.config.js               # PostCSS配置
├── README.md                       # 项目说明
└── CHANGELOG.md                    # 更新日志
```

### 目录结构说明

#### 核心目录
- **src/pages/**: 页面文件，按功能模块组织，支持子页面嵌套
- **src/components/**: 公共组件，使用p-前缀（功能组件）、page-前缀（页面组件）、zyzx-前缀（自研组件）
- **src/services/**: 业务服务层，包含HTTP请求、状态管理、枚举定义
- **src/static/**: 静态资源，包含图片、WASM文件、XML配置等

#### 配置文件
- **pages.json**: UniApp页面路由配置，定义页面路径、样式、分包等
- **manifest.json**: 应用配置清单，定义应用信息、权限、平台配置
- **vue.config.js**: Vue CLI构建配置，支持多平台编译和自定义优化
- **config.index.local.js**: 环境配置文件，定义不同环境的API地址和参数

## 开发环境配置

### 环境要求

- **Node.js**: >= 14.0.0
- **包管理器**: npm >= 6.0.0 或 yarn >= 1.22.0
- **开发工具**: HBuilderX (推荐) 或 VS Code
- **浏览器**: Chrome >= 80 (用于H5调试)

### 开发工具配置

#### HBuilderX (推荐)
- **版本**: 最新稳定版
- **插件**: 内置UniApp开发环境，无需额外配置
- **优势**: 官方IDE，调试功能完善，支持真机调试

#### VS Code
- **必装插件**:
  - uni-app插件
  - Vue Language Features (Volar)
  - Vue 3 Snippets
  - SCSS IntelliSense
- **配置**: 需要配置UniApp语法支持

#### 小程序开发工具
- **微信开发者工具**: 用于微信小程序调试和发布
- **支付宝小程序开发者工具**: 用于支付宝小程序调试
- **mPaaS工作台**: 用于mPaaS小程序开发和调试

### 构建命令

#### 开发环境
```bash
# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 支付宝小程序开发
npm run dev:mp-alipay

# mPaaS小程序开发
npm run dev:mp-mpaas

# 分包开发模式
npm run dev:mp-weixin:sub      # 微信小程序分包
npm run dev:mp-alipay:sub      # 支付宝小程序分包

# 自定义分包名
npm run dev:mp-alipay -- --subpackage=example
npm run dev:mp-weixin -- --subpackage=example

# 插件开发模式
npm run dev:mp-mpaas:plugin    # mPaaS小程序插件
npm run dev:mp-alipay:plugin   # 支付宝小程序插件
npm run dev:mp-weixin:plugin   # 微信小程序插件
```

#### 生产构建
```bash
# H5构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# 支付宝小程序构建
npm run build:mp-alipay

# mPaaS小程序构建
npm run build:mp-mpaas

# App构建
npm run build:app-plus

# 自定义构建
npm run build:custom
```

#### 辅助命令
```bash
# 安装依赖后自动执行
npm run postinstall            # 构建组件和清单

# 构建相关
npm run build:components       # 构建自研组件
npm run build:manifest         # 构建应用清单

# 文档和日志
npm run changelog              # 生成更新日志
```

### 环境配置

#### 配置文件说明
- **config.index.local.js**: 本地环境配置，包含API地址、渠道号等
- **支持环境**: dev(开发) / sit(测试) / uat(预生产) / prd(生产)

#### 环境切换
```javascript
// src/config.index.local.js
export default {
  env: 'sit',                    // 当前环境
  channel: 'C15',                // 渠道号
  packageName: 'zyzx-driver-sit', // 包名
  // ... 其他配置
};
```

## 开发规范要求

### 强制性约束
1. **严格使用Vue 2.6.14 + Options API**，禁止使用Vue 3或Composition API
2. **统一使用uni.$petro.http**进行网络请求，禁止使用其他HTTP库
3. **优先使用uView UI组件**，其次使用petro/zyzx前缀的自研组件
4. **必须使用Vuex 3.6.2**进行状态管理，禁止使用Pinia
5. **样式必须使用SCSS**，禁止使用CSS-in-JS或styled-components

### 代码质量要求
1. **遵循ESLint规范**，确保代码风格一致
2. **组件命名规范**：使用kebab-case命名，功能组件使用p-前缀
3. **API函数规范**：必须包含内联Mock数据，遵循统一的命名规范
4. **状态管理规范**：按业务模块组织Vuex store，使用命名空间
5. **路由配置规范**：严格按照UniApp规范配置pages.json

### 最佳实践
1. **跨平台兼容**：使用UniApp条件编译确保多平台兼容性
2. **性能优化**：合理使用分包、懒加载等优化策略
3. **错误处理**：统一的错误处理机制和用户反馈
4. **安全规范**：敏感信息加密，防止XSS和CSRF攻击
5. **可维护性**：清晰的代码注释，合理的组件拆分
