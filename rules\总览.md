---
description: UniApp + Vue 2 司机小程序项目开发总览
globs: .md,.vue,.js,.ts,.scss,.less
alwaysApply: true
---

### 总体描述
该文档对基于UniApp + Vue 2的司机小程序项目进行了全面概述，涵盖技术栈、工程结构、开发规范等核心内容，为项目的整体架构和开发提供清晰的指引。

#### 应用范围
本文档适用于petro-soti-zyzx-miniapp-driver项目的所有开发人员、项目经理和相关技术人员，帮助他们了解UniApp司机小程序项目的整体架构、技术栈和开发规范。

#### 使用要求
开发人员在进行UniApp小程序项目开发时要严格遵循相关规则文档的要求进行开发，确保项目的一致性和可维护性。项目经理可以根据本文档对前端项目进行整体规划和管理。

### 项目技术栈

#### 核心框架
- **UniApp**: 2.0.2 - 跨平台应用开发框架
- **Vue**: 2.6.14 - 渐进式JavaScript框架 (Options API)
- **Vuex**: 3.6.2 - Vue.js的状态管理模式

#### 构建工具
- **Vue CLI**: 5.0 - Vue.js开发的标准工具
- **Webpack**: 通过Vue CLI集成
- **Babel**: ES6+语法转换
- **PostCSS**: CSS后处理器

#### UI组件库
- **uView UI**: 2.0.36 - UniApp生态最优秀的UI框架
- **@petro-soti/foudation-zyzx**: 1.2.0-1090 - 中石油自研组件库

#### 样式预处理
- **SCSS/Sass**: 1.70.0 - CSS预处理器
- **sass-loader**: 10 - Webpack的Sass加载器

#### 网络请求
- **flyio**: 0.6.2 - 支持请求转发的HTTP请求库
- **uni.$petro.http**: 基于flyio封装的统一请求方法

#### 开发工具
- **vconsole**: 3.15.1 - 移动端调试工具
- **commitizen**: 4.3.0 - Git提交规范工具

#### 支持平台
- 微信小程序 (mp-weixin)
- 支付宝小程序 (mp-alipay)
- mPaaS小程序 (mp-mpaas)
- H5 (h5)
- App Plus (app-plus)

### 前端规范文档总览

|      | 规则名称         | 用途描述                                                     |
| ---- | ---------------- | ------------------------------------------------------------ |
| 1    | 前端_公共规则.md | 1.技术栈规范：明确UniApp + Vue 2技术栈的使用规范<br />2.工程结构：展示UniApp项目的标准目录组织<br />3.编码规范：统一的代码风格和命名规范 |
| 2    | 前端_路由.md     | 1.pages.json配置：定义UniApp页面路由的配置方式<br />2.页面跳转：规范uni.navigateTo等导航API的使用<br />3.路径命名：明确页面路径和文件的命名规范 |
| 3    | 前端_状态管理.md | 1.Vuex模块化：说明Vuex store的模块化架构设计<br />2.状态管理规范：规定Vuex在UniApp项目中的具体使用方式<br />3.数据流管理：定义组件与store的交互模式 |
| 4    | 前端_API接口.md  | 1.uni.$petro.http：明确统一请求方法的使用规范<br />2.Mock数据：规范内联Mock数据的配置方式<br />3.响应格式：统一API响应数据的结构标准<br />4.错误处理：说明请求错误的统一处理机制 |
| 5    | 前端_UI.md       | 1.uView UI组件：规范uView UI组件库的使用方式<br />2.自研组件：说明@petro-soti/foudation-zyzx组件的使用<br />3.样式规范：定义SCSS样式的编写规范<br />4.响应式设计：跨平台UI适配的最佳实践 |

### 工程结构

```
petro-soti-zyzx-miniapp-driver/     # 项目根目录
├── build/                          # 构建配置
│   ├── manifest/                   # manifest构建工具
│   ├── tools/                      # 构建工具
│   └── env/                        # 环境配置
├── dist/                           # 构建输出目录
├── public/                         # 静态资源
│   └── index.html                  # H5入口文件
├── src/                            # 源码目录
│   ├── api/                        # API数据管理
│   ├── assets/                     # 静态资源文件
│   ├── components/                 # 公共组件
│   │   ├── p-*/                    # 功能组件 (p-前缀)
│   │   └── page-*/                 # 页面级组件
│   ├── mycomponents/               # 自定义组件
│   │   ├── keyboard/               # 键盘组件
│   │   └── security-plugin/        # 安全插件
│   ├── pages/                      # 页面文件
│   │   ├── index/                  # 首页
│   │   ├── station-list/           # 加油站列表
│   │   ├── car/                    # 车辆管理
│   │   └── ...                     # 其他页面
│   ├── services/                   # 业务服务
│   │   ├── http/                   # HTTP请求模块
│   │   ├── store/                  # Vuex状态管理
│   │   └── enum/                   # 枚举定义
│   ├── static/                     # 静态资源
│   ├── utils/                      # 工具函数
│   ├── wxcomponents/               # 微信原生组件
│   ├── App.vue                     # 应用入口组件
│   ├── main.js                     # 应用入口文件
│   ├── pages.json                  # 页面路由配置
│   ├── manifest.json               # 应用配置文件
│   ├── uni.scss                    # 全局样式变量
│   └── config.index.local.js       # 环境配置文件
├── rules/                          # 开发规范文档
├── package.json                    # 项目依赖配置
├── vue.config.js                   # Vue CLI配置
├── babel.config.js                 # Babel配置
└── postcss.config.js               # PostCSS配置
```

### 开发环境配置

#### 环境要求
- Node.js >= 14.0.0
- npm >= 6.0.0 或 yarn >= 1.22.0
- HBuilderX (推荐) 或 VS Code

#### 开发工具配置
- **HBuilderX**: UniApp官方IDE，内置UniApp开发环境
- **VS Code**: 需安装uni-app插件和Vue插件
- **微信开发者工具**: 微信小程序调试
- **支付宝小程序开发者工具**: 支付宝小程序调试

#### 构建命令
```bash
# 开发环境
npm run dev:h5              # H5开发
npm run dev:mp-weixin       # 微信小程序开发
npm run dev:mp-alipay       # 支付宝小程序开发
npm run dev:mp-mpaas        # mPaaS小程序开发

# 生产构建
npm run build:h5            # H5构建
npm run build:mp-weixin     # 微信小程序构建
npm run build:mp-alipay     # 支付宝小程序构建
npm run build:mp-mpaas      # mPaaS小程序构建
```
| 5    | 前端_UI.md       | 1.布局规范：规定页面整体及各区域的布局方式<br />2.色彩规范：定义项目使用的主色调、中性色等色彩标准<br />3.字体规范：明确字体大小、权重、行高的统一规则<br />4.组件规范：制定基础组件尺寸及UI组件的定制标准<br />5.间距规范：确定基础间距单位及页面元素间的间距规则<br />6.表单规范：规范表单的布局、控件样式及验证方式<br />7.表格规范：定义表格的基础样式、功能及状态展示规则<br />8.按钮规范：明确按钮的类型、尺寸及状态样式标准<br />9.图标规范：规定图标库的选用及使用原则<br />10.交互规范：说明页面交互中的反馈机制、加载状态及动画效果要求 |
