<template>
  <div class="page-car-container">
    <u-navbar :title="'实人认证'" :autoBack="false" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <petro-layout ref="layout">
      <page-verification></page-verification>
    </petro-layout>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import pageVerification from '@/components/page-verification/page-verification.vue';

export default {
  components: {
    pageVerification,
  },
  data() {
    return {};
  },
  onLoad(query) {
    if (!uni.$petro.Utils?.isEmptyObject(query)) {
      // uni.showModal({
      //   title: 'onLoad.query',
      //   content: JSON.stringify(query),
      // });
      
    }
  },
  computed: {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.page-car-container {
  height: 100vh;
  background: #f7f7fb;
}
</style>
