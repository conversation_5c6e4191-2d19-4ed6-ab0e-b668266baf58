<template>
  <div class="page-container">
    <u-navbar :title="'加油账单'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <view class="page-order-detail">
          <div class="content-view">
            <div class="station-row">
              <img class="station-img" src="@/static/images/cnpc-logo.png" />
              <div class="station-name text-overflow">{{ orderInfo.stationName || '中国石油加油站' }}</div>
            </div>
            <div class="amount-view">
              <div class="amount-title">实付金额</div>
              <div class="amount">
                <span>￥</span>
                <span>{{ !orderInfo.payItemList.length ? 0 : orderInfo.payItemList[0].payAmount }}</span>
              </div>
              <div class="system-time">
                <div class="countdown-bg1">{{ hour }}</div>
                <span>:</span> <div class="countdown-bg2">{{ minutes }} </div><span>:</span><div class="countdown-bg3">{{ seconds }}</div
                ><span>:</span>
                <div class="countdown-bg4">{{ count }}</div>
              </div>
            </div>

            <div class="details-price">
              <div class="details-price-item">
                <div class="item-left">订单编号</div>
                <div class="item-right">
                  <div class="item-right-text">{{ orderInfo.orderNo || '' }}</div>
                  <div class="copy" @click="copyProductNo(orderInfo.orderNo)">复制</div>
                </div>
              </div>
              <div class="details-price-item">
                <div class="item-left">枪号</div>
                <div class="item-right">{{ oilOrder.gunNo || '' }}号枪</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">油品名称</div>
                <div class="item-right">{{ oilOrder.productName || '' }}</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">单价</div>
                <div class="item-right">{{ oilOrder.unitPrice || '' }}</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">升数</div>
                <div class="item-right">{{ (oilOrder.productQty || '') + (oilOrder.productUnit || '') }}</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">订单金额</div>
                <div class="item-right">{{ orderInfo.orderTotalAmount || 0 }}元</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">支付时间</div>
                <div class="item-right">{{ orderInfo.payConfirmationTime || '' }}</div>
              </div>
              <!-- <div class="details-price-item">
                <div class="item-left">支付渠道</div>
                <div class="item-right">{{ orderInfo.payChannel || '' }}</div>
              </div> -->
              <!-- <div class="details-price-item">
                <div class="item-left">订单类型</div>
                <div class="item-right">{{ ORDER_SUBTYPE[orderInfo.orderSubType] }}</div>
              </div> -->
              <div class="details-price-item">
                <div class="item-left">优惠金额</div>
                <div class="item-right">
                  <span class="red">{{ orderInfo.discountTotalAmount || 0 }}</span>
                  <span>元</span>
                </div>
              </div>
              <div class="details-price-item-panel">
                <div class="details-price-item pay-list" v-for="(item, index) in orderInfo.discountList" :key="index">
                  <div class="item-left">{{ item.payMethodName || '' }}</div>
                  <div class="item-right">
                    <span>{{ item.payAmount }}</span>
                    <span>元</span>
                  </div>
                </div>
              </div>
              <div class="details-price-item">
                <div class="item-left">实付金额</div>
                <div class="item-right">
                  <span class="red">{{ orderInfo.actualPayTotalAmount || 0 }}</span>
                  <span>元</span>
                </div>
              </div>
              <div class="details-price-item-panel">
                <div class="details-price-item pay-list" v-for="(item, index) in orderInfo.payItemList" :key="index">
                  <div class="item-left">{{ item.payMethodName || '' }}</div>
                  <div class="item-right">
                    <span>{{ item.payAmount }}</span>
                    <span>元</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="bottom-btn">
            <button class="custom-btn-block circle" type="default" @click="back()">确认</button>
          </div>
        </view>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { ORDER_SUBTYPE } from '@/services/enum';
import { getOrderDetailApi } from '@/services/http';

export default {
  components: {},
  data() {
    return {
      ORDER_SUBTYPE: ORDER_SUBTYPE,
      query: {},
      timer: null,
      // 倒计时展示绑定值
      hour: '00',
      minutes: '00',
      seconds: '00',
      count: '00',
      // 订单详情
      orderInfo: null,
    };
  },
  computed: {
    oilOrder() {
      console.log('this.orderInfo.orderItemList[0]---', this.orderInfo);
      return this.orderInfo ? this.orderInfo.orderItemList[0] : {};
    },
  },
  onLoad(query) {
    this.query = query;
    clearInterval(this.timer);
    this.goto();
    this.getData();
  },
  methods: {
    // 获取订单详情
    async getData() {
      try {
        const params = {
          stationCode: this.query?.stationCode,
          orderNo: this.query?.orderNo,
        };

        const { success, data } = await getOrderDetailApi(params);

        if (!success) return;

        this.orderInfo = data;
      } catch (error) {
        console.log(error);
      }
    },
    // 时钟
    goto() {
      this.timer = setInterval(() => {
        this.count = Number(this.count);
        if (this.count < 99) {
          this.count = this.count + 1;
        } else {
          this.count = 0;
        }
        if (this.count < 10) {
          this.count = '0' + this.count;
        }
        var date = new Date();
        this.hour = date.getHours(); // 时
        if (this.hour >= 0 && this.hour <= 9) {
          this.hour = '0' + this.hour;
        }
        this.minutes = date.getMinutes(); // 分
        if (this.minutes >= 0 && this.minutes <= 9) {
          this.minutes = '0' + this.minutes;
        }
        this.seconds = date.getSeconds(); //秒
        if (this.seconds >= 0 && this.seconds <= 9) {
          this.seconds = '0' + this.seconds;
        }
      }, 10);
    },
    // 复制
    copyProductNo(value) {
      uni.setClipboardData({
        data: value,
        success: () => {
          uni.showToast({
            title: '复制成功',
          });
        },
      });
    },
    // 返回
    back() {
      uni.$petro.route({
        type: 'back',
      });
    },
  },
  // 销毁计时器
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
    overflow: hidden;
    .page-order-detail {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .content-view {
        width: 100%;
        flex: 1;
        padding: 40rpx 32rpx 32rpx;
        overflow-y: auto;
        box-sizing: border-box;
        .station-row {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 32rpx;
          .station-img {
            width: 52rpx;
            height: 52rpx;
            flex-shrink: 0;
          }
          .station-name {
            margin-left: 16rpx;
            font-weight: bold;
            font-size: 36rpx;
            color: #000000;
            line-height: 42rpx;
          }
        }

        .amount-view {
          margin-top: 24rpx;
          background: #ffffff;
          padding: 32rpx 32rpx 0;
          border-radius: 16rpx;
          box-sizing: border-box;
          .amount-title {
            text-align: center;
            height: 44rpx;
            font-size: 28rpx;
            color: #000000;
            line-height: 44rpx;
            text-align: center;
          }
          .amount {
            margin-top: 28rpx;
            text-align: center;
            span:nth-child(1) {
              height: 52rpx;
              font-weight: bold;
              font-size: 44rpx;
              color: #ff5500;
              line-height: 52rpx;
              letter-spacing: 1px;
            }
            span:nth-child(2) {
              text-align: center;
              height: 78rpx;
              font-weight: bold;
              font-size: 64rpx;
              color: #ff5500;
              line-height: 75rpx;
              letter-spacing: 1px;
            }
          }
        }

        .system-time {
          width: 100%;
          height: 160rpx;
          background: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #e64f22;
          font-size: 32rpx;
          font-weight: bold;
          span {
            padding: 0 16rpx;
          }
          div {
            width: 88rpx;
            height: 88rpx;
            color: #ffffff;
            border-radius: 20rpx;
            text-align: center;
            line-height: 88rpx;
            font-size: 36rpx;
            box-shadow: 0 2rpx 20rpx 0 rgba(0, 0, 0, 0.07);
          }
          .countdown-bg1 {
            background-image: linear-gradient(288deg, #f9773c 0%, #ff7c34 100%);
          }
          .countdown-bg2 {
            background-image: linear-gradient(288deg, #ff6728 0%, #ff702d 100%);
          }
          .countdown-bg3 {
            background-image: linear-gradient(288deg, #ff5c22 0%, #ff6426 100%);
          }
          .countdown-bg4 {
            background-image: linear-gradient(288deg, #ff501c 0%, #ff5921 100%);
          }
        }

        .details-price {
          margin-top: 24rpx;
          background: #ffffff;
          border-radius: 16rpx;
          padding: 24rpx 32rpx;

          .details-price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .item-left {
              font-size: 28rpx;
              font-weight: 400;
              color: #666666;
              line-height: 67rpx;
            }

            .item-right {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 67rpx;

              .item-right-text {
                line-height: 50rpx;
                font-size: 26rpx;
                font-weight: 400;
                color: #333333;
              }

              .copy {
                margin-left: 10rpx;
                width: 64rpx;
                height: 32rpx;
                background: #ebf6ff;
                border-radius: 4rpx;
                font-size: 20rpx;
                color: #47b2ff;
                line-height: 32rpx;
                text-align: center;
              }

              .red {
                color: #ff5500;
              }
            }

            .item-right-button {
              padding: 0 20rpx;
              height: 48rpx;
              border-radius: 4rpx;
              border: 1rpx solid #333333;
              line-height: 48rpx;
              font-size: 26rpx;
              font-weight: 400;
              color: #333333;
              text-align: center;
            }

            .color {
              color: #e64f22;
              border: 1rpx solid #e64f22;
            }
          }
        }

        .pay-list {
          padding: 0 32rpx;
        }

        .details-price-item-panel {
          background: #f5f8fa;
          border-radius: 8rpx;
        }
      }

      .bottom-btn {
        padding: 36rpx 48rpx 0;
        background: #ffffff;
        padding-bottom: env(safe-area-inset-bottom);
        button {
          color: #fa1919;
          border: 2rpx solid #eeeeee;
        }
      }
    }
  }
}
</style>
