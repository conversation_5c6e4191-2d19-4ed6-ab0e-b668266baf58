<template>
  <div class="page-wallet">
    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-wallet-unitcar ref="walletRef" :isShowBar="true" />
    </petro-layout>
  </div>
</template>

<script>
// import zyzxPageWalletUnitcar from '@/components/zyzx-page-wallet-unitcar/zyzx-page-wallet-unitcar.vue';

export default {
  name: 'wallet',
  // components: { zyzxPageWalletUnitcar },
  data() {
    return {};
  },
  onShow() {
    this.$nextTick(() => {
      this.$refs.walletRef.getBillList(true);
    });
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.page-wallet {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}

.wallet {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bill {
  flex: 1;
  overflow: scroll;
}
</style>
