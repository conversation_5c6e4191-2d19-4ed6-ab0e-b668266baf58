<template>
  <view class="header-menu">
    <div class="item" v-for="(item, index) in menuList" :key="index" @click="menuClick(item)">
      <image class="icon" :src="item.icon"></image>
      <div class="name">{{ item.name }}</div>
    </div>
  </view>
</template>

<script>
import { mapState, mapGetters } from 'vuex';

export default {
  name: 'header-menu',
  components: {},
  props: {
    shopId: {
      type: [Number, String],
      default: '',
    },
    initOpen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      menuList: [
        { name: '我的钱包', type: 'url', value: '/pages/wallet/wallet', icon: '/static/images/oil-menu-wdqb.png' },
        { name: '车牌卡', type: 'url', value: 'pages/car-account/car-account', icon: '/static/images/oil-menu-wdcl.png' },
        { name: '室内付款', type: 'event', value: 'onOilCode', icon: '/static/images/oil-menu-dbsx.png' }
        // { name: '我的车辆', type: 'url', value: '/pages/car/car', icon: '/static/images/oil-menu-wdcl.png' },
        // { name: '待办事项', type: 'url', value: '/pages/todo/todo', icon: '/static/images/oil-menu-dbsx.png' },
        // { name: '扫一扫', type: 'event', value: 'onScannedCode', icon: '/static/images/oil-menu-sys.png' },
      ],
    };
  },
  computed: {},
  methods: {
    // 点击事件
    async menuClick(item) {
      const { type, value } = item;
      if (type == 'url') {
        uni.$petro.route(value, {});
      } else if (type == 'event') {
        // 扫一扫
        if (value == 'onScannedCode') {
          this.onScan();
        }
        // 跳转加油模块室内支付
        if (value == 'onOilCode') {
          this.$store.commit('switchOilTab', 'code');
          this.$store.commit('switchTab', 'refuel-oil');
        }
        // this.$emit(value);
      }
    },
    // 扫一扫
    async onScan() {
      try {
        const res = await uni.$petro.scan({
          type: 4,
        });
        if (res?.success && res?.type === 'other') {
          uni.showModal({
            title: '温馨提示',
            content: '实人认证成功',
            showCancel: false,
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.header-menu {
  padding: 34rpx 50rpx;
  display: flex;
  justify-content: space-between;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    .icon {
      width: 78rpx;
      height: 78rpx;
    }
    .name {
      margin-top: 4rpx;

      font-size: 24rpx;
      color: #ffffff;
      line-height: 33rpx;
    }
  }
}
</style>
