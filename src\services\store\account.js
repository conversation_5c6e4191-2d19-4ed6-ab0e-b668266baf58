export default {
  state: {
    walletInfo: {
      enterpriseAccountNo: '',
      mainAccountNo: '',
      enterpriseNo: '',
      enterpriseStaffNo: '',
      userId: '',
      userName: '',
      licencePlate: '',
      unitAlias: '',
      accountType: 0,
      businessNo: '',
      businessType: 0,
      invoiceType: 0,
      accountStatus: 0,
      accountPlace: '',
      accountPlaceName: '',
      remark: '',
      serialNo: 0,
      memberName: '',
      memberDocNo: '',
      enterpriseName: '',
      staffNo: '',
      bdCardNo: '',
      walletAccountList: [
        {
          cardNo: null,
          accountNo: '',
          frozenAmount: 0,
          availableAmount: 0,
          accountStatus: null,
          capitalType: 1,
          accountType: 5,
        },
      ],
      loyaltyAccountList: [
        {
          pointAccountNo: '',
          frozenPointAmount: 0,
          availablePointAmount: 0,
          accountStatus: 1,
          accountType: 8,
        },
        {
          pointAccountNo: '',
          frozenPointAmount: 0,
          availablePointAmount: 0,
          accountStatus: 1,
          accountType: 13,
        },
      ],
    },
  },
  mutations: {
    setWalletInfo(state, value) {
      Object.assign(state.walletInfo, value);
      console.log(state.walletInfo);
    },
  },
  actions: {
    // 获取钱包信息
    async getWalletInfo({ commit, dispatch, state, rootState }, payload = {}) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.walletInfo?.staffNo) return state.walletInfo;
      try {
        // const params = {
        //   enterpriseNo: orgCode,
        //   businessNo: rootState?.roles?.role?.businessNo,
        //   staffRole: payload?.staffRole,
        // };
        console.log(rootState, 'rootState');

        const params = {
          businessNo: rootState?.roles?.role?.businessNo,
          enterpriseNo: orgCode,
          enterpriseAccountNo: rootState?.company?.accountInfo?.enterpriseAccountNo,
          mainAccountNo: rootState?.company?.accountInfo?.mainAccountNo,
        };
        let showLoading = true;
        if (typeof payload?.showLoading === 'boolean') showLoading = payload?.showLoading;
        // const { success, data } = await uni.$petro.http('account.manager.getDetailAccountInfo.h5', params, {
        //   showLoading: showLoading,
        // });

        const { success, data } = await uni.$petro.http('account.user.queryAccountDetailInfo.h5', params, {
          showLoading: showLoading,
          mockResponse: {
            success: true,
            data: {
              enterpriseAccountNo: '************',
              mainAccountNo: '************',
              enterpriseNo: '****************',
              enterpriseStaffNo: '*****************',
              userId: '469',
              userName: '雷天伟',
              licencePlate: '',
              unitAlias: '长沙市岳麓区林雀子饭店',
              accountType: 1,
              businessNo: '****************',
              businessType: 10,
              invoiceType: 1,
              accountStatus: 1,
              accountPlace: '1-A4301-C001',
              accountPlaceName: '湖南长沙销售分公司',
              remark: null,
              serialNo: 0,
              memberName: '雷天伟',
              memberDocNo: '**************',
              enterpriseName: '长沙市岳麓区林雀子饭店',
              staffNo: '*****************',
              bdCardNo: '***********',
              walletAccountList: [
                {
                  cardNo: null,
                  accountNo: '************',
                  frozenAmount: *********.0,
                  availableAmount: ********.0,
                  accountStatus: null,
                  capitalType: 1,
                  accountType: 5,
                },
              ],
              loyaltyAccountList: [
                {
                  pointAccountNo: '************',
                  frozenPointAmount: 0.0,
                  availablePointAmount: 5.0,
                  accountStatus: 1,
                  accountType: 8,
                },
                {
                  pointAccountNo: '************',
                  frozenPointAmount: 0.0,
                  availablePointAmount: 8.0,
                  accountStatus: 1,
                  accountType: 13,
                },
              ],
            },
            message: '请求成功',
            errorCode: null,
          },
        });
        if (success) {
          // 获取积分编号
          data.integralAccountNo = data?.loyaltyAccountList?.find(item => item.accountType == 8)?.pointAccountNo || '';
          // 获取优惠金编号
          data.discountAccountNo = data?.loyaltyAccountList?.find(item => item.accountType == 13)?.pointAccountNo || '';
          commit('setWalletInfo', data);
          return data;
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  getters: {},
};
