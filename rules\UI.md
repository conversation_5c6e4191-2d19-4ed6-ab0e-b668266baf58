---
description: UniApp + uView UI + 自研组件库 UI设计规范与组件使用指南
globs: *.vue,*.scss,*.js,uni.scss
alwaysApply: true
---

### UniApp UI 开发规范

#### 总体描述
本文档定义了基于UniApp + uView UI + @petro-soti/foudation-zyzx的UI开发规范，涵盖布局规范、色彩规范、字体规范、组件使用规范等内容，确保跨平台UI的一致性和可维护性。

#### 应用范围
本规范适用于所有UniApp项目的UI开发，包括页面布局、组件使用、样式编写等场景。

#### 使用要求
开发前端UI时需严格遵循本规范，确保跨平台兼容性、一致性和可维护性。优先使用uView UI组件，其次使用自研组件库，最后考虑自定义组件。

## 规则1 布局规范

### 整体布局结构
基于UniApp的跨平台特性，定义标准的页面布局结构。

#### 应用范围
适用于所有页面的布局设计，包括主包页面和分包页面。

#### 使用要求
严格按照UniApp的view容器和flex布局进行页面结构设计。

#### 标准页面布局

```vue
<template>
  <view class="page-container">
    <!-- 自定义导航栏 (navigationStyle: custom) -->
    <petro-navbar
      :title="pageTitle"
      :show-back="true"
      @back="handleBack"
    />

    <!-- 页面主体内容 -->
    <view class="page-content">
      <!-- 页面头部信息区 -->
      <view class="page-header" v-if="showHeader">
        <view class="header-title">{{ headerTitle }}</view>
        <view class="header-desc">{{ headerDesc }}</view>
      </view>

      <!-- 页面主要内容区 -->
      <view class="page-main">
        <!-- 内容区域 -->
        <scroll-view
          class="scroll-content"
          scroll-y
          @scrolltolower="onReachBottom"
          @refresherrefresh="onPullDownRefresh"
          :refresher-enabled="enableRefresh"
        >
          <!-- 具体页面内容 -->
          <slot></slot>

          <!-- 加载更多 -->
          <u-loadmore
            v-if="showLoadMore"
            :status="loadMoreStatus"
            @loadmore="loadMore"
          />
        </scroll-view>
      </view>
    </view>

    <!-- 页面底部操作区 -->
    <view class="page-footer" v-if="showFooter">
      <u-button
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        {{ submitText }}
      </u-button>
    </view>

    <!-- 全局加载组件 -->
    <petro-layout :loading="pageLoading" />
  </view>
</template>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7fb;
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  padding: 32rpx;
  background-color: #ffffff;

  .header-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16rpx;
  }

  .header-desc {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.page-main {
  flex: 1;
  overflow: hidden;
}

.scroll-content {
  height: 100%;
}

.page-footer {
  padding: 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
}
</style>
```

### 列表页面布局
```vue
<template>
  <view class="list-page">
    <!-- 搜索筛选区 -->
    <view class="search-section">
      <u-search
        v-model="searchKeyword"
        placeholder="请输入搜索关键词"
        @search="handleSearch"
        @clear="handleClear"
      />

      <!-- 筛选条件 -->
      <view class="filter-bar">
        <u-tabs
          :list="filterTabs"
          v-model="activeFilter"
          @change="handleFilterChange"
        />
      </view>
    </view>

    <!-- 列表内容区 -->
    <view class="list-content">
      <scroll-view
        class="list-scroll"
        scroll-y
        @scrolltolower="loadMore"
        :refresher-enabled="true"
        @refresherrefresh="onRefresh"
      >
        <!-- 列表项 -->
        <view
          class="list-item"
          v-for="(item, index) in dataList"
          :key="item.id || index"
          @click="handleItemClick(item)"
        >
          <!-- 列表项内容 -->
          <slot name="item" :item="item" :index="index">
            <!-- 默认列表项样式 -->
            <view class="item-content">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-desc">{{ item.description }}</view>
              <view class="item-meta">
                <text class="meta-time">{{ item.createTime }}</text>
                <text class="meta-status">{{ item.statusText }}</text>
              </view>
            </view>
          </slot>
        </view>

        <!-- 空状态 -->
        <u-empty
          v-if="!loading && dataList.length === 0"
          text="暂无数据"
          mode="list"
        />

        <!-- 加载更多 -->
        <u-loadmore
          :status="loadMoreStatus"
          @loadmore="loadMore"
        />
      </scroll-view>
    </view>
  </view>
</template>
```

## 规则2 色彩规范

### 主色调系统
基于中石油企业色彩，定义统一的色彩规范。

#### 应用范围
适用于所有UI元素的色彩设计，包括背景色、文字色、边框色等。

#### 使用要求
严格按照色彩规范使用颜色，确保品牌一致性。

#### 色彩定义

```scss
// src/uni.scss - 全局色彩变量

// 主色调 - 中石油红
$primary-color: #E60012;
$primary-light: #FF3333;
$primary-dark: #CC0010;

// 辅助色
$secondary-color: #1890FF;
$success-color: #52C41A;
$warning-color: #FAAD14;
$error-color: #F5222D;
$info-color: #1890FF;

// 中性色
$text-primary: #333333;      // 主要文字
$text-secondary: #666666;    // 次要文字
$text-placeholder: #999999;  // 占位文字
$text-disabled: #CCCCCC;     // 禁用文字

// 背景色
$bg-primary: #FFFFFF;        // 主背景
$bg-secondary: #F7F7FB;      // 次背景
$bg-tertiary: #F0F0F0;       // 三级背景

// 边框色
$border-primary: #E6E6E6;    // 主边框
$border-secondary: #F0F0F0;  // 次边框
$border-light: #FAFAFA;      // 浅边框

// 功能色
$link-color: #1890FF;        // 链接色
$mask-color: rgba(0, 0, 0, 0.5); // 遮罩色
```

#### 色彩使用示例

```vue
<template>
  <view class="color-demo">
    <!-- 主色调使用 -->
    <u-button type="primary" color="#E60012">主要按钮</u-button>

    <!-- 状态色使用 -->
    <u-tag type="success">成功状态</u-tag>
    <u-tag type="warning">警告状态</u-tag>
    <u-tag type="error">错误状态</u-tag>

    <!-- 文字色使用 -->
    <view class="text-primary">主要文字内容</view>
    <view class="text-secondary">次要文字内容</view>
    <view class="text-placeholder">占位文字内容</view>
  </view>
</template>

<style scoped lang="scss">
.color-demo {
  padding: 32rpx;

  .text-primary {
    color: $text-primary;
    font-size: 32rpx;
    margin-bottom: 16rpx;
  }

  .text-secondary {
    color: $text-secondary;
    font-size: 28rpx;
    margin-bottom: 16rpx;
  }

  .text-placeholder {
    color: $text-placeholder;
    font-size: 28rpx;
  }
}
</style>
```

## 规则3 字体规范

### 字体大小系统
基于UniApp的rpx单位，定义响应式字体大小规范。

#### 应用范围
适用于所有文字内容的字体大小设计。

#### 使用要求
使用rpx单位确保跨设备适配，严格按照字体层级使用。

#### 字体大小定义

```scss
// src/uni.scss - 全局字体变量

// 标题字体
$font-size-h1: 48rpx;        // 一级标题
$font-size-h2: 40rpx;        // 二级标题
$font-size-h3: 36rpx;        // 三级标题
$font-size-h4: 32rpx;        // 四级标题

// 正文字体
$font-size-large: 32rpx;     // 大号正文
$font-size-base: 28rpx;      // 基础正文
$font-size-small: 24rpx;     // 小号正文
$font-size-mini: 20rpx;      // 迷你字体

// 字体权重
$font-weight-light: 300;     // 细体
$font-weight-normal: 400;    // 常规
$font-weight-medium: 500;    // 中等
$font-weight-bold: 600;      // 粗体

// 行高
$line-height-tight: 1.2;     // 紧凑行高
$line-height-base: 1.5;      // 基础行高
$line-height-loose: 1.8;     // 宽松行高
```

#### 字体使用示例

```vue
<template>
  <view class="typography-demo">
    <!-- 标题层级 -->
    <view class="title-h1">一级标题 48rpx</view>
    <view class="title-h2">二级标题 40rpx</view>
    <view class="title-h3">三级标题 36rpx</view>
    <view class="title-h4">四级标题 32rpx</view>

    <!-- 正文层级 -->
    <view class="text-large">大号正文 32rpx</view>
    <view class="text-base">基础正文 28rpx</view>
    <view class="text-small">小号正文 24rpx</view>
    <view class="text-mini">迷你字体 20rpx</view>

    <!-- 特殊样式 -->
    <view class="text-bold">粗体文字</view>
    <view class="text-medium">中等粗细</view>
  </view>
</template>

<style scoped lang="scss">
.typography-demo {
  padding: 32rpx;

  .title-h1 {
    font-size: $font-size-h1;
    font-weight: $font-weight-bold;
    color: $text-primary;
    line-height: $line-height-tight;
    margin-bottom: 24rpx;
  }

  .title-h2 {
    font-size: $font-size-h2;
    font-weight: $font-weight-bold;
    color: $text-primary;
    line-height: $line-height-tight;
    margin-bottom: 20rpx;
  }

  .title-h3 {
    font-size: $font-size-h3;
    font-weight: $font-weight-medium;
    color: $text-primary;
    line-height: $line-height-base;
    margin-bottom: 16rpx;
  }

  .text-base {
    font-size: $font-size-base;
    font-weight: $font-weight-normal;
    color: $text-primary;
    line-height: $line-height-base;
    margin-bottom: 16rpx;
  }

  .text-small {
    font-size: $font-size-small;
    color: $text-secondary;
    line-height: $line-height-base;
  }
}
</style>
```

## 规则4 组件使用规范

### uView UI 组件库使用
uView UI是UniApp生态最优秀的UI框架，提供丰富的组件和工具。

#### 应用范围
适用于所有基础UI组件的使用，包括按钮、输入框、列表等。

#### 使用要求
优先使用uView UI组件，通过easycom自动引入，使用u-前缀。

#### 常用组件使用示例

```vue
<template>
  <view class="uview-demo">
    <!-- 按钮组件 -->
    <view class="demo-section">
      <view class="section-title">按钮组件</view>
      <view class="button-group">
        <u-button type="primary" size="large">主要按钮</u-button>
        <u-button type="success" size="normal">成功按钮</u-button>
        <u-button type="warning" size="small">警告按钮</u-button>
        <u-button type="error" plain>错误按钮</u-button>
        <u-button loading loadingText="加载中">加载按钮</u-button>
      </view>
    </view>

    <!-- 表单组件 -->
    <view class="demo-section">
      <view class="section-title">表单组件</view>
      <u-form :model="formData" ref="form">
        <u-form-item label="用户名" prop="username" required>
          <u-input
            v-model="formData.username"
            placeholder="请输入用户名"
            clearable
          />
        </u-form-item>

        <u-form-item label="手机号" prop="mobile" required>
          <u-input
            v-model="formData.mobile"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
          />
        </u-form-item>

        <u-form-item label="性别" prop="gender">
          <u-radio-group v-model="formData.gender">
            <u-radio name="male" label="男"></u-radio>
            <u-radio name="female" label="女"></u-radio>
          </u-radio-group>
        </u-form-item>

        <u-form-item label="爱好" prop="hobbies">
          <u-checkbox-group v-model="formData.hobbies">
            <u-checkbox name="reading" label="阅读"></u-checkbox>
            <u-checkbox name="music" label="音乐"></u-checkbox>
            <u-checkbox name="sports" label="运动"></u-checkbox>
          </u-checkbox-group>
        </u-form-item>
      </u-form>
    </view>

    <!-- 反馈组件 -->
    <view class="demo-section">
      <view class="section-title">反馈组件</view>
      <view class="feedback-group">
        <u-button @click="showToast">显示提示</u-button>
        <u-button @click="showModal">显示弹窗</u-button>
        <u-button @click="showActionSheet">显示操作菜单</u-button>
      </view>
    </view>

    <!-- 展示组件 -->
    <view class="demo-section">
      <view class="section-title">展示组件</view>

      <!-- 标签 -->
      <view class="tag-group">
        <u-tag text="默认标签" />
        <u-tag text="主要标签" type="primary" />
        <u-tag text="成功标签" type="success" />
        <u-tag text="警告标签" type="warning" />
        <u-tag text="错误标签" type="error" />
      </view>

      <!-- 徽章 -->
      <view class="badge-group">
        <u-badge :count="5">
          <u-button>消息</u-button>
        </u-badge>
        <u-badge dot>
          <u-button>通知</u-button>
        </u-badge>
      </view>

      <!-- 进度条 -->
      <u-line-progress
        :percent="progressPercent"
        :show-percent="true"
        active-color="#E60012"
      />
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        username: '',
        mobile: '',
        gender: 'male',
        hobbies: []
      },
      progressPercent: 60
    };
  },

  methods: {
    showToast() {
      this.$u.toast('这是一个提示消息');
    },

    showModal() {
      this.$u.modal({
        title: '提示',
        content: '这是一个确认弹窗',
        showCancelButton: true
      }).then(res => {
        if (res.confirm) {
          this.$u.toast('点击了确认');
        }
      });
    },

    showActionSheet() {
      this.$u.actionSheet({
        list: [
          { text: '选项一' },
          { text: '选项二' },
          { text: '选项三' }
        ]
      }).then(res => {
        this.$u.toast(`选择了${res.text}`);
      });
    }
  }
};
</script>
```

### 自研组件库使用
@petro-soti/foudation-zyzx是中石油自研的组件库，提供业务特定的组件。

#### 组件前缀规范
- **petro-前缀**: 通用业务组件
- **zyzx-前缀**: 专用业务组件

#### 常用自研组件示例

```vue
<template>
  <view class="petro-demo">
    <!-- 布局组件 -->
    <petro-layout :loading="pageLoading">
      <!-- 导航栏组件 -->
      <petro-navbar
        title="页面标题"
        :show-back="true"
        :show-home="true"
        @back="handleBack"
        @home="handleHome"
      />

      <!-- 内容区域 -->
      <view class="content">
        <!-- 卡片组件 -->
        <petro-card title="卡片标题" :border="true">
          <view class="card-content">
            卡片内容区域
          </view>
        </petro-card>

        <!-- 列表组件 -->
        <petro-list
          :data="listData"
          :loading="listLoading"
          @item-click="handleItemClick"
          @load-more="loadMore"
        >
          <template #item="{ item }">
            <view class="list-item-custom">
              <text>{{ item.title }}</text>
            </view>
          </template>
        </petro-list>

        <!-- 表单组件 -->
        <petro-form
          :model="formData"
          :rules="formRules"
          ref="petroForm"
        >
          <petro-form-item label="企业名称" prop="companyName">
            <petro-input
              v-model="formData.companyName"
              placeholder="请输入企业名称"
            />
          </petro-form-item>

          <petro-form-item label="车牌号" prop="carNumber">
            <petro-input
              v-model="formData.carNumber"
              placeholder="请输入车牌号"
              type="car-number"
            />
          </petro-form-item>
        </petro-form>

        <!-- 业务特定组件 -->
        <zyzx-oil-price
          :price-list="oilPriceList"
          @price-select="handlePriceSelect"
        />

        <zyzx-payment-method
          v-model="selectedPayment"
          :methods="paymentMethods"
        />
      </view>
    </petro-layout>
  </view>
</template>

<script>
export default {
  data() {
    return {
      pageLoading: false,
      listLoading: false,
      listData: [],
      formData: {
        companyName: '',
        carNumber: ''
      },
      formRules: {
        companyName: [
          { required: true, message: '请输入企业名称' }
        ],
        carNumber: [
          { required: true, message: '请输入车牌号' },
          { pattern: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$/, message: '车牌号格式不正确' }
        ]
      },
      oilPriceList: [],
      paymentMethods: [],
      selectedPayment: ''
    };
  },

  methods: {
    handleBack() {
      uni.navigateBack();
    },

    handleHome() {
      uni.switchTab({ url: '/pages/index/index' });
    },

    handleItemClick(item) {
      console.log('点击列表项:', item);
    },

    loadMore() {
      // 加载更多数据
    },

    handlePriceSelect(price) {
      console.log('选择油价:', price);
    }
  }
};
</script>
```

## 规则5 响应式设计规范

### 屏幕适配
基于UniApp的rpx单位实现响应式设计。

#### 应用范围
适用于所有需要跨设备适配的UI元素。

#### 使用要求
使用rpx单位进行尺寸设计，确保在不同屏幕尺寸下的一致性。

#### 响应式布局示例

```vue
<template>
  <view class="responsive-layout">
    <!-- 栅格布局 -->
    <u-row gutter="16">
      <u-col span="12">
        <view class="grid-item">左侧内容</view>
      </u-col>
      <u-col span="12">
        <view class="grid-item">右侧内容</view>
      </u-col>
    </u-row>

    <!-- 弹性布局 -->
    <view class="flex-container">
      <view class="flex-item flex-1">弹性项目1</view>
      <view class="flex-item flex-2">弹性项目2</view>
      <view class="flex-item flex-1">弹性项目3</view>
    </view>

    <!-- 媒体查询适配 -->
    <view class="adaptive-content">
      <view class="content-item" v-for="item in contentList" :key="item.id">
        {{ item.title }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.responsive-layout {
  padding: 32rpx;
}

.grid-item {
  height: 200rpx;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.flex-container {
  display: flex;
  margin-top: 32rpx;
  gap: 16rpx;
}

.flex-item {
  height: 120rpx;
  background-color: #e6f7ff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;

  &.flex-1 {
    flex: 1;
  }

  &.flex-2 {
    flex: 2;
  }
}

.adaptive-content {
  margin-top: 32rpx;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 16rpx;
}

.content-item {
  padding: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

// 小屏幕适配
@media screen and (max-width: 600rpx) {
  .adaptive-content {
    grid-template-columns: 1fr;
  }
}
</style>
```

### 平台差异化处理
针对不同平台的特殊处理。

```vue
<template>
  <view class="platform-adaptive">
    <!-- 条件编译 -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="weixin-specific">微信小程序特有内容</view>
    <!-- #endif -->

    <!-- #ifdef MP-ALIPAY -->
    <view class="alipay-specific">支付宝小程序特有内容</view>
    <!-- #endif -->

    <!-- #ifdef H5 -->
    <view class="h5-specific">H5特有内容</view>
    <!-- #endif -->

    <!-- 动态平台判断 -->
    <view v-if="isWeixin" class="dynamic-weixin">动态微信内容</view>
    <view v-if="isAlipay" class="dynamic-alipay">动态支付宝内容</view>
  </view>
</template>

<script>
export default {
  computed: {
    isWeixin() {
      // #ifdef MP-WEIXIN
      return true;
      // #endif
      return false;
    },

    isAlipay() {
      // #ifdef MP-ALIPAY
      return true;
      // #endif
      return false;
    }
  }
};
</script>

<style scoped lang="scss">
.platform-adaptive {
  padding: 32rpx;
}

// 平台特定样式
/* #ifdef MP-WEIXIN */
.weixin-specific {
  background-color: #07c160;
  color: white;
}
/* #endif */

/* #ifdef MP-ALIPAY */
.alipay-specific {
  background-color: #1677ff;
  color: white;
}
/* #endif */

/* #ifdef H5 */
.h5-specific {
  background-color: #722ed1;
  color: white;
}
/* #endif */
</style>
```

## 规则6 性能优化规范

### 图片优化
合理使用图片资源，优化加载性能。

```vue
<template>
  <view class="image-optimization">
    <!-- 懒加载图片 -->
    <u-lazy-load
      :src="imageUrl"
      :loading-src="loadingImage"
      :error-src="errorImage"
      width="300rpx"
      height="200rpx"
    />

    <!-- 响应式图片 -->
    <image
      :src="responsiveImageUrl"
      mode="aspectFit"
      lazy-load
      @load="onImageLoad"
      @error="onImageError"
    />

    <!-- 图片预览 -->
    <u-album
      :urls="albumUrls"
      :show-menu="true"
      @click="previewImage"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      imageUrl: '',
      loadingImage: '/static/images/loading.png',
      errorImage: '/static/images/error.png',
      albumUrls: []
    };
  },

  computed: {
    responsiveImageUrl() {
      // 根据设备像素比选择合适的图片
      const dpr = uni.getSystemInfoSync().pixelRatio;
      if (dpr >= 3) {
        return this.imageUrl + '@3x';
      } else if (dpr >= 2) {
        return this.imageUrl + '@2x';
      }
      return this.imageUrl;
    }
  },

  methods: {
    onImageLoad() {
      console.log('图片加载成功');
    },

    onImageError() {
      console.log('图片加载失败');
    },

    previewImage(index) {
      uni.previewImage({
        urls: this.albumUrls,
        current: index
      });
    }
  }
};
</script>
```

### 规则5 间距规范

- **基础间距单位**

  ```less
  --space-xs: 4px;    
  --space-sm: 8px;   
  ```

- **页面间距规则**

  - **模块间距**: 16px (查询区域与表格区域)
  - **卡片内边距**: 24px

### 规则6 表单规范

- **表单布局**

  ```less
  // 查询表单规范
  interface QueryFormRules {
    layout: 'horizontal';         
      ...
  }
  ```

- **表单控件**

  - **输入框**: 高度32px，圆角4px
  - **选择器**: 与输入框保持一致的尺寸

- **表单验证**

  - 实时验证，错误提示明确
  - 必填项标红星号标识

### 规则7 表格规范

- **表格基础样式**

  ```less
  interface TableStandards {
    fontSize: '14px';            
    headerBackground: '#fafafa';            
         ...     
  }
  ```

- **表格功能规范**

  - **分页**: 默认每页10条，支持10/20/50/100条切换
  - **排序**: 支持列排序，显示排序图标

- **表格状态**

  - **加载状态**: 显示Loading效果
  - **空数据**: 显示"暂无数据"提示

### 规则8 按钮规范

- **按钮类型和用途**

  ```typescript
  interface ButtonTypes {
    primary: '主要操作按钮';        
    text: '文本按钮';  
      ...
  }
  ```

- **按钮尺寸**

  - **小按钮**: 高度28px，用于表格操作

- **按钮状态**

  - **常规态**: 默认样式
  - **悬停态**: 背景色加深

### 规则9 图标规范

- **图标库**
  - 主要使用 `@vicons/ionicons5`
  - 自定义图标存放在 `xxx/icons/`
- **图标使用原则**
  - 保持图标风格一致
  - 合理使用图标语义

### 规则10 交互规范

- **反馈机制**

  比如**成功操作**:

- **加载状态**

  比如**页面加载**

- **动画效果**

  比如**页面切换**