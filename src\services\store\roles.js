export default {
  state: {
    role: {
      id: NaN,
      avatar: '',
      name: '',
      remark: '',
      code: '',
      opened: true,
      defaultFlag: 0,
      businessNo: '',
      roleType: '',
      roleBusinessType: '',
      roleDescribe: 'manager',
    },
    userInfo: {
      mobile: '',
      name: '',
      businessRegionName: '',
      staffNo: '',
      // petroChinaNo: '', // 档案编号 - 统一从tokenInfo中获取
      identityNo: '',
      shortName: '', // 短名称
      roleDescribe: '', // 角色描述
      splitterName: '', // 姓名脱敏
    },
    tokenInfo: {
      isAgreePrivacy: false,
      tinyAppType: '',
      accessToken: '',
      refreshToken: '',
      gsmsToken: '',
      userId: '',
      orgCode: '', // 机构编号
      petroChinaNo: '', // 档案编号
      fleetType: 1, // 车队类型:1-普通 2-军队
      isDriver: false,
    },
    roles: [],
    rolesMap: [],
    rolesUnion: [], // 角色并集集合
  },
  mutations: {
    selectRole(state, role) {
      console.log('初始化设置role----', role);
      const { config } = uni.$petro;
      const { mPaasMap } = uni.$petro.config;
      const { appId = '' } = mPaasMap?.[role?.code] || {};
      if (role?.autoCheck && !!appId && appId !== config.mPaas.mPaaSId) {
        uni.$petro.switchTinyApp(role);
        return;
      }
      Object.assign(state.role, role);
    },
    setRoles(state, roles) {
      const shortName = (name = '') => {
        return (
          name.substring(0, name.lastIndexOf('管理人')) ||
          name.substring(0, name.lastIndexOf('业务联系人')) ||
          name.substring(0, name.lastIndexOf('联系人')) ||
          name.substring(0, 2)
        ).substring(0, 4);
      };
      roles.forEach(v => {
        if (!v.shortName) v.shortName = shortName(v.name);
        v.roleDescribe = uni.$petro.Enum?.MEMBER_ROLES_CODE_DESCRIBE[v?.code] || '';
      });
      console.log('初始化roles----', JSON.stringify(roles));
      this.commit('selectRole', { ...roles.find(v => v.defaultFlag === 1), autoCheck: true });
      Object.assign(state.roles, roles);
    },
    setRolesMap(state, rolesMap) {
      Object.assign(state.rolesMap, rolesMap);
    },
    setRolesUnion(state, rolesUnion) {
      Object.assign(state.rolesUnion, rolesUnion);
    },
    setUserInfo(state, userInfo) {
      const shortName = (name = '') => {
        return name.length >= 3 ? name.substring(1, name.length) : name;
      };
      if (!userInfo.shortName) userInfo.shortName = shortName(userInfo.name);
      Object.assign(state.userInfo, userInfo);
    },
    setTokenInfo(state, tokenInfo) {
      Object.assign(state.tokenInfo, tokenInfo);
    },
  },
  actions: {
    async getTokenInfo({ commit, dispatch, state, rootState }, payload = false) {
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.tokenInfo?.accessToken) return state.tokenInfo;
      const tokenInfo = await uni.$petro.getTokenInfo();
      commit('setTokenInfo', tokenInfo);
      return tokenInfo;
    },
    async getRoles({ commit, dispatch, state, rootState }, payload = false) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.roles.length > 0) return state.roles;
      try {
        const { success, data = [] } = await uni.$petro.http(
          'user.role.list.h5',
          {},
          {
            mockResponse: {
              success: true,
              data: [
                {
                  id: 5,
                  defaultFlag: 1,
                  name: '车队卡业务联系人',
                  code: '2-10',
                  remark: '企业账户、车辆及司机信息管理',
                  businessNo: '****************',
                },
                {
                  id: 6,
                  defaultFlag: 0,
                  name: '车队卡司机',
                  code: '4-10',
                  remark: '使用企业账户进行油费管理',
                  businessNo: '****************',
                },
              ],
              message: '请求成功',
              errorCode: null,
            },
          },
        );
        if (success) {
          data.forEach(v => {
            v.roles = v.code?.split('-');
            v.roleType = v.roles?.[0];
            if (v.roles.length >= 2) v.roleBusinessType = v.roles?.[1];
            Object.assign(v, uni.$petro.Enum.ROLES_DESC[v.code]);
          });
          commit('setRoles', data);
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return [];
    },
    async getRolesMap({ commit, dispatch, state, rootState }, payload = false) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      try {
        let { success, data = {} } = await uni.$petro.http(
          'user.business.queryEnterpriseBusinessList.h5',
          {
            // queryType: 4, // 拿所有的
            queryType: 5, // v430拿所有的
            enterpriseNo: orgCode,
          },
          {
            mockResponse: {
              success: true,
              data: [
                {
                  id: null,
                  enterpriseAccountNo: '************',
                  mainAccountNo: '************',
                  enterpriseNo: '****************',
                  enterpriseName: '长沙市岳麓区林雀子饭店',
                  userId: '469',
                  unitAlias: '长沙市岳麓区林雀子饭店',
                  accountType: 1,
                  businessNo: '****************',
                  businessType: 10,
                  invoiceType: 1,
                  parentEnterpriseNo: null,
                  parentEnterpriseName: null,
                  parentContactName: null,
                  parentMainAccountNo: null,
                  inviteType: null,
                  accountStatus: 1,
                  role: 2,
                  staffNo: '*****************',
                },
                {
                  id: ************,
                  enterpriseAccountNo: null,
                  mainAccountNo: null,
                  enterpriseNo: '****************',
                  enterpriseName: '长沙市岳麓区林雀子饭店',
                  userId: '469',
                  unitAlias: null,
                  accountType: null,
                  businessNo: '****************',
                  businessType: 10,
                  invoiceType: 1,
                  parentEnterpriseNo: '****************',
                  parentEnterpriseName: '长沙市岳麓区林雀子饭店',
                  parentContactName: null,
                  parentMainAccountNo: '************',
                  inviteType: 1,
                  accountStatus: null,
                  role: 4,
                  staffNo: '*****************',
                },
              ],
              message: '请求成功',
              errorCode: null,
            },
          },
        );
        if (success) {
          const driverList = (data || []).filter(item => item.role === 4 && item.accountStatus !== 3); // 未注销司机列表
          const driverActivatedList = driverList.filter(item => [1, 2].includes(item.accountStatus)); // 已激活/冻结司机列表
          if (!driverList?.length) {
            uni.$petro.route({ url: '/pages/none/none', type: 'redirectTo' });
            return 'none';
          }
          if (!driverActivatedList?.length) {
            uni.$petro.route({ url: '/pages/todo/todo', params: { isInit: 1 }, type: 'redirectTo' });
            return 'none';
          }
          // 存储用户信息
          await uni.$petro.setTokenInfo({
            isDriver: true,
          });
          commit('setTokenInfo', {
            isDriver: true,
          });

          let unionAll = [];
          data.map(v => {
            v.code = v.role + '-' + v.businessType;
            v.roleType = v.role;
            v.roleBusinessType = v.businessType;
            Object.assign(v, uni.$petro.Enum.ROLES_DESC[v.code]);
          });
          // unionAll.push(...data);
          unionAll = data.filter(item => item.accountType !== 4);

          // // 当前企业所有角色
          // const nowList = unionAll.filter(v => v.enterpriseNo === orgCode);
          //
          // // 非当前企业其他角色
          // const notNowListAndOther = unionAll.filter(v => v.enterpriseNo !== orgCode && !nowList.find(vv => vv.code === v.code));
          //
          // // 合并数据
          // let union = [...nowList, ...notNowListAndOther];
          commit('setRolesUnion', unionAll);
          commit('setRolesMap', data);
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return [];
    },
    async selectRole({ commit, dispatch, state, rootState }, role) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (state.role?.code === role?.code) {
        return role;
      }
      try {
        const { success, data } = await uni.$petro.http(
          'user.role.switch.h5',
          {
            roleId: role?.id,
            orgCode: orgCode,
          },
          {
            mockResponse: {
              success: true,
              message: '请求成功',
              errorCode: '0',
              data: {},
            },
          },
        );
        if (success) {
          commit('selectRole', role);
          return role;
        }
      } catch (err) {
        console.error(err);
      }
      return null;
    },
    async getUserInfo({ commit, dispatch, state, rootState }, payload = false) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.userInfo?.mobile) return state.userInfo;
      if (!state.role?.businessNo) return {};
      try {
        const { success, data } = await uni.$petro.http(
          'user.getUserDetailInfo.h5',
          {
            businessNo: state.role?.businessNo,
            type: 1,
          },
          {
            mockResponse: {
              success: true,
              data: {
                staffNo: '*****************',
                petroChinaNo: '35116154673278',
                memberType: 2,
                name: '雷天伟',
                identityNo: '511621********4177',
                businessRegionName: '长沙市',
                mobile: '183****0904',
              },
              message: '请求成功',
              errorCode: null,
            },
          },
        );
        if (success) {
          data.splitterName = uni.$petro.Utils.splitterName(data?.name);

          commit('setUserInfo', data);
          uni.$petro.PayPlugin.init();
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return {};
    },
  },
  getters: {},
};
