<template>
  <view class="car-details-page">
    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-car-manage ref="zyzxPageCarManage" type="2" v-if="queryParams.id" :queryParams="queryParams" />
    </petro-layout>
  </view>
</template>

<script>
// import zyzxPageCarManage from '@/components/zyzx-page-car-manage/zyzx-page-car-manage.vue';

export default {
  // components: { zyzxPageCarManage },
  data() {
    return {
      queryParams: {
        id: '',
        licensePlate: '',
      },
    };
  },
  onLoad(options) {
    let queryInfo = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
    this.queryParams = {
      id: queryInfo.id,
      licensePlate: queryInfo.licensePlate,
    };
    console.log('🚀 ~ onLoad ~ this.queryParams:', this.queryParams);
  },
  onShow() {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.car-details-page {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #f0f1f5;
}
</style>
