<template>
  <div class="page-container">
    <u-navbar :title="'站内信'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div style="width: 100%; height: 200rpx; background: #ddd"></div>
        <p-map :mapId="'map3'"></p-map>
        <div style="width: 100%; height: 200rpx; background: #ddd"></div>
        <!-- <u-empty mode="data" marginTop="200" textSize="24" iconSize="120" text="暂无信息"> </u-empty> -->

        <div class="test">
          <button @click="toPage('/pages/verification/main')">实人认证</button>
          <button @click="toPage('/pages/account-open/main')">开通账户</button>
          <button @click="testGetTokenInfo()">getTokenInfo</button>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {};
  },
  onLoad(query) {},
  computed: {},
  methods: {
    // 测试跳转
    toPage(url) {
      uni.$petro.route(url);
    },
    async testGetTokenInfo() {
      const res = await uni.$petro.Bridge.zyzx.getTokenInfo();
      uni.showModal({
        title: '测试获取token',
        content: JSON.stringify(res),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
