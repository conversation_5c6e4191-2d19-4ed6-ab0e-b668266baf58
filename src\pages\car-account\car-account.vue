<template>
  <div class="page-container" :class="{white: !list || !list.length}">
    <u-navbar :title="'车牌卡'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div class="page-car">
          <div class="car-list" v-if="list && list.length">
            <div class="car-item" v-for="(item, index) in list" :key="index">
              <div class="car-item-left">
                <div class="car-item-left-num">{{ item.licencePlate }}</div>
                <div class="car-item-left-type">车牌卡</div>
                <!-- <div class="car-item-left-type">{{ CAR_TYPE[item.vehicleType] }}</div>
                <div class="car-item-left-tag">{{ FILL_TYPE[item.fillType] }}</div> -->
              </div>
              <!-- <div class="car-item-right">
                <u-icon name="arrow-right" color="#666666" size="16" @click="toDetail(item)"></u-icon>
              </div> -->
            </div>
          </div>
          <template v-else>
            <u-empty
              :icon="require('@/static/images/empty-car-account-list.png')"
              marginTop="100"
              textSize="16"
              textColor="#333333"
              width="280"
              height="218"
              text="暂无车牌卡信息"
            >
            </u-empty>
            <p class="empty-text">如需开通请联系管理员</p>
          </template>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { CAR_TYPE, FILL_TYPE } from '@/services/enum';

export default {
  components: {},
  data() {
    return {
      CAR_TYPE: CAR_TYPE,
      FILL_TYPE: FILL_TYPE,
      list: [],
    };
  },
  computed: {
    ...mapState({
      accountInfo: state => state.company?.accountInfo,
    }),
  },
  async onLoad(query) {
    const params = {
      enterpriseAccountNo: this.accountInfo?.enterpriseAccountNo,
      refresh: true,
    };
    const list = await this.$store.dispatch('getWalletList', params);
    // 筛选出车牌账户
    this.list = list.filter(item => item.accountType == 4) || [];
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #F0F1F5;
  display: flex;
  flex-direction: column;
  &.white {
    background: #ffffff;
  }
  .page-content {
    flex: 1;
  }
  .page-car {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .car-list {
      padding: 0 32rpx;
      margin-top: 40rpx;
      .car-item {
        background: #ffffff;
        display: flex;
        align-items: center;
        height: 108rpx;
        padding: 0 32rpx;
        border-radius: 16rpx;
        &:not(:first-child) {
          margin-top: 24rpx;
        }
        &-left {
          flex: 1;
          display: flex;
          align-items: center;
          &-num {
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            line-height: 44rpx;
          }
          &-type {
            margin-left: 16rpx;
            padding: 0 12rpx;
            height: 38rpx;
            background: #EBF0FF;
            border-radius: 4rpx;
            border: 1rpx solid #3C61CB;
            font-size: 24rpx;
            color: #3C61CB;
            line-height: 38rpx;
            box-sizing: border-box;
          }
          &-tag {
            margin-left: 16rpx;
            padding: 0 12rpx;
            height: 38rpx;
            background: #f6fffe;
            border-radius: 4rpx;
            border: 1rpx solid #1bc3b9;
            font-size: 24rpx;
            color: #1bc3b9;
            line-height: 38rpx;
            box-sizing: border-box;
          }
        }
        &-right {
          flex-shrink: 0;
          padding: 20rpx 0 20rpx 60rpx;
        }
      }
    }
  }
  .empty-text {
    margin-top: 12rpx;
    font-size: 20rpx;
    color: #999999;
    line-height: 44rpx;
    text-align: center;
  }
}
</style>
