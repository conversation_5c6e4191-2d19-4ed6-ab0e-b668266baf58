<template>
  <div class="page-station-list" :style="{ paddingBottom: pageChannel === 'station-list' ? '32rpx' : '0rpx' }">
    <div class="header">
      <div class="search-box">
        <div class="search-el">
          <image class="icon" src="@/static/images/icon-station-01.png"></image>
          <input class="input" v-model="searchText" placeholder-class="search-input-placeholder" type="text" placeholder="输入搜索内容" />
          <div class="btn" @click="searchClick">搜索</div>
          <div class="input-pop" v-if="addressList && addressList.length > 0 && searchText && searchText.length > 0">
            <div v-for="item in addressList" :key="item.uid" class="address-search-item" @click="handleClickAddress(item)">
              <span class="search-item-location">{{ item.name }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="type-list">
        <div
          class="type-item"
          :class="{ actived: typeActive === item.code }"
          v-for="(item, index) in typeList"
          :key="index"
          @click="onTypeChange(item)"
          >{{ item.name }}</div
        >
      </div>
    </div>
    <div class="content">
      <zyzx-data-list
        ref="dataList"
        :safeAreaInsetBottom="true"
        :showEmpty="showEmpty"
        :emptyImage="require('@/static/images/empty-order-list.png')"
        emptyText="未查询到油站"
        :widthImg="180"
        :heightImg="120"
        @refreshPullDown="refreshPullDown"
        @scrolltolower="scrolltolower"
        :isLoad="!!(dataList && dataList.length)"
      >
        <div class="station-list">
          <div class="station-item" v-for="(item, index) in dataList" :key="index" @click="onSelectStataion(item)">
            <div class="item-content">
              <div class="name-row">
                <image src="@/static/images/station-list-logo.png"></image>
                <div>{{ item.orgName }}</div>
                <u-icon name="arrow-right" color="#949494" size="15"></u-icon>
              </div>
              <div class="info-box">
                <div class="info-left">
                  <div class="location-row">
                    <div class="location">{{ item.address }}</div>
                  </div>
                  <div class="tag-row" v-if="pageChannel == 'navigation' && item.tagList.length">
                    <div v-for="(tag, tagIndex) in item.tagList" :key="tagIndex">{{ tag }}</div>
                  </div>
                  <div class="other-row">
                    <div class="distance">{{ item.distanceInKm || '' }}km</div>
                    <template v-if="item.businessHoursList && item.businessHoursList.length">
                      <span v-for="(el, elIndex) in item.businessHoursList" :class="'time' + elIndex"
                        >{{ timeSplit(el.startTime) }}-{{ timeSplit(el.endTime) }}</span
                      >
                    </template>
                    <template v-else>
                      <div class="time-bucket" v-if="item.businessStartTime && item.businessEndTime">
                        {{ timeSplit(item.businessStartTime) }}-{{ timeSplit(item.businessEndTime) }}
                      </div>
                    </template>
                    <div class="phone">电话:{{ item.phone || '' }}</div>
                  </div>
                </div>
                <div class="info-right">
                  <image
                    src="@/static/images/icon-station-navigation-04.png"
                    v-if="pageChannel == 'navigation'"
                    @click.stop="openLocation(item)"
                  ></image>
                  <div class="status color-green" v-if="item.stationStatus == '20' || item.stationStatus == '10'">正常营业</div>
                  <div class="status color-gray" v-if="item.stationStatus == '30' || item.stationStatus == '50'">暂停营业</div>
                </div>
              </div>
            </div>
            <div class="position-tag" v-if="index == 0">
              <image src="@/static/images/station-right-icon.png"></image>
              <div>距离最近</div>
            </div>
          </div>
        </div>
        <!-- <template v-slot:empty>
          <u-empty
            :icon="require('@/static/images/empty-order-list.png')"
            marginTop="0"
            textSize="12"
            textColor="#999999"
            width="174"
            height="119"
            text="未查询到油站"
          >
          </u-empty>
        </template> -->
      </zyzx-data-list>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { getStationServiceListApi, getStationListApi } from '@/services/http';
import { formatDistanceToTwoDecimal } from '@/utils';

export default {
  name: 'page-station-list',
  components: {},
  props: {
    pageChannel: {
      type: String,
      default: 'station-list', // 油站列表(station-list), 导航(navigation)
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      osName: uni.$petro.store.systemInfo.osName,
      location: null,
      typeActive: '',
      typeList: [],
      pageNum: 1,
      pageSize: 10,
      dataList: [],
      showEmpty: false,

      // 搜索
      isListenSearchText: true,
      searchText: '',
      addressList: [],
      destinationLocationInfo: null,
    };
  },
  computed: {
    ...mapState({
      pagePre: state => state.tabBar?.tabBarPermissions.navigation,
    }),
  },
  watch: {
    searchText(curVal, oldVal) {
      if (!this.isListenSearchText) {
        return;
      }
      this.destinationLocationInfo = {};
      if (curVal) {
        let URL = `http://api.map.baidu.com/place/v2/search?query='${this.searchText}'&region=131&output=json&ak=T0VPdIGBUv1w1YgAODQIciaP8CssB588`;
        uni.request({
          url: URL,
          method: 'GET',
          data: {},
          success: response => {
            this.addressList = response.data.results;
          },
          fail: err => {
            this.addressList = [];
          },
        });
      } else {
        this.addressList = [];
        this.$emit('change', {});
      }
    },
    pagePre: {
      handler(newValue, oldValue) {
        if (!oldValue?.localPre && newValue?.localPre) {
          this.getStationList(true);
        }
      },
      deep: true, // 深度监听对象内部属性的变化
    },
  },
  async mounted() {
    await this.stationServiceListPost();
  },
  methods: {
    // 获取油站标识列表
    async stationServiceListPost() {
      try {
        const res = await getStationServiceListApi();
        if (!res || !res.success) return;
        res.data.push({
          name: '98#',
          code: '14',
        });
        this.typeList = res.data;
        if (this.typeList.length > 0) {
          this.typeActive = this.typeList[0].code;
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取油站列表
    async getStationList(reload = false) {
      if (!this.pagePre?.localPre) return;
      if (!this.pagePre?.isDefaultGPS) {
        this.location = await uni.$petro.getLocation({}, true, {
          showModal: false,
        });
      } else {
        this.location = uni.$petro.config.defaultGPS;
      }

      if (reload) {
        this.pageNum = 1;
        this.dataList = [];
        this.$refs.dataList.loadStatus = 'loading';
      }
      try {
        let destinationLongitude = '';
        let destinationLatitude = '';
        if (this.destinationLocationInfo && this.destinationLocationInfo.location) {
          if (this.destinationLocationInfo.location.lng) {
            destinationLongitude = this.destinationLocationInfo.location.lng;
          }
          if (this.destinationLocationInfo.location.lat) {
            destinationLatitude = this.destinationLocationInfo.location.lat;
          }
        }

        // TODO 北京昌平石油基地
        // this.location = {
        //   latitude: '40.16673979857886',
        //   longitude: '116.2376462404155',
        // };

        const params = {
          stationService: this.typeActive,
          distance: 50,
          // bookingRefueling: '1',
          longitude: this.location.longitude || uni.$petro.config.defaultGPS.longitude,
          latitude: this.location.latitude || uni.$petro.config.defaultGPS.latitude,
          destinationLongitude: destinationLongitude,
          destinationLatitude: destinationLatitude,
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          mapType: 0, //地图坐标系标识（0高德，1百度，2腾讯）
        };

        const res = await getStationListApi(params);

        if (!res || !res.success) return;
        const newList = res.data.rows || [];
        newList.forEach(item => {
          item.distanceInKm = formatDistanceToTwoDecimal(item.distance || 0);
        });

        // 当前为第一页则直接赋值, 否则合并新数据
        if (this.pageNum == 1) {
          this.dataList = newList;
        } else {
          this.dataList = this.dataList.concat(newList);
        }

        // 当前页大于等于总页数,则没有更多了。禁用上拉加载更多
        if (this.pageNum >= res.data.pageSum) {
          this.$refs.dataList.loadStatus = 'nomore';
        } else {
          this.$refs.dataList.loadStatus = 'contentdown';
        }

        // 是否展示无数据标识
        if (this.dataList.length == 0) {
          this.showEmpty = true;
        } else {
          this.showEmpty = false;
        }
        this.pageNum++;
      } catch (error) {
        console.log(error);
      }
    },
    // 搜索结果列表中选择油站
    handleClickAddress(item) {
      this.isListenSearchText = false;
      this.searchText = item.name;
      this.$nextTick(() => {
        this.isListenSearchText = true;
        this.destinationLocationInfo = item;
        this.addressList = [];
        this.getStationList(true);
        this.$emit('change', item);
      });
    },
    searchClick() {
      if (this.isCustom) {
        this.$emit('searchClick', this.searchText);
      }
    },
    // 下拉刷新触发
    async refreshPullDown() {
      this.$refs.dataList.loadStatus = 'loading';
      await this.getStationList(true);
      this.$refs.dataList.stopRefresh();
    },
    // 上拉加载更多
    scrolltolower() {
      console.log('上拉加载更多....');
      // 可加载状态
      if (this.$refs.dataList.loadStatus == 'contentdown') {
        this.$refs.dataList.loadStatus = 'loading';
        this.getStationList(false);
      }
    },
    openLocation(item) {
      console.log('openLocation', item);
      uni.$petro.Utils.debounce(
        () => {
          this.selectType(item);
        },
        1000,
        this,
      );
    },
    // 打开导航
    async selectType(item) {
      console.log('selectType', item);
      if (this.pagePre?.isDefaultGPS) {
        uni.showModal({
          title: '定位失败，定位服务未开启，无法定位当前位置，请开启定位服务',
          confirmText: '确认',
          showCancel: false,
        });
        return;
      }

      try {
        const location = await uni.$petro.getLocation({}, true, {
          showModal: false,
        });
        if (!item?.latitude || !item?.longitude) return;
        // uni.$petro.openLocation({
        //   startLat: location.latitude,
        //   startLon: location.longitude,
        //   endLat: item.latitude,
        //   endLon: item.longitude,
        // });
        uni.$petro.route(
          '/pages/car/pages/car-type',
          {
            startLat: location.latitude,
            startLon: location.longitude,
            endLat: item.latitude,
            endLon: item.longitude,
          },
          true,
        );
      } catch (error) {
        console.log(error);
      }
    },
    // 类型选择
    onTypeChange(item) {
      if (item.code === this.typeActive) return;
      this.typeActive = item.code;
      // 刷新列表
      this.getStationList(true);
    },
    // 截取时间字符串
    timeSplit(data) {
      try {
        return data.substring(0, 5);
      } catch (error) {}
    },
    // 选择油站
    onSelectStataion(stationInfo) {
      this.$store.commit('setStationInfo', stationInfo);
      if (this.pageChannel === 'station-list') {
        uni.$petro.route({
          type: 'back',
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.input-pop {
  position: absolute;
  top: 40px;
  left: 0;
  width: 100%;
  height: 270px;
  background: #f7f7fb;
  box-shadow: 0px 2px 3px 0px rgba(192, 187, 187, 0.5);
  border-radius: 0 0 10px 10px;
  z-index: 1000;
  overflow: auto;

  .address-search-item {
    padding: 10px 40px;
    box-sizing: border-box;

    .search-item-location {
      box-sizing: border-box;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
  }
}
.page-station-list {
  width: 100%;
  height: 100%;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
  padding: 32rpx 32rpx 0;
  box-sizing: border-box;
  .header {
    .search-box {
      display: flex;
      align-items: center;
      .search-el {
        flex: 1;
        height: 80rpx;
        padding: 0 28rpx;
        border: 1rpx solid #e7e7e7;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        position: relative;
        background: #ffffff;
        .icon {
          width: 34rpx;
          height: 34rpx;
        }
        .input {
          flex: 1;
          padding-left: 24rpx;
        }
        .input [disabled] {
          color: #333333;
          opacity: 1;
        }
        .btn {
          margin-left: 40rpx;
          position: relative;
          color: #999999;
          font-size: 28rpx;
          &::before {
            content: '';
            width: 2rpx;
            height: 24rpx;
            background: #999999;
            position: absolute;
            left: -30rpx;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }

    .type-list {
      margin-top: 24rpx;
      display: flex;
      overflow-x: auto;
      overflow-y: hidden;
      .type-item {
        flex-shrink: 0;
        padding: 0 30rpx;
        height: 60rpx;
        line-height: 60rpx;
        font-size: 26rpx;
        color: #333333;
        text-align: center;
        box-sizing: border-box;
        border-radius: 8rpx;
        border: 2rpx solid #cdcdcd;

        &.actived {
          color: #fa1919;
          background: #ffefef;
          border: 2rpx solid #fa1919;
        }
        &:not(:last-child) {
          margin-right: 20rpx;
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;
    margin-top: 24rpx;
    .station-list {
      .station-item {
        padding: 20rpx 24rpx;
        background: #fff;
        border-radius: 16rpx;
        display: flex;
        position: relative;
        &:not(:first-child) {
          margin-top: 24rpx;
        }
        .position-tag {
          position: absolute;
          top: 0;
          right: 0;
          width: 186rpx;
          height: 76rpx;
          image {
            width: 186rpx;
            height: 76rpx;
          }
          div {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            text-align: center;
            font-size: 26rpx;
            color: #fa1919;
            line-height: 64rpx;
          }
        }

        .item-content {
          width: 0;
          flex: 1;
          .name-row {
            display: flex;
            align-items: center;
            image {
              width: 28rpx;
              height: 28rpx;
              flex-shrink: 0;
            }
            div {
              margin-left: 8rpx;
              margin-right: 10rpx;
              font-size: 32rpx;
              font-weight: bold;
              color: #333333;
              line-height: 45rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .info-box {
            margin-top: 16rpx;
            display: flex;
            justify-content: space-between;
            .info-left {
              overflow: hidden;
              .location-row {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;

                .location {
                  flex: 1;
                  font-size: 24rpx;
                  color: #333333;
                  line-height: 33rpx;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              .tag-row {
                margin-top: 24rpx;
                display: flex;
                flex-wrap: wrap;
                max-height: 116rpx;
                overflow: hidden;
                > div {
                  margin-right: 16rpx;
                  padding: 0rpx 14rpx;
                  color: #666666;
                  font-size: 24rpx;
                  height: 42rpx;
                  line-height: 42rpx;
                  background: #e0ebff;
                  border-radius: 8rpx;
                  margin-bottom: 16rpx;
                }
              }
              .other-row {
                display: flex;
                align-items: center;
                justify-content: start;
                flex-wrap: wrap;
                color: #333333;
                font-size: 24rpx;
                > div:not(:first-child) {
                  margin-left: 30rpx;
                  position: relative;
                  &::before {
                    content: '';
                    width: 2rpx;
                    height: 20rpx;
                    background: #333333;
                    position: absolute;
                    left: -14rpx;
                    top: 50%;
                    transform: translateY(-50%);
                  }
                }
                .distance {
                }
                .time-bucket {
                }
                > span {
                  margin-left: 10rpx;
                }
                .time0 {
                  margin-left: 30rpx;
                  position: relative;
                  &::before {
                    content: '';
                    width: 2rpx;
                    height: 20rpx;
                    background: #333333;
                    position: absolute;
                    left: -14rpx;
                    top: 50%;
                    transform: translateY(-50%);
                  }
                }
                .phone {
                }
              }
            }
            .info-right {
              flex-shrink: 0;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-end;
              image {
                width: 64rpx;
                height: 64rpx;
                margin-bottom: 28rpx;
              }
              .status {
                font-size: 22rpx;
                height: 42rpx;
                line-height: 42rpx;
                padding: 0 12rpx;
                border-radius: 8rpx;
                &.color-green {
                  color: #30983d;
                  background: #f3f9f4;
                }
                &.color-gray {
                  color: #666666;
                  background: #f0f0f0;
                }
              }
            }
          }
        }
        .right-icon {
          margin-left: 20rpx;
          width: 36rpx;
          height: 40rpx;
        }
      }
      .more {
        margin-top: 30rpx;
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }
}

::v-deep .search-input-placeholder {
  font-size: 28rpx;
  color: #999999;
}
</style>
