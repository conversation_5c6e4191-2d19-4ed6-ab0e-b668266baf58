export default {
  state: {
    companyList: [],
    companySourceList: [],
    companyInfo: {
      enterpriseNo: NaN,
      enterpriseName: '',
      businessNo: NaN,
      businessType: NaN,
      entpNickName: '',
      roles: [],
      flag: NaN,
    },
    roleMap: {
      '2-10': 'managerEntpList',
      '2-11': 'managerEntpList',
      '4-10': 'driverEntpList',
    },
    accountInfoList: [], // 账户信息列表
    accountInfo: {
      enterpriseAccountNo: '',
      mainAccountNo: '',
      enterpriseNo: '',
      accountType: NaN,
      businessNo: '',
      businessType: NaN,
    },
    subEnterpriseList: [], // 下级企业列表
  },
  mutations: {
    setCompanyInfo(state, company) {
      // 找对企业对应账户
      if (state.accountInfoList?.length) {
        const accountInfo = state.accountInfoList.find(v => v.mainAccountNo === company?.mainAccountNo);
        this.commit('setAccountInfo', accountInfo);
      }
      Object.assign(state.companyInfo, company);
    },
    setCompanySourceList(state, list) {
      Object.assign(state.companySourceList, list);
    },
    setCompanyList(state, { data, orgCode, rootRole }) {
      const shortName = (name = '') => {
        if (!name?.length) return '';
        return name.substring(0, 1);
      };
      // const list = data[state.roleMap[rootRole?.code]];
      // v430改动
      const list = data.filter(v => {
        const roleCode = v.role + '-' + v.businessType;
        return rootRole?.code === roleCode;
      });
      if (!list?.length) {
        uni.showModal({
          content: `未找到对应角色数据`,
          showCancel: false,
        });
        return;
      }
      list.forEach(v => {
        v.shortName = shortName(v.enterpriseName);
        v.roleText = uni.$petro.Enum?.MEMBER_ROLES[v.role];
        v.bgColor = uni.$petro.Utils.randomColor();
      });
      Object.assign(state.companyList, list);
      this.commit(
        'setCompanyInfo',
        list.find(v => Number(v.enterpriseNo) === Number(orgCode) && Number(v.businessNo) === Number(rootRole?.businessNo)),
      );
    },
    setAccountInfoList(state, accountInfoList) {
      Object.assign(state.accountInfoList, accountInfoList);
    },
    setAccountInfo(state, accountInfo) {
      Object.assign(state.accountInfo, accountInfo);
    },
    setSubEnterprise(state, subEnterpriseList) {
      Object.assign(state.subEnterpriseList, subEnterpriseList);
    },
  },
  actions: {
    async getCompanyList({ commit, dispatch, state, rootState }, payload) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.queryType && payload?.queryType != 0) {
        return console.info('查询类型不能为空');
      }
      if (!payload?.refresh && state.companyList.length > 0) return state.companyList;
      try {
        const roles = rootState?.roles;
        const params = {
          // accountStatus: 1, // 账户状态 1:正常 2:冻结 3:注销 6:待激活  v430涛说不传
          // businessNo: roles?.role?.businessNo,  v430涛说不传
          queryType: payload?.queryType, // 0:企业 2:司机
          // memberRole: uni.$petro?.Enum?.MEMBER_ROLES_CODE[roles?.role?.code],
          enterpriseNo: orgCode,
        };
        const { success, data = [] } = await uni.$petro.http('user.business.queryEnterpriseBusinessList.h5', params, {
          mockResponse: {
            success: true,
            data: [
              {
                id: null,
                enterpriseAccountNo: '************',
                mainAccountNo: '************',
                enterpriseNo: '****************',
                enterpriseName: '长沙市岳麓区林雀子饭店',
                userId: '469',
                unitAlias: '长沙市岳麓区林雀子饭店',
                accountType: 1,
                businessNo: '****************',
                businessType: 10,
                invoiceType: 1,
                parentEnterpriseNo: null,
                parentEnterpriseName: null,
                parentContactName: null,
                parentMainAccountNo: null,
                inviteType: null,
                accountStatus: 1,
                role: 2,
                staffNo: '*****************',
              },
              {
                id: ************,
                enterpriseAccountNo: null,
                mainAccountNo: null,
                enterpriseNo: '****************',
                enterpriseName: '长沙市岳麓区林雀子饭店',
                userId: '469',
                unitAlias: null,
                accountType: null,
                businessNo: '****************',
                businessType: 10,
                invoiceType: 1,
                parentEnterpriseNo: '****************',
                parentEnterpriseName: '长沙市岳麓区林雀子饭店',
                parentContactName: null,
                parentMainAccountNo: '************',
                inviteType: 1,
                accountStatus: null,
                role: 4,
                staffNo: '*****************',
              },
            ],
            message: '请求成功',
            errorCode: null,
          },
        });
        if (success) {
          commit('setCompanySourceList', data);
          commit('setCompanyList', {
            data,
            orgCode,
            rootRole: roles?.role,
          });
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return [];
    },
    // 切换企业
    async companySwitch({ commit, dispatch, state, rootState }, payload) {
      const { accessToken, petroChinaNo } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      try {
        // 切换企业与角色
        if (!payload?.type) {
          payload.businessNo = rootState.roles?.role?.businessNo;
          payload.roleType = rootState.roles?.role?.roleType;
          payload.roleBusinessType = rootState.roles?.role?.roleBusinessType;
        }
        const params = {
          type: payload?.type || '',
          orgCode: payload?.enterpriseNo,
          businessNo: payload?.businessNo,
          roleType: payload?.roleType,
          roleBusinessType: payload?.roleBusinessType,
        };
        const { success, data } = await uni.$petro.http('user.companySwitch.h5', params);
        if (success && data) {
          await uni.$petro.setTokenInfo({ ...data, petroChinaNo });
          const res = await uni.$petro.http('user.business.getFleetType.h5', {
            businessNo: payload?.businessNo,
          });
          await uni.$petro.setTokenInfo(res.data);
          const app = getApp();
          await app?.hookInit();
          return data;
        }
      } catch (err) {
        console.error(err);
      }
    },
    async getAccountInfoList({ commit, dispatch, state, rootState }, payload) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.accountType) {
        return console.info('账户类型不能为空');
      }
      if (!payload?.refresh && state.accountInfoList.length > 0) return state.accountInfoList;
      try {
        const roles = rootState?.roles;
        const { success, data } = await uni.$petro.http(
          'account.user.queryMainAccountInfo.h5',
          {
            // businessNo: roles?.role?.businessNo,
            // v430需要
            accountType: payload?.accountType, // 1:企业 2:司机
            accountStatusList: [1, 2], // 账户状态 1:正常 2:冻结 3:注销 6:待激活
            enterpriseNo: orgCode,
          },
          {
            mockResponse: {
              success: true,
              data: [
                {
                  enterpriseAccountNo: '************',
                  mainAccountNo: '************',
                  enterpriseStaffNo: '*****************',
                  checkLicensePlate: '0',
                  enterpriseNo: '****************',
                  accountType: 1,
                  businessNo: '****************',
                  businessType: 10,
                  licencePlate: '',
                  accountStatus: 1,
                  petroChinaNo: '**************',
                },
              ],
              message: '请求成功',
              errorCode: null,
            },
          },
        );
        if (success) {
          commit('setAccountInfoList', data);
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return {};
    },
    async getSubEnterprise({ commit, dispatch, state, rootState }, payload) {
      console.log(235);

      const { accessToken } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      console.log(245);
      try {
        const { success, data } = await uni.$petro.http(
          'account.manager.querySubEnterpriseAccount.h5',
          {
            enterpriseAccountNo: state.accountInfo?.enterpriseAccountNo,
            businessNo: state.accountInfo?.businessNo,
          },
          {
            mockResponse: {
              success: true,
              data: [
                {
                  enterpriseAccountNo: '************',
                  mainAccountNo: '************',
                  enterpriseNo: '****************',
                  userId: '469',
                  userName: '雷天伟',
                  licencePlate: null,
                  unitAlias: '长沙市岳麓区林雀子饭店',
                  accountType: 1,
                  businessNo: '****************',
                  businessType: 10,
                  invoiceType: 1,
                  accountStatus: 1,
                  present: true,
                  accountPlace: null,
                  accountPlaceName: null,
                  remark: null,
                  enterpriseName: '长沙市岳麓区林雀子饭店',
                  petroChinaNo: '**************',
                },
              ],
              message: '请求成功',
              errorCode: null,
            },
          },
        );
        if (success) {
          commit('setSubEnterprise', data);
          return data;
        }
      } catch (err) {
        console.error(err);
      }
      return {};
    },
  },
  getters: {},
};
