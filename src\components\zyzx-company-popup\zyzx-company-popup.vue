<template>
  <div class="zyzx-company-popup">
    <u-popup :show="showPopup" mode="bottom" bgColor="#fff" @close="close" :round="16" :safeAreaInsetBottom="true">
      <div class="company-popup">
        <div class="close-wrap">
          <div></div>
          <div>切换组织</div>
          <u-icon name="close" color="#333333" size="18" @click="close"></u-icon>
        </div>
        <div class="company-list">
          <div class="company-item-wrap" v-for="(item, i) in companyList" :key="i" @click="onSelect(item)">
            <div
              class="company-item"
              :class="{ active: item.enterpriseNo == companyInfo.enterpriseNo && item.businessNo == role.businessNo }"
              v-if="item.accountStatus == 1"
            >
              <div class="left">
                <u-avatar :text="item.shortName" :size="40" fontSize="18" :bg-color="item.bgColor"></u-avatar>
              </div>
              <div class="right">
                <div class="name">
                  <span>{{ item.enterpriseName }}</span>
                  <img src="./images/icon-auth.png" />
                </div>
                <div class="remark" v-if="item.unitAlias">{{ item.unitAlias }}</div>
                <div class="tags" v-if="item.roleText">
                  <span>{{ item.roleText }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="company-item-wrap">
            <div class="company-item" v-if="isAdd" @click="add()">
              <div class="left">
                <u-avatar icon="plus" :size="40" fontSize="18"></u-avatar>
              </div>
              <div class="right">
                <div class="name">加入新的企业/组织 </div>
                <div class="remark">用于新建或补充组织信息</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </u-popup>
  </div>
</template>

<style lang="scss" scoped>
.zyzx-company-popup {
  width: 100%;

  .company-popup {
    padding: 50rpx 32rpx 32rpx;

    .close-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
      }
    }

    .company-list {
      margin-top: 72rpx;
      display: flex;
      flex-direction: column;

      .company-item-wrap {
        &:not(:first-child) {
          .company-item {
            margin-top: 16rpx;
          }
        }
        .company-item {
          padding: 28rpx 32rpx;
          display: flex;
          box-sizing: border-box;
          border-radius: 16rpx;
          border: 2rpx solid #fff;
          overflow: hidden;

          &.active {
            border: 2rpx solid #fa1919;
          }

          .left {
            flex-shrink: 0;
          }

          .right {
            flex: 1;
            margin-left: 24rpx;
            overflow: hidden;

            .name {
              height: 44rpx;
              font-weight: bold;
              font-size: 32rpx;
              color: #333333;
              line-height: 38rpx;
              display: flex;
              align-items: center;
              margin-bottom: 4rpx;
              span {
                color: #333333;
                margin-right: 16rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              img {
                flex-shrink: 0;
                width: 32rpx;
                height: 32rpx;
              }
            }

            .remark {
              height: 34rpx;
              font-size: 24rpx;
              color: #666666;
              line-height: 28rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              margin-bottom: 8rpx;
            }

            .tags {
              display: flex;

              span {
                padding: 0 12rpx;
                height: 42rpx;
                line-height: 42rpx;
                background: #e0ebff;
                border-radius: 8rpx;
                font-size: 24rpx;
                color: #666666;
                text-align: center;

                &:not(:first-child) {
                  margin-left: 16rpx;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>

<script>
import { mapState } from 'vuex';

export default {
  name: 'zyzx-company-popup',
  components: {},
  props: {
    isAdd: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showPopup: false,
    };
  },
  computed: {
    ...mapState({
      companyList: state => state?.company?.companyList,
      companyInfo: state => state?.company?.companyInfo,
      role: state => state?.roles?.role,
    }),
  },
  watch: {},
  mounted() {},
  methods: {
    show() {
      this.showPopup = true;
    },
    close() {
      this.showPopup = false;
    },
    add() {
      this.close();
      this.$emit('add');
    },
    async onSelect(v) {
      // 如果当前企业与选中企业相同，则不切换
      if (v.enterpriseNo == this.companyInfo.enterpriseNo && v.businessNo == this.role.businessNo) return;
      this.close();
      // 正常应该是:如果是同一企业，则同时切角色type传1，否则不变
      // 出现了切企业但是另一个企业默认角色不是当前端，所以直接默认一起切换角色
      v.type = '1';
      v.roleType = this.$store?.state?.roles?.role?.roleType;
      v.roleBusinessType = this.$store?.state?.roles?.role?.roleBusinessType;
      const selectCompanyInfo = await this.$store.dispatch('companySwitch', v);
      if (selectCompanyInfo) {
        this.$emit('onSelect', selectCompanyInfo);
      }
    },
  },
};
</script>
