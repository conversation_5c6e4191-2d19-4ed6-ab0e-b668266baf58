/**
 * @description: 积分支付协议
 * @param {*} accountCenterParams.orderNo 订单号
 * @param {*} accountCenterParams.amount  金额
 * @return {*}
 */
export const confirmPayOrdersBridge = params => {
  return new Promise((resolve, reject) => {
    try {
      const jsonData = {
        bizType: 'accountCenter',
        method: 'confirmPayOrders',
        data: {
          accountCenterParams: params,
        },
      };
      uni.$petro.Bridge.acc.handleCenter(jsonData, res => {
        resolve(res);
      });
    } catch (error) {
      reject(error)
    }
  });
};
