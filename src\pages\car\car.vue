<template>
  <div class="page-container">
    <u-navbar :title="'我的车辆'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div class="page-car">
          <div class="car-list" v-if="list && list.length">
            <div class="car-item" v-for="(item, index) in list" :key="index">
              <div class="car-item-left" @click="onSelect(item)">
                <div class="car-item-left-num">{{ item.licensePlate }}</div>
                <div class="car-item-left-type">{{ CAR_TYPE[item.vehicleType] }}</div>
                <div class="car-item-left-tag">{{ FILL_TYPE[item.fillType] }}</div>
              </div>
              <div class="car-item-right">
                <u-icon name="arrow-right" color="#666666" size="16" @click="toDetail(item)"></u-icon>
              </div>
            </div>
          </div>
          <u-empty
            v-else
            :icon="require('@/static/images/empty-order-list.png')"
            marginTop="200"
            textSize="12"
            textColor="#999999"
            width="174"
            height="119"
            text="未查询到车辆"
          >
          </u-empty>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex';
import { CAR_TYPE, FILL_TYPE } from '@/services/enum';

export default {
  components: {},
  data() {
    return {
      CAR_TYPE: CAR_TYPE,
      FILL_TYPE: FILL_TYPE,
      list: [],
    };
  },
  computed: {},
  async onLoad(query) {
    this.list = await this.$store.dispatch('getCarList', false);
  },
  methods: {
    onSelect(item) {
      this.$store.commit('setCarInfo', item);
      uni.$petro.route({
        type: 'back',
      });
    },
    toDetail(item) {
      uni.$petro.route({ url: '/pages/car/pages/car-details-page', type: 'navigateTo', params: item }, true);
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
  .page-content {
    flex: 1;
  }
  .page-car {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .car-list {
      padding: 0 32rpx;
      margin-top: 40rpx;
      .car-item {
        background: #ffffff;
        display: flex;
        align-items: center;
        height: 108rpx;
        padding: 0 32rpx;
        border-radius: 16rpx;
        &:not(:first-child) {
          margin-top: 24rpx;
        }
        &-left {
          flex: 1;
          display: flex;
          align-items: center;
          &-num {
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            line-height: 44rpx;
          }
          &-type {
            margin-left: 16rpx;
            padding: 0 12rpx;
            height: 38rpx;
            background: #fff1eb;
            border-radius: 4rpx;
            border: 1rpx solid #ff7033;
            font-size: 24rpx;
            color: #ff7033;
            line-height: 38rpx;
            box-sizing: border-box;
          }
          &-tag {
            margin-left: 16rpx;
            padding: 0 12rpx;
            height: 38rpx;
            background: #f6fffe;
            border-radius: 4rpx;
            border: 1rpx solid #1bc3b9;
            font-size: 24rpx;
            color: #1bc3b9;
            line-height: 38rpx;
            box-sizing: border-box;
          }
        }
        &-right {
          flex-shrink: 0;
          padding: 20rpx 0 20rpx 60rpx;
        }
      }
    }
  }
}
</style>
