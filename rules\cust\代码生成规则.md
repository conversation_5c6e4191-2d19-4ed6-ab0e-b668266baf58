# AI工具使用规范 - 司机端小程序开发

## 🎯 项目技术栈识别
- **项目名称**: petro-soti-zyzx-miniapp-driver (司机端小程序)
- **版本**: 1.1.9
- **框架**: UniApp + Vue 2.6.14 (Options API)
- **UI组件库**: uView UI 2.0.36 + 自定义组件库 @petro-soti/foudation-zyzx ^1.2.0-1090
- **开发语言**: 原生JavaScript (严禁使用TypeScript)
- **状态管理**: Vuex 3.6.2
- **构建工具**: Vue CLI 5.0 + UniApp CLI 2.0.2
- **样式**: SCSS + CSS3 (sass ^1.70.0, sass-loader 10)
- **平台支持**: 微信小程序、支付宝小程序、mPaaS小程序、H5、App Plus
- **HTTP库**: flyio ^0.6.2 (通过uni.$petro.http封装)
- **调试工具**: vconsole ^3.15.1

## 🔧 AI工具核心约束

### 强制性技术约束
1. **框架版本**: 必须使用Vue 2.6.14 + Options API，严禁使用Vue 3或Composition API
2. **HTTP请求**: 必须使用 `uni.$petro.http()` 方法，不得使用其他HTTP库
3. **组件库**: 优先使用uView UI组件，其次使用petro/zyzx前缀的自定义组件
4. **状态管理**: 必须使用Vuex 3.6.2，不得使用Pinia或其他状态管理库
5. **样式语言**: 使用SCSS，不得使用CSS-in-JS或styled-components

### 项目结构识别
- **API文件**: `src/services/http/` 目录 (模块化API文件)
  - `api.js` - 通用API
  - `account.js` - 账户相关API
  - `car.js` - 车辆相关API
  - `station.js` - 加油站相关API
  - `order.js` - 订单相关API
  - `notices.js` - 通知相关API
- **组件目录**: `src/components/` (p-前缀命名) + `src/mycomponents/` (自定义组件)
- **页面目录**: `src/pages/`
- **状态管理**: `src/services/store/` (模块化Vuex store)
- **Mock数据**: 内联在API函数中的mockResponse配置
- **配置文件**: `src/config.index.local.js` (环境配置)

## 🔧 API开发规范

### 实际API结构模式
基于现有代码，API函数必须遵循以下模式：

```javascript
// 正确的API函数结构 (参考 src/services/http/api.js)
export function getCompanyListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      onEntpList: [
        {
          enterpriseName: '企业名称1',
          entpNickName: '企业别名1',
          staffRole: 1,
        },
      ],
      offEntpList: [],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('user.driver.queryCompanyByDriver.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
```

### Mock数据集成
- **Mock数据内联**: Mock数据直接在API函数内部定义，不使用独立的mock.js文件
- **每个API函数必须配置mockResponse**: 在uni.$petro.http的第三个参数中配置
- **Mock数据结构**: 必须包含success、data、message、errorCode字段
- **环境控制**: Mock数据仅在dev/sit环境生效，通过config.index.local.js控制

### HTTP请求约定
- **统一请求方法**: 使用uni.$petro.http()，基于flyio封装
- **接口命名规范**: 采用模块.动作.平台格式
  - 示例: `'user.driver.queryCompanyByDriver.h5'`
  - 示例: `'oilstation.station.list.h5'`
  - 示例: `'account.driver.queryWalletList.h5'`
- **请求配置**: 支持showLoading、ignoreErrs等配置项
- **平台适配**: 支持h5、mp-weixin、mp-alipay等多平台

## 🎨 Vue组件开发规范

### 组件结构约束
- **组件位置**:
  - `src/components/` - 页面级组件 (如: p-test, page-home)
  - `src/mycomponents/` - 自定义功能组件 (如: keyboard, security-plugin)
- **命名规范**:
  - p-前缀 + 功能名称 (如: p-test, p-map)
  - page-前缀 + 页面名称 (如: page-home, page-order)
- **文件结构**: 单文件组件(.vue)，包含template、script、style三部分
- **组件注册**: 通过pages.json的easycom自动注册

### Vue 2 Options API规范
```javascript
// 正确的组件结构 (参考 src/components/p-test/p-test.vue)
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'p-test',
  props: {
    data: String,
  },
  data() {
    return {};
  },
  computed: {
    // 使用mapState辅助函数
    ...mapState({
      test1Count: state => state.test1.count,
    }),
    // 直接访问store
    test2Count() {
      return this.$store.state.test2.count;
    },
  },
  methods: {},
  mounted() {},
};
```

### 状态管理集成
- **Vuex模块化**: 状态模块位于 `src/services/store/` 目录
  - `roles.js` - 用户角色状态
  - `accountDriver.js` - 司机账户状态
  - `car.js` - 车辆状态
  - `station.js` - 加油站状态
  - `company.js` - 企业状态
- **辅助函数**: 使用mapState、mapGetters、mapActions等
- **访问方式**: 组件中通过 `this.$store` 直接访问状态

### 样式规范
```vue
<style scoped lang="scss">
// 使用SCSS语法
// 必须添加scoped属性避免样式污染
// 可以使用src/uni.scss中的全局样式变量
</style>
```

### 组件库使用规范
- **uView UI**: 通过 `u-` 前缀使用 (如: u-button, u-input)
- **Petro组件**: 通过 `petro-` 前缀使用 (如: petro-layout)
- **Zyzx组件**: 通过 `zyzx-` 前缀使用
- **自动注册**: 通过pages.json的easycom配置自动注册

## 🔄 AI工具开发流程

### API开发流程
1. **分析需求**: 确定API功能和数据结构
2. **选择模块**: 根据功能选择合适的API文件
   - 通用功能 → `src/services/http/api.js`
   - 账户相关 → `src/services/http/account.js`
   - 车辆相关 → `src/services/http/car.js`
   - 加油站相关 → `src/services/http/station.js`
   - 订单相关 → `src/services/http/order.js`
3. **添加API函数**: 按照标准模式编写API函数
4. **配置Mock数据**: 在函数内部定义mockResponse
5. **集成调用**: 在组件或store中使用API函数
6. **测试验证**: 确保Mock数据和接口调用正常

### 组件开发流程
1. **确定组件类型**:
   - 页面级组件 → `src/components/page-*`
   - 功能组件 → `src/components/p-*`
   - 自定义组件 → `src/mycomponents/`
2. **创建组件**: 按照命名规范创建.vue文件
3. **使用Options API**: 严格按照Vue 2 Options API结构编写
4. **集成UI组件**: 优先使用uView UI，其次使用petro/zyzx组件
5. **状态管理**: 通过Vuex模块管理组件状态
6. **样式编写**: 使用SCSS + scoped样式
7. **组件注册**: 通过easycom自动注册或手动注册

### 状态管理开发流程
1. **确定模块**: 根据业务功能选择或创建Vuex模块
2. **定义状态**: 在模块中定义state、mutations、actions
3. **API集成**: 在actions中调用API函数
4. **组件集成**: 使用mapState、mapActions等辅助函数

## 🚫 AI工具使用限制

### 严格禁止的操作
- **启动项目**: 禁止使用 `npm run dev` 等启动命令
- **框架混用**: 禁止使用Vue 3语法或Composition API
- **依赖变更**: 禁止修改package.json或安装新依赖
- **配置修改**: 禁止修改src/config.index.local.js等配置文件
- **测试数据**: 禁止在组件中硬编码测试数据
- **深度嵌套**: 组件嵌套不超过3层
- **直接修改**: 禁止直接修改node_modules中的依赖

### 代码质量约束
- 单个方法不超过50行代码
- 必须使用描述性的变量和函数名
- 复杂逻辑必须添加注释
- 遵循DRY原则，避免代码重复
- API函数必须包含内联Mock数据
- 组件必须使用scoped样式

### 环境和构建约束
- **开发环境**: 当前配置为sit环境 (env: 'sit')
- **客户端代码**: 渠道号为C15 (clientCode: 'C15')
- **版本管理**: 当前版本1.1.9，禁止修改版本号
- **构建平台**: 支持多平台构建，但需遵循平台特定配置

## 📋 AI工具检查清单

### 代码生成前检查
- [ ] 确认使用Vue 2.6.14 + Options API
- [ ] 确认使用uni.$petro.http()进行API调用
- [ ] 确认组件使用正确的命名规范 (p-*, page-*)
- [ ] 确认使用uView UI 2.0.36组件库
- [ ] 确认使用Vuex 3.6.2进行状态管理
- [ ] 确认API函数包含内联mockResponse
- [ ] 确认遵循模块化API文件结构

### 代码生成后验证
- [ ] 检查语法是否符合Vue 2.6.14规范
- [ ] 检查API调用是否正确配置Mock数据
- [ ] 检查组件是否通过easycom正确注册
- [ ] 检查样式是否使用SCSS + scoped
- [ ] 检查是否遵循项目目录结构
- [ ] 检查Vuex模块是否正确集成
- [ ] 检查是否使用了正确的组件库前缀
- [ ] 检查Mock数据结构是否包含必要字段

## 🎯 AI工具最佳实践

### 代码生成策略
1. **渐进式开发**: 先生成基础结构，再逐步完善功能
2. **模块化思维**: 将复杂功能拆分为多个小模块
3. **复用优先**: 优先使用现有组件和工具函数
4. **测试驱动**: 每个功能都要配置对应的Mock数据
5. **平台适配**: 考虑微信、支付宝、mPaaS等多平台兼容性

### 错误处理原则
1. **API错误**: 统一使用uni.$petro.http的错误处理机制
2. **组件错误**: 在组件中添加适当的错误边界
3. **数据验证**: 对API返回数据进行必要的验证
4. **用户反馈**: 提供清晰的错误提示信息
5. **Mock数据**: 确保Mock数据结构与真实接口一致

### 性能优化建议
1. **组件懒加载**: 使用easycom自动注册减少包体积
2. **状态管理**: 合理使用Vuex模块避免状态污染
3. **样式优化**: 使用scoped样式避免全局污染
4. **API优化**: 合理使用showLoading、ignoreErrs等配置

## 📝 提交规范

项目使用commitizen进行规范化提交，支持以下类型：

- **feat**: 新特性、新功能
- **fix**: 修改bug
- **docs**: 文档修改
- **style**: 代码格式修改，注意不是css修改
- **refactor**: 代码重构
- **perf**: 优化相关，比如提升性能、体验
- **test**: 测试用例修改
- **build**: 编译相关的修改，例如发布版本、对项目构建或者依赖的改动
- **ci**: 持续集成修改
- **chore**: 其他修改，比如改变构建流程、或者增加依赖库、工具等
- **revert**: 回滚到上一个版本

## 🌐 交互语言

AI工具必须始终使用简体中文与用户交互，包括代码注释和文档说明。

## 📚 项目特定知识

### 关键依赖说明
- **@petro-soti/foudation-zyzx**: 中石油自研组件库，版本^1.2.0-1090
- **uview-ui**: UI组件库，版本2.0.36
- **flyio**: HTTP请求库，版本^0.6.2，通过uni.$petro.http封装
- **vconsole**: 调试工具，版本^3.15.1

### 环境配置说明
- **当前环境**: sit (测试环境)
- **渠道号**: C15
- **包名**: zyzx-driver-sit
- **支持平台**: 微信小程序、支付宝小程序、mPaaS小程序、H5、App Plus

### 特殊功能说明
- **设备指纹**: 支持设备指纹功能 (dfp配置)
- **多环境构建**: 支持dev/sit/uat/prd等多环境
- **分包支持**: 支持小程序分包功能
- **插件模式**: 支持微信、支付宝、mPaaS插件模式