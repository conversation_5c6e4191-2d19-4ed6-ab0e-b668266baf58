import { getStationListApi, getFuelGunByOrgCodeApi, getDistanceByStationCodeApi } from '@/services/http';
import { formatDistanceToTwoDecimal } from '@/utils';

export default {
  state: {
    stationList: [],
    stationInfo: {},
    // 获取该网点的油品编码集合。
    fuelData: [],
    // 地图中心纬度(选中油站)(地图中心点)
    mapCenterLat: '',
    // 地图中心经度(选中油站)(地图中心点)
    mapCenterLon: '',
  },
  mutations: {
    setStationList(state, list) {
      state.stationList = list;
    },
    setStationInfo(state, value) {
      state.stationInfo = value;
    },
    // 设置油品信息集合包括油品油枪
    setFuelData(state, list) {
      state.fuelData = list;
    },
    // 设置地图定位信息
    setLocation(state, locationObj) {
      state.mapCenterLat = '';
      state.mapCenterLat = '';
      setTimeout(() => {
        state.mapCenterLat = locationObj.latitude;
        state.mapCenterLon = locationObj.longitude;
      }, 0);
    },
  },
  actions: {
    // 获取油站列表
    async getStationList({ commit, dispatch, state, rootState }, payload) {
      const { accessToken } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      try {
        let location = {};
        if (payload?.latitude && payload?.longitude) {
          location = payload;
        } else {
          location = await uni.$petro.getLocation({}, true, {
            showModal: false,
          });
        }
        // TODO 北京昌平石油基地
        // location = {
        //   latitude: '40.16673979857886',
        //   longitude: '116.2376462404155',
        // };
        const params = {
          longitude: location.longitude || uni.$petro.config.defaultGPS.longitude,
          latitude: location.latitude || uni.$petro.config.defaultGPS.latitude,
          destinationLatitude: '',
          destinationLongitude: '',
          // bookingRefueling: '1',
          distance: 50,
          pageSize: 10,
          pageNum: 1,
          mapType: 0, //地图坐标系标识（0高德，1百度，2腾讯）
        };
        const { success, data } = await getStationListApi(params);
        if (!success) return [];
        if (data.rows.length) {
          data.rows.forEach(item => {
            item.distanceInKm = formatDistanceToTwoDecimal(item.distance || 0);
          });
          // 最近的油站打个标注
          data.rows[0].isFirst = true;
          commit('setStationList', data.rows);
          commit('setStationInfo', data.rows[0]);
          commit('setLocation', {
            latitude: data.rows[0]?.latitude,
            longitude: data.rows[0]?.longitude,
          });
          return data.rows;
        } else {
          commit('setLocation', {
            latitude: location.latitude,
            longitude: location.longitude,
          });
        }
      } catch (error) {
        console.log(error);
      }
      return [];
    },

    // 获取该网点的油品编码集合接口
    async getFuelGunByOrgCodePost({ state, commit, dispatch, rootState }, value) {
      const { accessToken } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (state.stationInfo.hosCode) {
        // 获取油品集合/oilstation /station/getFuelGunByOrgCode接口]
        const resData = await getFuelGunByOrgCodeApi({
          orgCode: state.stationInfo.orgCode,
          onlineType: state.stationInfo.stationType == 1 ? '1' : '0',
          hosCode: state.stationInfo.hosCode || '',
        });
        if (resData.success) {
          let dataArr = resData.data;
          commit('setFuelData', dataArr);
        }
      } else {
        // 如果没网点列表没配置hosCode，就把油品数据置空
        commit('setFuelData', []);
      }
    },

    // 获取当前位置到油站距离
    async getDistanceByStationCode({ state, commit, dispatch, rootState }, payload = {}) {
      const { accessToken } = await dispatch('getTokenInfo');
      if (!accessToken) {
        return console.info('用户未登录');
      }
      try {
        let location = {};
        if (payload.location?.latitude && payload.location?.longitude) {
          location = payload.location;
        } else {
          location = await uni.$petro.getLocation({}, true, {
            showModal: false,
          });
        }
        // TODO 北京昌平石油基地
        // location = {
        //   latitude: '40.16673979857886',
        //   longitude: '116.2376462404155',
        // };
        const params = {
          longitude: location?.longitude,
          latitude: location?.latitude,
          stationCode: payload?.stationCode,
          mapType: 0, //地图坐标系标识（0高德，1百度，2腾讯）
        };
        const { success, data } = await getDistanceByStationCodeApi(params);
        if (success && data?.stationCode) {
          // 将km转换为m，并转换为整数
          data.distanceInM = Math.floor(Number(data?.distance || 0) * 1000);
          data.distanceInKm = formatDistanceToTwoDecimal(data?.distance || 0);
          return data;
        }
      } catch (error) {
        console.log(error);
      }
      return null;
    },
  },
  getters: {},
};
