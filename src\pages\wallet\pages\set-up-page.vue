<template>
  <div class="page-setup">
    <u-navbar title="钱包设置" :placeholder="true" :autoBack="true"> </u-navbar>

    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-wallet-setup :list="list"/>
    </petro-layout>
  </div>
</template>

<script>
// import zyzxPageWalletSetup from '@/components/zyzx-page-wallet-setup/zyzx-page-wallet-setup.vue';

export default {
  data() {
    return {
      list: [
        {
          title: '修改支付密码',
          arrow: true,
          link: '/pages/wallet/pages/face-recognition-page',
        },
        {
          title: '单位限制查询',
          arrow: true,
          link: '/pages/wallet/pages/limit-list-page',
        },
      ],
    };
  },
  // components: {
  //   zyzxPageWalletSetup,
  // },
  onShow() {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.page-setup {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
</style>
