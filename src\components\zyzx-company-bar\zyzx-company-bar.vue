<template>
  <div class="zyzx-company-bar" :class="{ 'is-ide': isIDE }">
    <div class="zyzx-company-bar__placeholder" :style="{ height: systemInfo.statusBarHeight }"></div>
    <div class="zyzx-company-bar__header">
      <div class="zyzx-company-bar__info" :style="{ height: systemInfo.titleBarHeight }">
        <u-avatar :text="companyInfo.shortName" :size="30" :fontSize="16" :bg-color="companyInfo.bgColor"></u-avatar>
        <div class="zyzx-company-bar__name text-overflow" @click="onCompanyPopupShow">
          <span class="zyzx-company-bar__label text-overflow" :style="{ color: color }">{{ companyInfo.enterpriseName }}</span>
          <u-icon name="arrow-down" :color="color" size="14"></u-icon>
        </div>
      </div>
      <div class="zyzx-company-bar__icon">
        <slot>
          <slot name="menu">
            <div id="menu" class="menu" @click="onMenu" v-if="menu">
              <u-icon name="plus-circle" :color="color" size="28"></u-icon>
            </div>
          </slot>
          <slot name="todo">
            <div class="todo" @click="onTodo" v-if="todo">
              <u-icon name="calendar" :color="color" size="28"></u-icon>
            </div>
          </slot>
          <slot name="scan">
            <div class="scan" @click="onScan" v-if="scan">
              <u-icon name="scan" :color="color" size="28"></u-icon>
            </div>
          </slot>
        </slot>
      </div>
    </div>
    <root-portal :enable="true">
      <zyzx-company-popup ref="companyPopup" :isAdd="isAdd" @onSelect="onSelect" @add="onAdd"></zyzx-company-popup>
    </root-portal>
    <root-portal :enable="true">
      <u-overlay :show="menuShow" @click="menuShow = false" :opacity="0.3"></u-overlay>
      <div class="company-menu-list" v-show="menuShow" :style="{ right: menuInfo.x, top: menuInfo.y }">
        <div class="company-menu-li" v-for="(v, k) in menuList" @click="onMenuItemSelect(v)">
          <img class="company-menu-li__icon" v-if="v.icon" :src="v.icon" alt="" />
          <span class="company-menu-li__text">{{ v.text }}</span>
        </div>
      </div>
    </root-portal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import ZyzxCompanyPopup from '../zyzx-company-popup/zyzx-company-popup.vue';

export default {
  name: 'company-bar',
  components: { ZyzxCompanyPopup },
  props: {
    color: {
      type: String,
      default: '#fff',
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
    scan: {
      type: Boolean,
      default: false,
    },
    todo: {
      type: Boolean,
      default: false,
    },
    menu: {
      type: Boolean,
      default: false,
    },
    menuList: {
      type: Array,
      default: [
        // {
        //   type: 'route',
        //   icon: '',
        //   text: '业务开通',
        //   url: '',
        // },
        // {
        //   type: 'route',
        //   icon: '',
        //   text: '待办事项',
        //   url: '',
        // },
        {
          type: 'scan',
          icon: '',
          text: '扫一扫',
        },
        // {
        //   type: 'route',
        //   icon: '',
        //   text: '邀请下级企业',
        //   url: '',
        // },
        // {
        //   type: 'route',
        //   icon: '',
        //   text: '邀请司机',
        //   url: '',
        // },
      ],
    },
  },
  data() {
    return {
      systemInfo: uni.$petro.store?.systemInfo,
      menuShow: false,
      menuInfo: {
        x: '0px',
        y: '0px',
      },
    };
  },
  computed: {
    ...mapState({
      companyInfo: state => state?.company?.companyInfo,
    }),
    isIDE() {
      return my.isIDE;
    },
  },
  mounted() {
    this.setMenuXY();
  },
  methods: {
    setMenuXY() {
      const query = uni.createSelectorQuery().in(this);
      query
        .select('#menu')
        .boundingClientRect(data => {
          this.menuInfo.x = `calc(100vw - ${data.left}px - ${data.width}px - 10px)`;
          this.menuInfo.y = `calc(${data.top}px + ${data.height}px + 10px)`;
        })
        .exec();
    },
    onCompanyPopupShow() {
      this.$refs.companyPopup.show();
    },
    onSelect(v) {
      this.$emit('onSelect', v);
    },
    onAdd() {
      this.$emit('onAdd');
    },
    // 菜单
    async onMenu() {
      this.menuShow = true;
      this.$emit('onMenu');
    },
    // 菜单选择
    async onMenuItemSelect(v) {
      this.menuShow = false;
      switch (v.type) {
        case 'scan':
          this.onScan();
          break;
        case 'route':
          uni.$petro.route({ url: v.url, params: v.data, type: 'navigateTo' }, true);
          break;
        case 'custom':
          this.$emit('onMenuItemSelect', v);
          break;
      }
    },
    // 待办
    async onTodo() {
      this.$emit('onTodo');
    },
    // 扫码
    async onScan() {
      const res = await uni.$petro.scan({
        type: 4,
      });
      if (res?.success && res?.type === 'other') {
        console.log('onScan', res?.data?.authInfo);
        uni.showModal({
          title: '温馨提示',
          content: '实人认证成功',
          showCancel: false,
          success: ss => {
            if (ss?.confirm) {
              this.$emit('onScan', res);
            }
          },
        });
        return;
      }
      this.$emit('onScan', res);
    },
  },
};
</script>

<style lang="scss" scoped>
.zyzx-company-bar {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 0 24rpx;
  box-sizing: border-box;

  &.is-ide {
    padding-right: calc(240rpx + 24rpx);
  }

  &__header,
  &__icon {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
    cursor: pointer;
    z-index: 11;

    &:active {
      opacity: 0.8;
    }
  }

  &__info {
    box-sizing: content-box !important;
    display: flex;
    align-items: center;

    &:first-child {
      flex: 1;
    }
  }

  &__name {
    display: flex;
    align-items: center;
    max-width: 13em;
  }

  &__label {
    margin-left: 10rpx;
    color: #ffffff;
    font-size: 32rpx;
    margin-right: 10rpx;
  }
}

.company-menu-list {
  position: fixed;
  right: 100%;
  top: -100%;
  z-index: 10080;
  background-color: #fff;
  border-radius: 16rpx;

  &:before {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 16rpx solid transparent;
    border-right: 16rpx solid transparent;
    border-bottom: 10rpx solid #fff;
    right: 30rpx;
    top: -8rpx;
  }

  .company-menu-li {
    position: relative;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;

    &__icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 16rpx;
    }

    &__text {
      font-size: 28rpx;
      color: #333;
    }

    &:not(:last-child):after {
      position: absolute;
      content: '';
      left: 48rpx;
      right: 48rpx;
      bottom: 0;
      display: inline-block;
      height: 1rpx;
      background-color: #eee;
    }
  }
}

// 单行文本溢出显示省略号
.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
