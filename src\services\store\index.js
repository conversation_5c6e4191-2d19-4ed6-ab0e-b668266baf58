import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

import test1 from '@/services/store/test1';
import test2 from '@/services/store/test2';

// 油站相关
import station from '@/services/store/station';
// 车辆相关
import car from '@/services/store/car';
// 账户相关-公共
import account from '@/services/store/account';
// 企业相关
import company from '@/services/store/company';
// tabbar
import tabBar from '@/services/store/tabBar';
// 账户相关-司机
import accountDriver from '@/services/store/accountDriver';
// 角色
import roles from '@/services/store/roles';

const store = new Vuex.Store({
  modules: {
    ...uni.$petro.store.store,
    test1,
    test2,
    station,
    car,
    account,
    company,
    tabBar,
    accountDriver,
    roles,
  },
});

export default store;
